import { DirectiveBinding } from "vue";

// 权限指令
export const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 开发环境下不进行权限控制
    if (import.meta.env.DEV) {
      return;
    }

    // 获取指令绑定的权限值
    const { value } = binding;
    // 从存储中获取用户权限列表
    const permissions = JSON.parse(localStorage.getItem("permissions") || "[]");

    // 将输入转换为数组形式
    const requiredPermissions = Array.isArray(value) ? value : [value];

    // 判断用户是否具有权限
    const hasPermission = permissions.some((permission: string) =>
      requiredPermissions.includes(permission)
    );

    // 如果没有权限，则移除该元素
    if (!hasPermission) {
      if (el.parentNode) {
        el.parentNode.removeChild(el);
      }
    }
  },
};

// 注册指令的方法
export const setupPermissionDirective = (app: any) => {
  app.directive("has", permission);
};
