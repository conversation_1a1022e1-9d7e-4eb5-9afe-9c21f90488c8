import request from "./request";

/**
 * 分页带条件查询定制出行线路
 */
export const postTravelLine = (params: any) => {
  return request.post("/sc/travelLine/pageTravelLine", params);
};

/**
 * 查询定制出行线路详情
 */
export const getTravelLineDetail = (params: any) => {
  return request.get(`/sc/travelLine/travelLineDetail/${params.travelLineId}`);
};

/**
 * 删除定制出行线路
 */
export const deleteTravelLine = (params: any) => {
  return request.get(`/sc/travelLine/deleteTravelLine/${params.travelLineId}`);
};

/**
 * 获取订单详情
 */
export const getOrderDetais = (params: any) => {
  return request.get(`/sc/order/getOrderItem/${params.orderId}`);
};

/**
 * 关闭订单
 */
export const getCloseOrder = (params: any) => {
  return request.get(`/sc/order/cancelOrder`, params);
};

/**
 * 订单到达
 */
export const postArriveOrder = (params: any) => {
  return request.post(`/sc/order/arriveOrder`, params);
};

/**
 * 查询定制线路列表
 */
export const getTravelLineList = (params: any) => {
  return request.get("/sc/travelLine/getTravelLineList", params);
};

/**
 * 支付订单
 */
export const postPayOrder = (params: any) => {
  return request.get("/sc/order/payOrder", params);
};

/**
 * 根据该线路获取当天的班次
 */
export const getTravelLineSchedule = (params: any) => {
  return request.post("/sc/order/getSchedulingByTravelLineId", params);
};

/**
 * 确认订单
 */
export const postConfirmOrder = (params: any) => {
  return request.post("/sc/order/confirmOrder", params);
};

/**
 * 派车
 */
export const postDispatchOrder = (params: any) => {
  return request.post("/sc/order/dispatchOrder", params);
};

/**
 * 后台获取订单列表
 */
export const getOrderList = (params: any) => {
  return request.post("/sc/order/getOrderList", params);
};

/**
 * 获取所有的排版
 */
export const getTrainNoList = (params: any) => {
  return request.get("/sc/order/getTrainNoList", params);
};

/**
 * 查询用户分享列表
 */
export const postUserShareList = (params: any) => {
  return request.post(`/uc/clientUserShare/getSharedInfoItem`, params);
};

/**
 * 查询佣金列表
 */
export const postCommissionList = (params: any) => {
  return request.post(`/uc/clientUserShare/getCommissionList`, params);
};

/**
 * 确认收款
 */
export const postReceivePay = (params: any) => {
  return request.post(`/pay/receivePay/receive`, params);
};

/**
 * 查询区域列表
 */
export const getRegionList = (params: any) => {
  return request.post("/sc/region/getRegionList", params);
};

/**
 * 查询区域详情
 */
export const getRegionDetail = (params: any) => {
  return request.get(`/sc/region/regionDetail/${params.regionId}`);
};

/**
 * 保存线路定制上下车
 */
export const savedTravelLineRegionStop = (params: any) => {
  return request.post(
    "/sc/travelLineRegionStop/savedTravelLineRegionStop",
    params
  );
};

/**
 * 根据线路ID获取定制线路关联的区域站点信息列表
 */
export const getTravelLineRegionStopListByLineId = (params: any) => {
  return request.post(
    `/sc/travelLineRegionStop/getTravelLineRegionStopList`,
    params
  );
};

/**
 * 查询电子围栏范围内的指定性质（定制）的站点
 */
export const getRegionElecFenceOwnStopList = (params: any) => {
  return request.post("/sc/region/getRegionElecFenceOwnStopList", params);
};

/**
 * 获取线路类型
 */
export const getAllLineTypeTree = (params: any) => {
  return request.get("/lineType/getAllLineTypeTree", params);
};

/**
 * 查询指定线路类型的线路列表
 */
export const postLineListByLineTypeCode = (params: any) => {
  return request.post(`/lineType/getLineListByLineTypeCode`, params);
};

/**
 * 分页带条件查询区域
 */
export const getRegionListByPage = (params: any) => {
  return request.post("/sc/region/getRegionList", params);
};

/**
 * 定制-删除排班班次驳载数据
 */
export const deleteSchedulingBarge = (params: any) => {
  return request.get(`/scheduling/template/deleteSchedulingItem/${params}`);
};

/**
 * 定制-删除排班模板驳载数据
 */
export const deleteSchedulingTemplateBarge = (params: any) => {
  return request.get(
    `/scheduling/template/deleteSchedulingTemplateItemById/${params}`
  );
};
