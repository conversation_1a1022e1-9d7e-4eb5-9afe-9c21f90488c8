<template>
  <div style="width: 100%">
    <pl-select
      :options="selfOptions"
      v-model="selectVal"
      :valueKey="valueKey"
      :labelKey="labelKey"
      @change="change"
      filterable
      :multiple="multiple"
      collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="1"
      class="line-list"
      :disabled="disabled"
      @remove-tag="onRemoveTag"
      valueType="diyId"
      :placeholder="placeholder"
    >
      <template #label="{ value }">
        <div v-if="value">
          <span class="line-type-name" :class="getColor(value.lineTypeCode)">{{
            value.lineTypeName
          }}</span>
          {{ value.lineName }}
        </div>
      </template>
      <template #option="{ item }">
        <div>
          <span class="line-type-name" :class="getColor(item.lineTypeCode)">{{
            item.lineTypeName
          }}</span>
          {{ item.lineName }}
        </div>
      </template>
    </pl-select>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { getColor } from "@/utils/index";
import { postLineListByLineTypeCode } from "@/api/travel";
import _ from "lodash";
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: "",
  },
  options: {
    type: Array,
    default: () => [],
  },
  valueKey: {
    type: String,
    default: "lineId",
  },
  labelKey: {
    type: String,
    default: "lineName",
  },
  valueType: {
    type: String,
    default: "string",
  },
  lineTypeCode: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  fromData: {
    type: Object,
    default: () => {},
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
});
const selfOptions = ref([]);

// 更新列表
const updateList = (lineTypeCode) => {
  selfOptions.value = [];
  if (!lineTypeCode) {
    return;
  }
  postLineListByLineTypeCode({
    lineTypeCode: lineTypeCode,
  }).then((res) => {
    res.data.map((item, index) => {
      item.diyId = index;
    });
    selfOptions.value = res.data;
    init();
  });
};
updateList(props.fromData?.lineTypeCode);
// 监听线路类型
watch(
  () => props.fromData?.lineTypeCode,
  (val, oldVal) => {
    if (val != oldVal) {
      updateList(val);
    }
  }
);

const selectVal = ref([]);
const init = () => {
  selectVal.value = [];
  let { lineId, travelLineId } = props.fromData;
  console.log(lineId, "lineId");
  console.log(travelLineId, "travelLineId");
  if (lineId) {
    let lineIds =
      typeof lineId == "string"
        ? lineId.split(",").map((item) => Number(item))
        : [lineId];
    let lineItems = selfOptions.value.filter(
      (item) =>
        lineIds.includes(item.lineId) && item.parentLineTypeCode == "GJLX"
    );
    if (lineItems.length > 0) {
      if (props.multiple) {
        selectVal.value = selectVal.value.concat(lineItems);
      } else {
        selectVal.value = lineItems[0];
      }
    }
  }
  if (travelLineId) {
    let travelLineIds =
      typeof travelLineId == "string"
        ? travelLineId.split(",").map((item) => Number(item))
        : [travelLineId];
    let travelLineItems = selfOptions.value.filter(
      (item) =>
        travelLineIds.includes(item.lineId) && item.parentLineTypeCode == "DZLX"
    );

    if (travelLineItems.length > 0) {
      if (props.multiple) {
        selectVal.value = selectVal.value.concat(travelLineItems);
      } else {
        selectVal.value = travelLineItems[0];
      }
    }
  }
};

watch(
  () => props.options,
  (val) => {
    val.map((item, index) => {
      item.diyId = index;
    });
    selfOptions.value = val;
  }
);

const emit = defineEmits(["change", "update:modelValue", "update:fromData"]);

const change = (valItems) => {
  const copyFromData = _.cloneDeep(props.fromData);
  let lineId = [];
  let travelLineId = [];

  if (Array.isArray(valItems)) {
    valItems.forEach((item) => {
      if (item.parentLineTypeCode == "DZLX") {
        travelLineId.push(item.lineId);
      } else if (item.parentLineTypeCode == "GJLX") {
        lineId.push(item.lineId);
      }
    });
  } else {
    lineId.push(valItems.lineId);
    travelLineId.push(valItems.travelLineId);
  }

  copyFromData.lineId = lineId.join(",");
  copyFromData.travelLineId = travelLineId.join(",");
  emit("update:fromData", copyFromData);
  emit("change", valItems);
};

const onRemoveTag = (val) => {
  if (val) {
    emit("removeTag", val);
  }
};

defineExpose({
  getOptions: () => selfOptions.value,
  setSelectVal: (val) => {
    if (props.multiple) {
      selectVal.value.push(val);
    } else {
      selectVal.value = val;
    }
  },
  clearSelectVal: () => {
    selectVal.value = [];
    emit("update:fromData", {
      lineId: "",
      travelLineId: "",
    });
  },
});
</script>

<style lang="scss" scoped>
.line-type-name {
  padding: 3px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  line-height: 12px;
}
.line-list {
  :deep(.el-select__input-wrapper) {
    max-width: 30px;
  }
}
</style>
