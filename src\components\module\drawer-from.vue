<template>
  <drawer :title="title" :modelValue="drawerVisible" @close="handleClose">
    <div class="drawer-box h100">
      <pl-form
        ref="formRef"
        :fields="fields"
        :isButtonShow="false"
        :form="form"
        :disabled="disabled"
        inline
        v-if="drawerVisible"
      >
        <template #colorCode>
          <slot name="colorCode"></slot>
        </template>

        <template
          v-for="field in fields"
          :key="field.prop"
          v-slot:[field.template]
        >
          <org-select
            v-model="form.orgId"
            v-if="field.template === 'orgId'"
            :disabled="disabled"
          ></org-select>
          <slot :name="field.template" v-else-if="field.template"></slot>
        </template>
      </pl-form>
      <div class="drawer-content">
        <pl-scrollbar class="main">
          <slot></slot>
        </pl-scrollbar>
      </div>
    </div>

    <!-- 按钮 -->
    <div class="button-box" v-if="!disabled">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <!-- <pl-button class="btn">确定并新增</pl-button> -->
      <pl-button
        class="btn"
        type="primary"
        @click="handleSubmit"
        :loading="props.loading"
        >确定</pl-button
      >
    </div>
  </drawer>
</template>

<script setup>
import { ref, watch } from "vue";
import drawer from "@/components/module/drawer.vue";
let formRef = ref(null);

const props = defineProps({
  modelValue: {
    // type: Boolean,
    type: [Boolean, String, Number, Array],
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  form: {
    type: Object,
    default: () => ({}),
  },
  fields: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const drawerVisible = ref(props.modelValue);
const emit = defineEmits(["update:modelValue", "submit", "open", "close"]);
watch(
  () => props.modelValue,
  (v) => {
    drawerVisible.value = v;
  }
);

const handleClose = () => {
  emit("update:modelValue", false);
  emit("close");
};
const handleSubmit = () => {
  formRef.value.confirm((data) => {
    emit("submit", data);
  });
};
</script>

<style scoped lang="scss">
.button-box {
  display: flex;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  justify-content: center;
  z-index: 2;
  height: 48px;
  background-color: #fff;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
  align-items: center;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;

  .btn {
    width: 200px;
  }
}
.drawer-content {
  flex: 1;
}
.drawer-box {
  display: flex;
  flex-direction: column;
  .drawer-content {
    position: relative;
    flex: 1;
    .main {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
