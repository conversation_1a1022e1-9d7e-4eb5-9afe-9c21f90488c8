import { ref } from "vue";

// 消息模板column
export const tempTableColumns = ref([
  {
    label: "模板编码",
    prop: "msgTemplateCode",
    sortable: "custom",
    minWidth: 100,
  },
  { label: "模板名称", prop: "msgTemplateName", minWidth: 120 },
  { label: "模板类型", prop: "msgTemplateTypeName", minWidth: 100 },
  {
    label: "模板内容",
    prop: "msgTemplateContent",
    template: "templateContent",
    sortable: "custom",
    minWidth: 200,
  },
  { label: "播报内容", prop: "msgTemplateVoiceContent", minWidth: 200 },
  {
    label: "状态",
    prop: "status",
    template: "status",
    sortable: "custom",
    minWidth: 80,
  },
  { label: "创建时间", prop: "createTime", minWidth: 130 },
  {
    label: "操作",
    template: "operation",
    minWidth: 100,
    fixed: "right",
    setting: true,
  },
]);

// 消息推送column
export const pushTableColumns = ref([
  { label: "序号", type: "index", minWidth: 30 },
  { label: "推送号", prop: "msgPushNo", sortable: "custom", minWidth: 80 },
  {
    label: "模板名称",
    prop: "msgTemplateName",
    minWidth: 80,
  },
  {
    label: "消息内容",
    prop: "msgPushContent",
    sortable: "custom",
    minWidth: 210,
  },
  {
    label: "状态",
    prop: "status",
    sortable: "custom",
    template: "status",
    minWidth: 80,
  },
  {
    label: "模板类型",
    prop: "msgTemplateTypeName",
    minWidth: 80,
  },
  {
    label: "推送时间",
    prop: "createTime",
    sortable: "custom",
    minWidth: 80,
    fixed: "right",
  },
]);
