<template>
  <div>
    <pl-form
      :fields="formColumns"
      inline
      :span="8"
      :isButtonShow="false"
      :form="formData"
      ref="formRef"
    ></pl-form>
    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <!-- <pl-button class="btn">确定并新增</pl-button> -->
      <pl-button class="btn" type="primary" @click="handleSubmit"
        >确定</pl-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { autoCode } from "@/utils";
const emit = defineEmits(["close", "submit"]);
const formRef = ref(null);
const props = defineProps({
  treeOptions: {
    type: Array,
    default: () => [],
  },
  fromData: {
    type: Object,
    default: () => {},
  },
});
console.log(props.treeOptions);
console.log(props.fromData);
// 回显
const formData = ref(props.fromData);

// 添加一个查找分类的函数
const findCategoryName = (tree, targetId) => {
  if (!targetId && targetId !== 0) return;
  for (const node of tree) {
    if (node.qaCategoryId === targetId) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const result = findCategoryName(node.children, targetId);
      if (result) return result;
    }
  }
  return "";
};
const categorOptions = ref([
  {
    categoryName: "一级类别",
    level: 0,
    parentId: 0,
    qaCategoryId: 0,
  },
  ...props.treeOptions,
]);
const categorySelect = ref(null);

// 在需要使用的地方
const getCategory = findCategoryName(
  categorOptions.value,
  props.fromData.parentId
);

if (getCategory) {
  formData.value.categoryId = [getCategory.categoryName];
  categorySelect.value = [getCategory];
}

const handleClose = () => {
  emit("close");
};
const handleSubmit = () => {
  formRef.value.confirm((data) => {
    data.categorySelect = categorySelect.value[0];
    const params = {
      categoryCode: data.categoryCode,
      categoryName: data.categoryName,
      qaCategoryId: data.qaCategoryId || "",
      status: data.status,
      remark: data.remark,
      level: data.qaCategoryId
        ? data.categorySelect.level
        : data.categorySelect.level + 1,
      parentId: data.qaCategoryId
        ? data.parentId
        : data.categorySelect.qaCategoryId,
    };
    emit("submit", params);
  });
};

const formColumns = ref([
  {
    label: "所属类别",
    type: "tree-select-pro",
    options: categorOptions.value,
    prop: "categoryId",
    nodeKey: "categoryName",
    cascaderProps: {
      label: "categoryName",
      value: "categoryName",
      children: "children",
    },
    multiple: false,
    rules: { required: true, message: "请选择所属类别" },
    onChange: (value, v2) => {
      console.log(v2);
      categorySelect.value = v2;
    },
  },
  {
    label: "类别名称",
    type: "input",
    prop: "categoryName",
    rules: { required: true, message: "请输入类别名称" },
    input: (value) => {
      formData.value.categoryCode = autoCode(value);
    },
  },
  {
    label: "类别编码",
    prop: "categoryCode",
    type: "input",
    disabled: true,
    rules: { required: true, message: "请输入类别编码" },
  },
  {
    label: "类别描述",
    type: "textarea",
    prop: "remark",
  },
  {
    label: "状态",
    type: "radio",
    prop: "status",
    rules: { required: true, message: "请选择状态" },
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
]);
</script>

<style lang="scss" scoped></style>
