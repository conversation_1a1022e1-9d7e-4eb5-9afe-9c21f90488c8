<template>
  <!-- 卡片组件开始 -->
  <pl-card class="unified-ticket-sales">
    <pl-drag-range :initialLeftWidth="1000">
      <template #left>
        <div class="wechat-notice-detail">
          <div class="notice-container">
            <div class="notice-row">
              <div class="notice-item">
                <div class="notice-label">起点</div>
                <div class="notice-value">重庆大学城</div>
              </div>
              <div class="notice-item">
                <div class="notice-label">终点</div>
                <div class="notice-value">万州</div>
              </div>
              <div class="notice-item">
                <div class="notice-label">出发时间</div>
                <div class="notice-value">2025-12-23 10:30</div>
              </div>
              <div class="notice-item">
                <div class="notice-label">班次号</div>
                <div class="notice-value">T001</div>
              </div>
              <div class="notice-item">
                <div class="notice-label">票价</div>
                <div class="notice-value">10元</div>
              </div>
              <div class="notice-item">
                <div class="notice-label">座次号</div>
                <div class="notice-value">001</div>
              </div>
            </div>
          </div>
        </div>
        <div class="drawer-w">
          <div class="title">乘车人信息</div>
          <pl-form
            confirmButtonText="搜索"
            cancelButtonText="重置"
            :fields="formColumns"
            :form="formData"
            formType="1"
            :isButtonShow="false"
          >
          </pl-form>
        </div>
      </template>
      <template #right>
        <seatSelect
          isSale
          :dropValueData="[]"
          seatId="7"
          @clickSeat="seatSelectChange"
        ></seatSelect>
      </template>
    </pl-drag-range>
    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <pl-button class="btn" type="primary" @click="handleSaveData"
        >确定</pl-button
      >
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import seatSelect from "@/components/seat-select/seat-select.vue";
import { plMessage } from "pls-common";
const formData = ref({});
const formColumns = ref([
  {
    label: "票种类型",
    prop: "ticketType",
    type: "select",
    options: [],
  },
  {
    label: "乘车人",
    prop: "passengerName",
    type: "input",
  },
  {
    label: "联系电话",
    prop: "contactPhone",
    type: "input",
  },
  {
    label: "身份证号",
    prop: "input",
  },
]);

onMounted(() => {
  //组件加载完成后执行事件
});

const emit = defineEmits(["close"]);
const handleClose = () => {
  emit("close");
};

const handleSaveData = () => {
  plMessage("保存成功", "success");
  handleClose();
};

// 座次选中事件
const seatSelectChange = (data) => {
  console.log(data);
};
</script>

<style lang="scss" scoped>
.button-box {
  z-index: 99;
}
.left-content-ticket {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.unified-ticket-sales {
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 60px !important;
}

.wechat-notice-detail {
  padding: 20px;

  .notice-container {
    display: flex;
    flex-direction: column;
  }

  .notice-row {
    display: flex;
    flex-wrap: wrap;
    .notice-item {
      min-width: 33.333333%;
    }
  }

  .notice-item {
    flex: 1;
    display: flex;
    border: 1px solid #ebeef5;
    margin-top: -1px;
    margin-right: -1px;
    .notice-label {
      width: 150px;
      padding: 12px 15px;
      background-color: #f5f7fa;
      border-right: 1px solid #ebeef5;
      color: #606266;
      font-weight: 500;
      white-space: nowrap;
    }

    .notice-value {
      flex: 1;
      padding: 12px 15px;
      color: #606266;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}
</style>
