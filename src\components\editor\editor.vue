<!-- https://www.wangeditor.com/v5/editor-config.html#customalert 编辑器文档 -->

<template>
  <div class="editor-box">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
    />
    <Editor
      style="height: 500px; overflow-y: hidden"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<script setup>
import { onBeforeUnmount, ref, shallowRef, onMounted } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import "@wangeditor/editor/dist/css/style.css";
import { ossUpload } from "pls-common";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
});

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
// 内容 HTML
const valueHtml = ref("");
// 模拟 ajax 异步获取内容
onMounted(() => {
  setTimeout(() => {}, 1500);
});

const toolbarConfig = {};
const editorConfig = {
  placeholder: "请输入内容...",
  MENU_CONF: {
    // 图片上传
    uploadImage: {
      customUpload: async (file, insertFn) => {
        const res = await ossUpload(file, file.name);
        insertFn(res, file.name);
      },
    },
    uploadVideo: {
      // 视频上传
      customUpload: async (file, insertFn) => {
        const res = await ossUpload(file, file.name);
        insertFn(res);
      },
    },
  },
};

const emit = defineEmits(["update:modelValue"]);
const handleChange = () => {
  emit("update:modelValue", valueHtml.value);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
  valueHtml.value = props.modelValue;
};
</script>

<style lang="scss" scoped></style>
