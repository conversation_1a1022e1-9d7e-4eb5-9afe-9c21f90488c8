import request from "./request";

/**
 * 分页带条件查询车型
 */
export const getVehicleModel = (params: any) => {
  return request.get("/vehicleModel/pageVehicleModel", params);
};

/**
 * 查询车型
 */
export const getVehicleModelDetail = (params: any) => {
  return request.get(
    `/vehicleModel/vehicleModelDetail/${params.vehicleModelId}`
  );
};

/**
 * 座次详情
 */
export const getSeatDetail = (params: any) => {
  return request.get(`/seat/seatDetail/${params.seatId}`);
};

/**
 * 获取座次列表
 */
export const getSeatList = (params: any) => {
  return request.get(`/seat/getSeatList`, params);
};

/**
 * 获取座次预览
 */
export const getSeatPreview = (params: any) => {
  return request.get(`/seat/getSeatPreview/${params.seatId}`);
};

/**
 * 车型列表
 */
export const getVehicleModelList = (params: any) => {
  return request.get(`/vehicleModel/getVehicleModelList`, params);
};
