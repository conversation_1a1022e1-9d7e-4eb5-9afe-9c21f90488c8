<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        :fields="columns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        @confirm="handleSearch"
        @cancel="handleCancel"
        confirmButtonText="搜索"
        cancelButtonText="重置"
      ></pl-form>

      <!-- 表单 start -->
      <!-- <div class="card-table mt20">
          <pl-table
            :columns="invoiceColumns"
            :data="tableData"
            class="table"
            v-loading="tabLoading"
          >
            <template #operation="{ scope }">
              <pl-button link @click="handleCheckOrder(scope.row)">
                查单
              </pl-button>
              <pl-button
                link
                type="primary"
                @click="handlePay(scope.row)"
                v-if="scope.row.payStatus === 'WZF'"
                >收款</pl-button
              >
            </template>
          </pl-table>
        </div> -->

      <div class="card-table mt20">
        <pl-table
          :columns="invoiceColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, invoiceColumns)"
        >
          <template #status="{ scope }">
            <div>
              {{ scope.row.status === 0 ? "待开票" : "已开票" }}
            </div>
          </template>
          <template #operation="{ scope }">
            <pl-button type="primary" link @click="goDetail(scope.row)">
              查看
            </pl-button>
            <pl-button
              link
              type="danger"
              @click="goTick(scope.row)"
              v-if="scope.row.status === 0"
            >
              开票
            </pl-button>
          </template>
        </pl-table>
      </div>
      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 表单 end -->
    </div>

    <!-- 查看弹窗 -->
    <drawerFrom
      :fields="drawerFields"
      :form="fromData"
      :disabled="true"
      v-model="drawerVisible"
      :title="drawerTitle"
    >
      <template #status>
        <pl-input
          disabled
          placeholder=" "
          :value="fromData.status === 0 ? '待开票' : '已开票'"
        ></pl-input>
      </template>
      <template #path>
        <pl-input
          v-if="!fromData.invoiceAttachmentPath"
          disabled
          placeholder=" "
        ></pl-input>
        <div v-else>
          <pl-image
            style="height: 100px; width: 100px"
            :src="fromData.invoiceAttachmentPath"
          />
        </div>
      </template>
      <!-- 票务信息 -->
      <div class="tick">
        <div class="tick-title">票务信息</div>
        <div class="tick-table">
          <table>
            <tbody>
              <tr>
                <td class="tick-label">发票抬头</td>
                <td class="tick-value">{{ tickInfo[0].value }}</td>
                <td class="tick-label">业务类型</td>
                <td class="tick-value">{{ tickInfo[1].value }}</td>
                <td class="tick-label">车票金额</td>
                <td class="tick-value">{{ tickInfo[2].value }}</td>
              </tr>
              <tr>
                <td class="tick-label">起点</td>
                <td class="tick-value">{{ tickInfo[3].value }}</td>
                <td class="tick-label">终点</td>
                <td class="tick-value">{{ tickInfo[4].value }}</td>
                <td class="tick-label">乘车时间</td>
                <td class="tick-value">{{ tickInfo[5].value }}</td>
              </tr>
              <tr>
                <td class="tick-label">乘车人</td>
                <td class="tick-value">{{ tickInfo[6].value }}</td>
                <td class="tick-label">联系电话</td>
                <td class="tick-value">{{ tickInfo[7].value }}</td>
                <td class="tick-label">身份证号</td>
                <td class="tick-value">{{ tickInfo[8].value }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </drawerFrom>

    <!-- 开票弹窗 -->
    <pl-dialog
      v-model="dialogVisible"
      title="开票"
      append-to-body
      showCancel
      :loading="confirmLoading"
      @confirm="handleConfirm"
    >
      <template #content>
        <div class="dialog">
          <div class="dialog-info">
            <div class="dialog-info-item">
              <div class="dialog-info-item-title">抬头名称</div>
              <div class="dialog-info-item-value">
                {{ curRow.headerName }}
              </div>
            </div>
            <div class="dialog-info-item">
              <div class="dialog-info-item-title">公司税号</div>
              <div class="dialog-info-item-value">
                {{ curRow.companyTaxNumber }}
              </div>
            </div>
          </div>
          <div class="dialog-upload">
            <div class="dialog-upload-title">发票附件</div>
            <plUpload
              :count="1"
              v-model="confirmImg"
              uploadType="image"
            ></plUpload>
          </div>
        </div>
      </template>
    </pl-dialog>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getDictionaryData } from "@/api/dict";
import { useTable } from "@/hooks/usetTable";
import { getBusinessType, invoiceDetail, openInvoice } from "@/api/index";
import { invoiceColumns } from "./config";
import drawerFrom from "@/components/module/drawer-from.vue";
import { plMessage } from "pls-common";

const queryForm = ref({});

const invoiceType = ref([]); // 抬头类型
onMounted(() => {
  getDictionaryData({
    dictTypeCode: "invoice_header_type",
  }).then((res) => {
    invoiceType.value = res;
    console.log(invoiceType.value);
  });
  getBusType();
});
// 获取业务类型
const businessType = ref([]); // 业务类型
const getBusType = async () => {
  const res = await getBusinessType();
  businessType.value = res.data;
};

// 使用表格hook，获取表格相关方法和数据
const {
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleSearch, // 搜索方法
  handleCancel, // 重置方法
  handleSort, // 排序方法
  current,
  loadData,
} = useTable({
  queryForm,
  list: "/pay/invoice/pageInvoice", // 列表接口
});

const columns = ref([
  {
    label: "抬头类型",
    type: "select",
    prop: "invoiceHeaderType",
    options: invoiceType,
  },
  {
    label: "抬头名称",
    prop: "headerName",
    type: "input",
  },
  {
    label: "电子邮箱",
    prop: "mailbox",
    type: "input",
  },
  {
    label: "状态",
    type: "select",
    prop: "status",
    options: [
      {
        label: "已开票",
        value: "1",
      },
      {
        label: "待开票",
        value: "0",
      },
    ],
  },
  {
    label: "业务类型",
    prop: "businessTypeList",
    type: "select",
    labelKey: "businessTypeName",
    valueKey: "businessType",
    options: businessType,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
]);

// 点击查看
const drawerVisible = ref(false);
const drawerTitle = ref("");
const fromData = ref({});
const goDetail = async (row) => {
  drawerTitle.value = "查看";

  const res = await invoiceDetail(row.invoiceId);
  fromData.value = res.data;
  // 票务信息赋值
  const a = fromData.value.ticketInfoVOList[0];
  tickInfo.value[0].value = fromData.value.invoiceHeaderTypeName;
  tickInfo.value[1].value = fromData.value.businessTypeName;
  tickInfo.value[2].value = fromData.value.invoiceAmount;
  tickInfo.value[3].value = a.startStopName;
  tickInfo.value[4].value = a.endStopName;
  tickInfo.value[5].value = a.departureTime;
  tickInfo.value[6].value = a.passName;
  tickInfo.value[7].value = a.passPhone;
  tickInfo.value[8].value = a.passId;

  drawerVisible.value = true;
};

// 点击开票
const dialogVisible = ref(false);
const confirmImg = ref();
const curRow = ref({});
const goTick = (row) => {
  dialogVisible.value = true;
  curRow.value = row;
};
const drawerFields = ref([
  {
    label: "抬头类型",
    prop: "invoiceHeaderTypeName",
    type: "input",
  },
  {
    label: "抬头名称",
    prop: "headerName",
    type: "input",
  },
  {
    label: "公司税号",
    prop: "companyTaxNumber",
    type: "input",
  },
  {
    label: "电子邮箱",
    prop: "mailbox",
    type: "input",
  },
  {
    label: "注册地址",
    prop: "registeredAddress",
    type: "input",
  },
  {
    label: "注册电话",
    prop: "registeredPhone",
    type: "input",
  },
  {
    label: "开户银行",
    prop: "openingBank",
    type: "input",
  },
  {
    label: "银行帐户",
    prop: "bankNo",
    type: "input",
  },
  {
    label: "备注",
    prop: "remark",
    type: "select",
    placeholder: "暂无",
  },
  {
    label: "开票金额",
    prop: "invoiceAmount",
    type: "input",
  },
  {
    label: "状态",
    prop: "status",
    template: "status",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    type: "input",
  },
  {
    label: "发票附件",
    prop: "invoiceAttachmentPath",
    template: "path",
  },
]);
// 确认开票
const confirmLoading = ref(false);
const handleConfirm = async () => {
  confirmLoading.value = true;
  console.log(confirmImg.value);
  if (!confirmImg.value) {
    plMessage("请上传发票附件", "warning");
    return;
  }

  try {
    const params = {
      orgId: JSON.parse(localStorage.getItem("userInfo"))?.orgId || 56,
      invoiceAttachmentPath: confirmImg.value,
      invoiceId: curRow.value.invoiceId,
    };
    const res = await openInvoice(params);
    if (res.code === 200) {
      plMessage("开票成功", "success");
      dialogVisible.value = false;
      confirmImg.value = "";
      loadData();
    }
  } catch (error) {
    console.log(error);
  } finally {
    confirmLoading.value = false;
  }
};

const tickInfo = ref([
  {
    title: "发票抬头",
    value: "",
  },
  {
    title: "业务类型",
    value: "",
  },
  {
    title: "车票金额",
    value: "",
  },
  {
    title: "起点",
    value: "",
  },
  {
    title: "终点",
    value: "",
  },
  {
    title: "乘车时间",
    value: "",
  },
  {
    title: "乘车人",
    value: "",
  },
  {
    title: "联系电话",
    value: "",
  },
  {
    title: "身份证号",
    value: "",
  },
]);
</script>

<style lang="scss">
.tick {
  .tick-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
  }
  .tick-table {
    width: 100%;
    table {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
      table-layout: fixed;

      tbody {
        tr {
          border-bottom: 1px solid #e4e7ed;

          &:last-child {
            border-bottom: none;
          }

          &:nth-child(even) {
            background-color: #fafafa;
          }

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .tick-label {
          width: 16.66%;
          padding: 8px 12px;
          background-color: #f5f7fa;
          font-weight: 600;
          color: #606266;
          border-right: 1px solid #e4e7ed;
          text-align: center;
          vertical-align: middle;
          font-size: 13px;
        }

        .tick-value {
          width: 16.66%;
          padding: 8px 12px;
          color: #303133;
          vertical-align: middle;
          word-break: break-all;
          font-size: 13px;
          text-align: center;
        }
      }
    }
  }
}

.dialog {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .dialog-info {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .dialog-info-item {
      display: flex;
      align-items: center;
      .dialog-info-item-title {
        font-size: 14px;
        font-weight: 600;
        margin-right: 10px;
      }
      .dialog-info-item-value {
        font-size: 14px;
      }
    }
  }
  .dialog-upload {
    margin-top: 20px;
    display: flex;
    align-items: flex-start;
    .dialog-upload-title {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
}
</style>
