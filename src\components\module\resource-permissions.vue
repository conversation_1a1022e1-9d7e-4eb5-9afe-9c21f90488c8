<template>
  <div class="resource-box h100">
    <div class="h100 resource-permissions">
      <div class="tabs-box">
        <div
          class="tabs-item"
          v-for="(item, index) in tabs"
          :key="index"
          :class="item.label == tabCurrent ? 'active' : ''"
          @click="handleTabChange(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <template v-for="(item, index) in tabs" :key="index">
        <div class="treex-box" v-show="item.label == tabCurrent">
          <pl-scrollbar class="tree-x">
            <treeX
              ref="treeRef"
              class="h100"
              :data="item.data"
              v-if="item.data.length > 0"
            ></treeX>
          </pl-scrollbar>
        </div>
      </template>
      <div class="button-box" v-if="showButton">
        <pl-button class="btn" type="primary" @click="handleSubmit"
          >确定</pl-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import treeX from "./tree-x.vue";
import { postRoleMenuTreeList, postRoleMenuSave } from "@/api/index";
import { plMessage } from "pls-common";
let tabCurrent = ref(""); //当前选中的tab
let treeXData = ref([]); //树形数据
let treeRef = ref({});
const props = defineProps({
  roleId: {
    type: [Number, String],
    default: "",
  },
  showButton: {
    type: Boolean,
    default: true,
  },
});
watch(
  () => props.roleId,
  (newVal) => {
    if (!newVal) return;
    getRoleList(newVal);
  }
);

onMounted(() => {
  if (!props.roleId) return;
  getRoleList(props.roleId);
});

let tabs = ref([]);
let updateRoleId = ref("");
const getRoleList = (roleId) => {
  updateRoleId.value = roleId;
  tabs.value = [];
  postRoleMenuTreeList({
    roleId,
  }).then((res) => {
    if (res.code === 200) {
      for (let k in res.data) {
        tabs.value.push({
          label: k,
          data: res.data[k],
        });
      }
      tabCurrent.value = tabs.value[0].label;
      treeXData.value = tabs.value[0].data;
    }
  });
};

/**
 * tab切换
 */
const handleTabChange = (item) => {
  tabCurrent.value = item.label;
};
const emit = defineEmits(["change"]);
const handleSubmit = () => {
  let menuIds = [];
  for (let i = 0; i < treeRef.value.length; i++) {
    menuIds = menuIds.concat(treeRef.value[i].menuIds);
  }
  postRoleMenuSave({
    menuIds,
    roleId: updateRoleId.value,
  }).then((res) => {
    if (res.code === 200) {
      plMessage(res.message, "success");
      emit("change");
    }
  });
};
let menuIds = treeRef.value.menuIds;

defineExpose({
  menuIds,
});
</script>

<style lang="scss" scoped>
.resource-permissions {
  display: flex;
  flex-direction: column;
  .treex-box {
    flex: 1;
    position: relative;
    margin-top: 20px;
    .tree-x {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: calc(100% - 50px);
    }
  }
}
.resource-box {
  display: flex;
  flex-direction: column;
  position: relative;
  .tree-x {
    flex: 1;
  }
}
.tabs-box {
  display: flex;
  .tabs-item {
    padding: 0 10px;
    cursor: pointer;
    position: relative;
    &.active {
      color: var(--el-color-primary);
      font-weight: bold;
      &::after {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        content: "";
        width: 100%;
        height: 2px;
        background-color: var(--el-color-primary);
      }
    }
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
