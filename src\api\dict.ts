import request from "./request";

/**
 * 字典类型管理-分页带条件查询
 * @param params  参数
 * @returns
 */
export const dictTypePage = (
  params: object = {
    current: 1, // 当前页
    limit: 10, // 每页条数
    dictTypeCode: "", //字典类型编码
    dictTypeName: "", // 字典类型名称
  }
) => {
  return request.post("/tenant/dictType/pageDictType", params);
};

/**
 * 字典类型管理-修改字典类型
 */

export const updateDictType = (
  params: object = {
    dictTypeCode: "", //字典类型编码
    dictTypeId: "", //字典类型id
    dictTypeName: "", // 字典类型名称
  }
) => {
  return request.post("/tenant/dictType/updateDictType", params);
};

/**
 * 字典类型管理-新增字典类型
 */
export const addDictType = (
  params: object = {
    dictTypeCode: "", //字典类型编码
    dictTypeName: "", // 字典类型名称
  }
) => {
  return request.post("/tenant/dictType/saveDictType", params);
};

/**
 * 字典类型管理-删除字典类型
 * @param params
 * @returns
 */
export const deleteDictType = (
  params: any = { dictTypeId: "", dictTypeCode: "" }
) => {
  return request.get(
    `/tenant/dictType/deleteDictType/${params.dictTypeId}/${params.dictTypeCode}`,
    params
  );
};

/**
 * 根据类型查询字典列表
 * @param params
 */
interface selectPageDictParams {
  current: number;
  limit: number;
  dictCode?: string;
  dictName?: string;
  dictTypeCode: string;
}
export const dictSelectPageDict = (params: selectPageDictParams) => {
  return request.post(`/tenant/dict/pageDict`, params);
};

/**
 * 添加字典
 */

export const addDict = (params: any) => {
  return request.post(`/tenant/dict/saveDict`, params);
};

/**
 * 修改字典
 */
export const updateDict = (params: any) => {
  return request.post(`/tenant/dict/updateDict`, params);
};

/**
 * 删除字典
 */
export const deleteDict = (id: number | string) => {
  return request.get(`/tenant/dict/deleteDict/${id}`);
};

/**
 * 系统参数
 */
export const postSysConfig = (params: any) => {
  return request.post(`/tenant/config/pageConfig`, params);
};

/**
 * 查询系统配置项详情
 */
export const getConfigItemDetail = (params: any) => {
  return request.get(
    `/tenant/configItem/configItemDetail/${params.configId}/${params.itemCode}`
  );
};

/**
 * 读取字典数据并缓存
 */
// export const getDictionaryData = async (params: any) => {
//   let storage = sessionStorage.getItem("dictArray") || ("{}" as any);
//   storage = JSON.parse(storage);
//   if (storage[params.dictTypeCode]) {
//     return storage[params.dictTypeCode];
//   }

//   const result = await request.post("/tenant/dict/dictList", {
//     dictTypeCode: params.dictTypeCode,
//     tenantId: params.tenantId || null,
//   });

//   if (result.code == 200) {
//     const dictArray = [];
//     for (let i = 0; i < result.data.length; i++) {
//       const item = {
//         label: result.data[i].dictName,
//         value: result.data[i].dictCode,
//       };
//       dictArray.push(item);
//     }

//     const obj = {
//       [params.dictTypeCode]: dictArray,
//     };
//     storage = Object.assign(storage, obj);
//     sessionStorage.setItem("dictArray", JSON.stringify(storage));
//     return dictArray;
//   }

//   return [];
// };

export const getDictionaryData = async (params: any) => {
  const storage = sessionStorage.getItem(params.dictTypeCode);
  if (storage) {
    return JSON.parse(storage);
  }

  const result = await request.post("/tenant/dict/dictList", {
    dictTypeCode: params.dictTypeCode,
    tenantId: params.tenantId || null,
  });

  if (result.code == 200) {
    const dictArray = [];
    for (let i = 0; i < result.data.length; i++) {
      const item = {
        label: result.data[i].dictName,
        value: result.data[i].dictCode,
        remark: result.data[i].remark,
      };
      dictArray.push(item);
    }
    sessionStorage.setItem(params.dictTypeCode, JSON.stringify(dictArray));
    return dictArray;
  }

  return [];
};

export default {
  deleteDict,
  dictSelectPageDict,
  deleteDictType,
  addDictType,
  dictTypePage,
  addDict,
  updateDict,
};
