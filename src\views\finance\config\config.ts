import { ref } from "vue";

/**
 * 应收记录模板列表
 */
export const arConfColumns = ref([
  { type: "selection", width: 55 },
  { label: "序号", type: "index", width: 80 },
  {
    label: "应收单号",
    prop: "receivableNo",
    sortable: "custom",
    width: 180,
  },
  { label: "订单号", prop: "orderNo", sortable: "custom", width: 180 },
  {
    label: "订单类型",
    prop: "orderTypeName",
    sortProp: "orderType",
    sortable: "custom",
    template: "orderType",
    width: 120,
  },
  { label: "付款方名称", prop: "payerName", sortable: "custom", width: 120 },
  {
    label: "应收金额(元)",
    prop: "receivableAmount",
    sortable: "custom",
    price: true,
    width: 180,
    align: "center",
  },
  {
    label: "已核销金额(元)",
    prop: "verifiedAmount",
    sortable: "custom",
    price: true,
    width: 180,
    align: "center",
  },
  {
    label: "未核销金额(元)",
    prop: "unverifiedAmount",
    sortable: "custom",
    price: true,
    width: 180,
    align: "center",
  },
  {
    label: "到期日期",
    prop: "expirationTime",
    sortable: "custom",
    width: 180,
  },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    sortProp: "verificationStatus",
    sortable: "custom",
    width: 180,
  },
  {
    label: "核销时间",
    prop: "verificationTime",
    sortable: "custom",
    width: 180,
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    sortProp: "businessType",
    sortable: "custom",
    width: 180,
  },
  { label: "关联合同", prop: "covenantNo", sortable: "custom", width: 180 },
  { label: "负责销售", prop: "covenantNo", sortable: "custom", width: 180 },
  { label: "账单备注", prop: "covenantNo", sortable: "custom", width: 180 },
  { label: "创建时间", prop: "covenantNo", sortable: "custom", width: 180 },
  { label: "更新时间", prop: "covenantNo", sortable: "custom", width: 180 },
  // {
  //   label: "操作",
  //   template: "operation",
  //   width: 150,
  //   fixed: "right",
  //   setting: true,
  // },
]);

/**
 * 实收记录配置列表
 */
export const actualConfColumns = ref([
  { type: "selection", width: 55 },
  { label: "实收单号", prop: "receiptNo", sortable: "custom", minWidth: 160 },
  { label: "订单号", prop: "orderNo", sortable: "custom", minWidth: 160 },
  {
    label: "订单类型",
    prop: "orderTypeName",
    sortProp: "orderType",
    sortable: "custom",
    template: "orderType",
    width: 180,
  },
  { label: "付款方名称", prop: "payerName", width: 180 },
  {
    label: "实收金额(元)",
    prop: "receiptAmount",
    sortable: "custom",
    price: true,
    width: 180,
    align: "center",
  },
  {
    label: "未核销金额(元)",
    prop: "unverifiedAmount",
    sortable: "custom",
    price: true,
    width: 180,
    align: "center",

  },
  { label: "支付账户", prop: "payerBankNo", sortable: "custom", width: 180 },
  {
    label: "收款状态",
    prop: "receiptStatusName",
    sortProp: "receiptStatus",
    sortable: "custom",
    width: 180,
  },
  {
    label: "收款方式",
    prop: "paymentWayName",
    sortProp: "paymentWay",
    sortable: "custom",
    width: 180,
  },
  {
    label: "交易流水号",
    prop: "transactionNo",
    sortable: "custom",
    width: 180,
  },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    sortProp: "verificationStatus",
    sortable: "custom",
    width: 180,
  },
  {
    label: "核销时间",
    prop: "verificationTime",
    sortable: "custom",
    width: 180,
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    sortProp: "businessType",
    sortable: "custom",
    width: 180,
  },
  //   { label: "关联合同", prop: "name", sortable: "custom", width: 180 },
  { label: "操作人", prop: "updateName", width: 180 },
  { label: "账单备注", prop: "remark", width: 180 },
  {
    label: "核销方式",
    prop: "verificationWay",
    sortable: "custom",
    template: "verificationWay",
    width: 180,
  },
  {
    label: "创建方式",
    prop: "createWay",
    sortable: "custom",
    template: "createWay",
    width: 180,
  },
  { label: "创建时间", prop: "createTime", sortable: "custom", width: 180 },
  { label: "更新时间", prop: "updateTime", sortable: "custom", width: 180 },
  {
    label: "交易凭证",
    prop: "voucherPath",
    sortable: "custom",
    template: "voucherPath",
    width: 180,
  },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);

/**
 * 支付通知配置列表
 */
export const payNoticeColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 80,
  },
  {
    label: "通知单号",
    prop: "notifyNo",
    minWidth: 160,
  },
  {
    label: "商户单号",
    prop: "outTradeNo",
    minWidth: 160,
  },
  {
    label: "通知类型",
    prop: "paymentNotifyTypeName",
    sortProp: "paymentNotifyType",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    minWidth: 120,
  },
  {
    label: "订单号",
    prop: "orderNo",
    sortable: "custom",
    minWidth: 160,
  },
  {
    label: "应结订单金额",
    prop: "payableOrderAmount",
    price: true,
    minWidth: 130,
    align: "center",
  },
  {
    label: "实付金额",
    prop: "payerPayAmount",
    price: true,
    minWidth: 120,
    align: "center",
  },
  {
    label: "退款金额",
    prop: "payerRefundAmount",
    price: true,
    minWidth: 120,
    align: "center",
  },
  {
    label: "业务结果",
    prop: "serviceResult",
    template: "serviceResult",
    minWidth: 130,
  },
  {
    label: "交易时间",
    prop: "transactionTime",
    minWidth: 150,
  },
  {
    label: "操作",
    template: "operation",
    minWidth: 180,
    fixed: "right",
    setting: true,
  },
]);

// 退款记录
export const refundColumns = ref([
  {
    label: "序号",
    type: "index",
    minWidth: 80,
  },
  {
    label: "退款单号",
    prop: "refundRecordNo",
    // sortProp: "paymentNotifyType",
    sortable: "custom",
    minWidth: 140,
  },
  {
    label: "订单号",
    prop: "refundNo",
    sortable: "custom",
    minWidth: 150,
  },
  {
    label: "退款金额(元)",
    prop: "refundAmount",
    sortable: "custom",
    price: true,
    minWidth: 130,
    align: "center",
  },
  {
    label: "退款方式",
    prop: "refundWay",
    sortable: "custom",
    minWidth: 120,
  },
  {
    label: "退款流水号",
    prop: "transactionNo",
    sortable: "custom",
    minWidth: 180,
  },
  {
    label: "退款时间",
    prop: "refundTime",
    sortable: "custom",
    minWidth: 180,
  },
  {
    label: "退款原因",
    prop: "refundReason",
    sortable: "custom",
    minWidth: 160,
  },
  {
    label: "退款状态",
    prop: "refundStatusName",
    sortable: "custom",
    minWidth: 110,
  },
  {
    label: "审核人",
    prop: "reviewUserName",
    minWidth: 130,
  },
  {
    label: "退款通知时间",
    prop: "notifyTime",
    sortable: "custom",
    minWidth: 180,
  },
  {
    label: "退款操作人",
    prop: "refundUserName",
    minWidth: 120,
    align: "center",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    sortable: "custom",
    minWidth: 120,
    align: "center",
  },
  {
    label: "创建时间",
    prop: "createTime",
    sortable: "custom",
    minWidth: 150,
    align: "center",
  },
  {
    label: "更新时间",
    prop: "updateTime",
    sortable: "custom",
    minWidth: 180,
    align: "center",
  },
  // {
  //   label: "备注",
  //   prop: "payAccount",
  //   minWidth: 280,
  //   align: "center",
  // },
  {
    label: "交易凭证",
    prop: "voucherPath",
    template: "voucherPath",
    minWidth: 280,
    align: "center",
  },
  {
    label: "操作",
    template: "operation",
    fixed: "right",
    minWidth: 160,
  },
]);
