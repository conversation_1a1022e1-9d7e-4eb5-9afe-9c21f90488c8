<!-- 收款记录 -->
<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        :fields="columns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        @confirm="handleSearch"
        @cancel="handleCancel"
        confirmButtonText="搜索"
        cancelButtonText="重置"
      ></pl-form>

      <!-- 操作按钮开始 -->
      <div class="operation-btns fixed">
        <pl-button
          type="primary"
          :loading="exportLoading"
          @click="exportDetail"
          class="fixed-right"
        >
          导出
        </pl-button>
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表单 start -->
      <div class="card-table mt20">
        <pl-table
          :columns="refundColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, refundColumns)"
        >
          <template #voucherPath="{ scope }">
            <pl-image
              style="height: 60px; width: 60px"
              :src="scope.row.voucherPath"
            />
          </template>
          <template #operation="{ scope }">
            <pl-button
              link
              type="primary"
              @click="confirm(scope.row)"
              v-if="scope.row.refundStatusName == '待退款'"
            >
              确认退款
            </pl-button>
            <pl-button
              link
              type="primary"
              @click="confirm(scope.row)"
              v-if="scope.row.refundStatusName == '退款异常'"
            >
              补发退款
            </pl-button>
          </template>
        </pl-table>
      </div>
      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 表单 end -->

      <!-- 确认/补发退款弹窗 -->
      <pl-dialog
        v-model="confirmVisible"
        title="确认退款"
        append-to-body
        align-center
        showCancel
        @confirm="handleConfirm"
        @cancel="confirmVisible = false"
      >
        <template #content>
          <div class="dialog">
            <div class="dialog-title">
              <span style="color: #ff0000">*</span><span>交易凭证</span>
            </div>
            <div class="dialog-upload">
              <plUpload
                :count="1"
                v-model="confirmImg"
                uploadType="image"
              ></plUpload>
            </div>
          </div>
        </template>
      </pl-dialog>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { plMessage } from "pls-common";
import {
  exportRefundRecord,
  getBusinessType,
  refundWayList,
  confirmRefund,
} from "@/api/index";
import { refundColumns } from "./config/config";
import { getDictionaryData } from "@/api/dict";
import { useTable } from "@/hooks/usetTable";

const businessType = ref([]); // 业务类型
const refundType = ref([]); // 退款状态
onMounted(() => {
  getDictionaryData({
    dictTypeCode: "refund_status",
  }).then((res) => {
    refundType.value = res;
  });
  getBusType();
  getRefundWay();
});
// 获取业务类型
const getBusType = async () => {
  const res = await getBusinessType();
  businessType.value = res.data;
};
// 获取退款方式
const refundWay = ref([]);
const getRefundWay = async () => {
  const res = await refundWayList();
  refundWay.value = res.data.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};

const queryForm = ref({});

// 使用表格hook，获取表格相关方法和数据
const {
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  loadData, // 加载数据方法
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleSearch, // 搜索方法
  handleCancel, // 重置方法
  current,
  handleSort,
} = useTable({
  queryForm,
  list: "/pay/refundRecord/pageRefundRecord", // 列表接口
});

const columns = ref([
  {
    label: "退款单号",
    type: "input",
    prop: "refundRecordNo",
  },
  {
    label: "退款方式",
    prop: "refundWayList",
    type: "select",
    options: refundWay,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "退款流水号",
    prop: "transactionNo",
    type: "input",
  },
  {
    label: "业务类型",
    type: "select",
    prop: "businessTypeList",
    labelKey: "businessTypeName",
    valueKey: "businessType",
    options: businessType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "退款状态",
    prop: "refundStatusList",
    type: "select",
    options: refundType,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "退款时间",
    prop: "refundTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (val) => {
      if (val && val.length === 2) {
        queryForm.value.startRefundTime = val[0] + " 00:00:00";
        queryForm.value.endRefundTime = val[1] + " 23:59:59";
      }
    },
  },
  {
    label: "创建日期",
    prop: "createTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (val) => {
      if (val && val.length === 2) {
        queryForm.value.startCreateTime = val[0] + " 00:00:00";
        queryForm.value.endCreateTime = val[1] + " 23:59:59";
      }
    },
  },
]);

// ^ 退款
const confirmVisible = ref(false);
const curConfirmId = ref(); // 当前确认退款id
const confirm = (row) => {
  curConfirmId.value = row.refundRecordId;
  confirmVisible.value = true;
};
// 确认退款
const confirmImg = ref();
const handleConfirm = async () => {
  if (!confirmImg.value) {
    plMessage("请上传交易凭证", "warning");
    return;
  }
  const res = await confirmRefund({
    refundRecordId: curConfirmId.value,
    voucherPath: confirmImg.value,
  });
  if (res.code == 200) {
    plMessage("退款成功", "success");
    confirmVisible.value = false;
    confirmImg.value = "";
    curConfirmId.value = "";
    loadData();
  }
};

// 点击导出
const exportLoading = ref(false);
const exportDetail = async () => {
  if (!tableData.value.length) {
    plMessage("暂无可导出的数据", "warning");
    return;
  }

  exportLoading.value = true;
  try {
    const res = await exportRefundRecord(queryForm.value);
    const filePath = res.data;

    window.open(filePath);
    plMessage("导出成功", "success");
  } catch (error) {
    console.log("error", error);
    plMessage(error.message, "error");
  } finally {
    exportLoading.value = false;
  }
};
</script>

<style lang="scss">
.pay-dialog {
  .el-message-box__container,
  .el-message-box__message {
    width: 100%;
  }
  .lineName {
    white-space: nowrap;
    display: flex;
    align-items: center;
  }
  .batch-dispatch {
    .flex {
      margin-bottom: 10px;
      align-items: center;
    }
    .input-kj {
      flex: 1;
    }
  }
}

.operation-btns {
  display: flex;
  justify-content: flex-end;
}

.dialog {
  display: flex;
  align-items: flex-start;
  .dialog-title {
    margin-right: 20px;
  }
}
</style>
