<template>
  <div class="tree-x-box">
    <div>
      <pl-checkbox
        v-model="allChecked"
        @change="allCheckBox($event, treeData)"
      ></pl-checkbox>
      全部
    </div>
    <div class="">
      <div class="tree-x-item" v-for="(item, index) in treeData" :key="index">
        <!-- 一级菜单 -->
        <div class="item-level1 item">
          <ElCheckbox
            class="checkbox"
            :indeterminate="item.indeterminate"
            v-model="item.selected"
            @change="level1Click($event, item)"
          >
          </ElCheckbox>
          {{ item.label }}
        </div>
        <!-- 二级菜单 -->
        <div class="item-level2 item">
          <div
            class="level2-item"
            v-for="(item2, index2) in item.children"
            :key="index2"
          >
            <div class="level2-checkbox">
              <ElCheckbox
                class="checkbox"
                :indeterminate="item2.indeterminate"
                v-model="item2.selected"
                @change="level2Click($event, item2)"
              >
              </ElCheckbox>
              {{ item2.label }}
            </div>
            <div class="item-level3">
              <div
                class="item-level3-item"
                v-for="(item3, index3) in item2.children"
                :key="index3"
              >
                <ElCheckbox
                  @change="level3Click($event, item3)"
                  class="checkbox"
                  v-model="item3.selected"
                ></ElCheckbox
                >{{ item3.label }}
              </div>
            </div>
          </div>

          <div class="level2-item" v-if="item.children.length === 0"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElCheckbox } from "element-plus";
let treeData = ref([]);
let menuIds = ref([]);
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

watch(
  () => props.data,
  (newVal) => {
    updateTreeData(newVal);
  },
  {
    deep: true,
  }
);
onMounted(() => {
  updateTreeData(props.data);
});
/**
 * 更新数据
 * 该函数用于递归地构建树形数据结构，根据传入的树形数组，生成一个新的树形数组
 * 新生成的树形数组中的每个节点都具有label、selected、indeterminate、selectedList、children和id属性
 * @param {Array} tree - 原始的树形数组数据
 */
const updateTreeData = (tree) => {
  // 初始化treeData数组
  treeData.value = [];
  // 遍历原始树形数组
  for (let i = 0; i < tree.length; i++) {
    // 创建一个新的树节点项
    let item = {
      label: tree[i].name, // 节点的名称
      selected: tree[i].authFlag || false, // 节点是否选中
      indeterminate: false, // 节点是否处于半选中状态
      selectedList: [], // 选中的子节点列表
      children: [], // 子节点列表
      id: tree[i].id, // 节点的唯一标识
    };
    // 如果当前节点有子节点，则处理子节点
    if (tree[i].children) {
      for (let j = 0; j < tree[i].children.length; j++) {
        let forChildren = tree[i].children[j];
        let children = {
          label: forChildren.name, // 子节点的名称
          selected: forChildren.authFlag || false, // 子节点是否选中
          indeterminate: false, // 子节点是否处于半选中状态
          selectedList: [], // 选中的子节点列表
          children: [], // 子节点的子节点列表
          id: forChildren.id, // 子节点的唯一标识
        };

        // 如果子节点还有子节点，则处理子节点的子节点
        if (forChildren.children && forChildren.children.length > 0) {
          for (let k = 0; k < forChildren.children.length; k++) {
            children.children.push({
              label: forChildren.children[k].name, // 子节点的子节点的名称
              selected: forChildren.children[k].authFlag || false, // 子节点的子节点是否选中
              id: forChildren.children[k].id, // 子节点的子节点的唯一标识
            });
          }
        }

        // 将处理后的子节点添加到当前节点的子节点列表中
        item.children.push(children);
      }
    }

    // 将处理后的节点添加到树形数据数组中
    treeData.value.push(item);
  }

  allOrInIndeterminate();
};

// 全部全选
let allChecked = ref(false);

/**
 * 全部权限or取消
 */

const allCheckBox = async (e) => {
  await forEachTree(treeData.value, e);
  await allOrInIndeterminate();
};
/**
 *
 */
function forEachTree(item, status) {
  let forItem = item.length > 0 ? item : item.children;
  forItem.forEach((e) => {
    e.selected = status;
    if (e.children && e.children.length > 0) {
      forEachTree(e.children, status);
    }
  });
}
/**
 * 判断是全选状态 还是 中间状态
 */

/**
 * 该函数用于遍历树形数据，根据三级菜单的选中情况更新菜单项的状态，包括全选、半选和未选中状态
 */
const allOrInIndeterminate = () => {
  menuIds.value = []; // 用于存储选中的菜单ID
  let checkLevel1 = 0; // 一级选中个数
  // 遍历一级菜单
  treeData.value.forEach((level1) => {
    level1.indeterminate = false; // 默认一级半选状态为false
    let checkLevel2 = 0; // 二级选中个数

    // 如果一级菜单有子菜单，遍历二级菜单
    if (level1.children.length > 0) {
      level1.children.forEach((level2) => {
        level2.indeterminate = false; // 默认二级半选状态为false
        if (level2.children.length > 0) {
          let checkLevel3 = 0; // 三级选中个数
          // 遍历三级菜单
          level2.children.forEach((level3) => {
            if (level3.selected) {
              checkLevel3++;
              menuIds.value.push(level3.id); // 将选中的三级菜单ID添加到menuIds.value数组中
            }
          });

          // 根据三级菜单的选中情况更新二级菜单状态
          if (checkLevel3 == level2.children.length) {
            level2.selected = true;
            level2.indeterminate = false;
          } else if (checkLevel3 > 0 && checkLevel3 < level2.children.length) {
            level2.indeterminate = true; // 如果三级没有都选中，则二级为半选
            level2.selected = false;
          }
        }

        // 如果二级菜单本身被选中或其子菜单为半选状态，更新一级菜单的选中个数
        if (level2.selected || level2.indeterminate) {
          checkLevel2++;
          menuIds.value.push(level2.id); // 将选中的二级菜单ID添加到menuIds.value数组中
        }
      });

      // 根据二级菜单的选中情况更新一级菜单状态
      if (checkLevel2 > 0 && checkLevel2 < level1.children.length) {
        level1.indeterminate = true; // 如果二级没有都选中，则一级为半选
        level1.selected = false;
      } else if (checkLevel2 == level1.children.length) {
        level1.selected = true;
        level1.indeterminate = false;
      }
    }

    // 如果一级菜单本身被选中，更新选中个数
    if (level1.selected) {
      checkLevel1++;
      menuIds.value.push(level1.id); // 将选中的一级菜单ID添加到menuIds.value数组中
    }
  });

  // 根据一级菜单的选中情况更新全选按钮状态
  if (checkLevel1 == treeData.value.length) {
    allChecked.value = true;
  } else {
    allChecked.value = false;
  }
};
/**
 * 一级菜单点击
 */
const level1Click = async (e, item) => {
  await forEachTree(item, e);
  await allOrInIndeterminate();
};

/**
 * 二级菜单点击
 */
const level2Click = async (e, item2) => {
  await forEachTree(item2, e);
  await allOrInIndeterminate();
};

/**
 * 三级菜单点击
 */
const level3Click = (e, item3) => {
  item3.selected = e;
  allOrInIndeterminate();
};

defineExpose({
  menuIds,
});
</script>

<style scoped lang="scss">
.tree-x-box {
  color: #606266;
}

.tree-x-item {
  display: flex;
  text-align: center;

  &:last-child {
    border-bottom: 1px solid var(--el-border-color);
  }
  .item-level1 {
    display: flex;
    align-items: center;
    border-left: 1px solid var(--el-border-color);
    border-top: 1px solid var(--el-border-color);
    border-right: 1px solid var(--el-border-color);
    padding: 10px;
    width: 200px;
  }

  .item-level2 {
    flex: 1;
    border-right: 1px solid var(--el-border-color);
  }

  .level2-checkbox {
    border-right: 1px solid var(--el-border-color);
    padding: 15px;
    width: 150px;
    display: flex;
    align-items: center;
  }

  .level2-item {
    display: flex;
    border-top: 1px solid var(--el-border-color);
  }

  .item-level3 {
    display: flex;
    padding: 0 10px;
    align-items: center;
    flex: 1;
    flex-wrap: wrap;
  }

  .checkbox {
    margin-right: 10px;
  }

  .item-level3-item {
    width: 20%;
    display: flex;
    align-items: center;
    padding: 10px 0;
  }
}
</style>
