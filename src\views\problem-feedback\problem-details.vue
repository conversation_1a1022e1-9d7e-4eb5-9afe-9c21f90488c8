<template>
  <div class="problem-details">
    <pl-scrollbar>
      <pl-form
        :fields="formColumns"
        inline
        :span="6"
        :isButtonShow="false"
        :form="detail"
        :disabled="true"
      >
        <template #questionType>
          <pl-dict-select
            dictCode="question_type"
            v-model="detail.questionType"
            :disabled="true"
          ></pl-dict-select>
        </template>
        <template #questionSource>
          <pl-dict-select
            dictCode="question_source"
            v-model="detail.questionSource"
            :disabled="true"
          ></pl-dict-select>
        </template>
      </pl-form>
      <div class="drawer-w">
        <div class="title">处理日志</div>
        <log :processList="processList" :disabled="true"></log>
      </div>
    </pl-scrollbar>

    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
    </div>
  </div>
</template>

<script setup>
import log from "./log.vue";
import {
  getFeedBackDetail,
  getFeedBackProcessList,
  getAppList,
} from "@/api/index";
import { ref } from "vue";
const props = defineProps({
  feedBackId: {
    type: [String, Number],
    required: true,
  },
});
const detail = ref({});
const processList = ref([]);
getFeedBackDetail({
  feedBackId: props.feedBackId,
}).then((res) => {
  if (res.code == 200) {
    detail.value = res.data;
  }
});
getFeedBackProcessList({
  feedBackId: props.feedBackId,
  sortOrder: 1,
}).then((res) => {
  if (res.code == 200) {
    processList.value = res.data;
  }
});

const emit = defineEmits(["close", "submit"]);
const handleClose = () => {
  emit("close");
};
const appList = ref([]);
getAppList().then((res) => {
  appList.value = res.data;
});
const formColumns = ref([
  {
    label: "反馈人",
    type: "input",
    prop: "userName",
  },
  {
    label: "反馈时间",
    type: "date",
    format: "YYYY-MM-DD HH:mm:ss",
    prop: "createTime",
  },
  {
    label: "问题来源",
    type: "select",
    prop: "questionSource",
    template: "questionSource",
  },
  {
    label: "问题类别",
    type: "select",
    template: "questionType",
  },
  {
    label: "所属应用",
    type: "select",
    options: appList,
    prop: "appId",
    valueKey: "appId",
    labelKey: "appName",
  },
  {
    label: "处理人",
    type: "input",
    prop: "processName",
  },
  {
    label: "处理时间",
    type: "input",
    prop: "processTime",
  },
  {
    label: "处理状态",
    type: "select",
    prop: "processStatus",
    options: [
      {
        label: "未处理",
        value: 0,
      },
      {
        label: "处理中",
        value: 1,
      },
      {
        label: "已完结",
        value: 2,
      },
    ],
  },
  {
    label: "线路及方向",
    type: "input",
    prop: "lineName",
  },
  {
    label: "车站名称",
    type: "input",
    prop: "stopName",
  },
  {
    label: "问题发生时间",
    type: "input",
    prop: "createTime",
  },
  {
    label: "联系方式",
    type: "input",
    prop: "phone",
  },
]);
</script>

<style lang="scss" scoped>
.problem-details {
  padding: 20px 20px 50px;
  height: 100%;
  .drawer-w {
    margin-top: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;

    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
      position: relative;
      padding-left: 12px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #1890ff;
        border-radius: 2px;
      }
    }
  }
}
</style>
