<template>
  <div class="belonging-line">
    <pl-select
      :options="selectLineList"
      v-model="selectLineList"
      valueKey="lineId"
      labelKey="lineName"
      valueType="diyId"
      multiple
      collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="1"
      :disabled="disabled"
      @click="dialogVisible = true"
      :placeholder="placeholder"
    >
      <template #label="{ value }">
        <div v-if="value">
          <span class="line-type-name" :class="getColor(value.lineTypeCode)">{{
            value.lineTypeName
          }}</span>
          {{ value.lineName }}
        </div>
      </template>
      <template #option="{ item }">
        <div>
          <span class="line-type-name" :class="getColor(item.lineTypeCode)">{{
            item.lineTypeName
          }}</span>
          {{ item.lineName }}
        </div>
      </template>
    </pl-select>
    <!-- <div class="belong-input" @click="dialogVisible = true">
      <div
        class="tag-name"
        v-for="(item, index) in selectLineList"
        :key="index"
      >
        {{ item.lineName }}
      </div>
    </div> -->

    <pl-dialog
      v-model="dialogVisible"
      title="所属线路"
      width="80vw"
      append-to-body
      align-center
      @confirm="confirm"
    >
      <template #content>
        <div class="dialog-content">
          <div class="cont-left">
            <ElTree
              :data="treeData"
              :props="defaultProps"
              show-checkbox
              @check="treeCheck"
              node-key="lineTypeCode"
              default-expand-all
              :default-checked-keys="defaultCheckedKeys"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <span>{{ node.label }}</span>
                  <span class="num" v-if="getNodeSelectedCount(data) > 0">{{
                    getNodeSelectedCount(data)
                  }}</span>
                </span>
              </template>
            </ElTree>
          </div>
          <div class="cont-right" v-loading="lineListLoading">
            <pl-scrollbar>
              <div class="search-box-content">
                <pl-input
                  class="search-input"
                  v-model="searchVal"
                  placeholder="请输入线路名称"
                  @input="searchChange"
                />
                <div class="tips">
                  温馨提示：禁用状态下代表该线路已被排班中使用，不能取消勾选。
                </div>
              </div>
              <pl-no-data
                text="请选择左侧线路类型"
                v-if="lineList.length == 0"
              ></pl-no-data>
              <ul class="line-ul">
                <li
                  v-for="item in lineList"
                  :key="`${item.lineId}-${item.lineTypeCode}`"
                  class="line-li"
                  :title="item.lineName"
                  v-show="item.isShow"
                >
                  <pl-checkbox
                    class="line-checkbox"
                    v-model="item.isChecked"
                    @change="selectLine($event, item)"
                    :disabled="item.isUsedByUserOrVehicle == 1 ? true : false"
                  /><span
                    class="line-code-name"
                    :class="getColor(item.lineTypeCode)"
                  >
                    {{ item.lineTypeName }}
                  </span>
                  <span class="line-name">{{ item.lineName }}</span>
                </li>
              </ul>
            </pl-scrollbar>
          </div>
        </div>
      </template>
    </pl-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getAllLineTypeTree, postLineListByLineTypeCode } from "@/api/travel";
import { getColor } from "@/utils/index";
import _ from "lodash";

const props = defineProps({
  lineId: {
    type: String,
    default: "",
  },
  travelLineId: {
    type: String,
    default: "",
  },
  lineTypeCode: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  userId: {
    type: [String, Number],
    default: "",
  },
  vehicleId: {
    type: [String, Number],
    default: "",
  },
});

const placeholder = ref("请选择所属路线");

// 默认选中的节点
const defaultCheckedKeys = ref([]);

// 公交类型线路回显
// if (props.lineId) {
//   defaultCheckedKeys.value = [props.lineId];
// }

const dialogVisible = ref(false);
const searchVal = ref("");
const selectLineList = ref([]);
import { ElTree } from "element-plus";
const treeData = ref([]);
const defaultProps = ref({
  children: "children",
  label: "lineTypeName",
});

//获取的父级的code
const getParentCode = ref([]);
onMounted(async () => {
  selectLineList.value = [];
  getAllLineTypeTree().then(async (res) => {
    getParentCode.value = res.data.map((item) => item.lineTypeCode);
    treeData.value = res.data;

    if (props.lineTypeCode) {
      placeholder.value = "数据正在加载中...";
      formData.value.lineTypeCode = props.lineTypeCode.split(",");
      defaultCheckedKeys.value = props.lineTypeCode.split(",");
      getLineList(defaultCheckedKeys.value, true);
    }
  });
});

const lineList = ref([]);
const lineListLoading = ref(false);
const formData = ref({});
// 当复选框点击的时候
const treeCheck = _.debounce((node, treeData) => {
  const { checkedKeys } = treeData;
  let lineTypeCode = checkedKeys; // 线路类型编码
  formData.value.lineTypeCode = lineTypeCode;
  // 判断lineTypeCode中是否有getParentCode中的值， 如果有，删除lineTypeCode中的值
  getParentCode.value.forEach((item) => {
    if (lineTypeCode.includes(item)) {
      // 将数组转换为Set进行高效比较和过滤
      lineTypeCode = Array.from(
        new Set(lineTypeCode.filter((code) => code !== item))
      );
    }
  });

  let copySelectLineList = _.cloneDeep(selectLineList.value);

  // 先收集需要删除的索引
  const indexesToRemove = [];
  copySelectLineList.forEach((item) => {
    if (!lineTypeCode.includes(item.lineTypeCode)) {
      const index = selectLineList.value.findIndex(
        (searchItem) =>
          item.lineId === searchItem.lineId &&
          item.lineTypeCode === searchItem.lineTypeCode
      );
      if (index !== -1) {
        indexesToRemove.push(index);
      }
    }
  });
  // 从后往前删除，这样不会影响前面的索引
  indexesToRemove
    .sort((a, b) => b - a)
    .forEach((index) => {
      selectLineList.value.splice(index, 1);
    });
  getLineList(lineTypeCode);
}, 1000);

// 获取线路列表
const getLineList = (lineTypeCode, select) => {
  lineListLoading.value = true;
  postLineListByLineTypeCode({
    lineTypeCode: lineTypeCode.join(","),
    userId: props.userId || "",
    vehicleId: props.vehicleId || "",
  })
    .then((res) => {
      if (select) {
        // 回显选中的线路
        const lineIds = props.lineId
          ? props.lineId.split(",").map((item) => Number(item))
          : "";
        const travelLineIds = props.travelLineId
          ? props.travelLineId.split(",").map((item) => Number(item))
          : "";

        res.data.map((item) => {
          item.isShow = true;
          if (
            lineIds.includes(item.lineId) &&
            item.parentLineTypeCode === "GJLX"
          ) {
            // 公交类型线路
            selectLineList.value.push(item);
            item.isChecked = true;
          }
          if (
            travelLineIds.includes(item.lineId) &&
            item.parentLineTypeCode === "DZLX"
          ) {
            // 定制类型线路
            selectLineList.value.push(item);
            item.isChecked = true;
          }
        });

        lineList.value = res.data;
      } else {
        const selectedMap = new Map(
          selectLineList.value.map((item) => [
            `${item.lineId}-${item.lineTypeCode}`,
            item,
          ])
        );
        lineList.value = res.data.map((item) => {
          const isChecked = selectedMap.has(
            `${item.lineId}-${item.lineTypeCode}`
          );
          return {
            ...item,
            isShow: true,
            isChecked,
          };
        });
      }
    })
    .finally(() => {
      lineListLoading.value = false;
    });
};

// 线路选择事件
const selectLine = (e, item) => {
  if (e) {
    selectLineList.value.push(item);
  } else {
    const index = selectLineList.value.findIndex(
      (searchItem) =>
        item.lineId == searchItem.lineId &&
        item.lineTypeCode == searchItem.lineTypeCode
    );
    if (index !== -1) {
      selectLineList.value.splice(index, 1);
    }
  }
};

// 输入框搜索事件
const searchChange = _.debounce((e) => {
  lineList.value.filter((item) => {
    if (item.lineName.includes(e)) {
      item.isShow = true;
    } else {
      item.isShow = false;
    }
  });
}, 500);

// 获取节点选中的数量
const getNodeSelectedCount = (data) => {
  // 如果是父级节点，则返回子级节点的数量
  if (data.children) {
    return selectLineList.value.filter(
      (item) => item.parentLineTypeCode == data.lineTypeCode
    ).length;
  }

  return selectLineList.value.filter(
    (item) => item.lineTypeCode == data.lineTypeCode
  ).length;
};
const emit = defineEmits([
  "change",
  "update:lineId",
  "update:travelLineId",
  "update:lineTypeCode",
]);
// 确认事件
const confirm = () => {
  let lineId = [];
  let travelLineId = [];
  // 遍历分开公交类型和定制类型
  selectLineList.value.forEach((item) => {
    if (item.parentLineTypeCode == "GJLX") {
      lineId.push(item.lineId);
    } else {
      travelLineId.push(item.lineId);
    }
  });
  formData.value.lineId = lineId.join(",");
  formData.value.travelLineId = travelLineId.join(",");
  formData.value.lineTypeCode = formData.value.lineTypeCode.join(",");
  console.log(formData.value, "formData.value");
  dialogVisible.value = false;
  emit("update:lineId", formData.value.lineId);
  emit("update:travelLineId", formData.value.travelLineId);
  emit("update:lineTypeCode", formData.value.lineTypeCode);
  emit("change", formData.value);
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  .num {
    background: #ff504d;
    color: #fff;
    border-radius: 5px;
    padding: 2px 5px;
    margin-left: 5px;
    font-size: 12px;
  }
}
.dialog-content {
  max-height: 80vh;
  height: 80vh;
}
.line-checkbox {
  margin-right: 5px;
}
.line-code-name {
  font-size: 12px;
  padding: 2px 5px;
  border-radius: 5px;
  margin-right: 5px;
  min-width: 60px;
  text-align: center;
}
.line-ul {
  display: flex;
  flex-wrap: wrap;
  margin-top: 15px;
  .line-li {
    width: 20%;
    display: flex;
    align-items: center;
    padding-right: 5px;
    .line-name {
      white-space: nowrap;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.search-input {
  width: 200px;
}
.dialog-content {
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  display: flex;
  .cont-right {
    flex: 1;
    border-left: 1px solid var(--el-border-color);
    padding: 10px;
  }
  .cont-left {
    width: 200px;
  }
}
.belonging-line {
  width: 100%;
}
.belong-input {
  align-items: center;
  background-color: var(--el-fill-color-blank);
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  gap: 6px;
  line-height: 24px;
  min-height: 32px;
  padding: 4px 12px;
  position: relative;
  text-align: left;
  transform: translateZ(0);
  transition: var(--el-transition-duration);
}

.line-type-name {
  padding: 3px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  line-height: 12px;
}
.search-box-content {
  display: flex;
  justify-content: space-between;
  .tips {
    font-size: 12px;
    color: #ff504d;
    margin-top: 10px;
  }
}
</style>
