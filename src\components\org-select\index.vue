<template>
  <plTreeSelectPro
    :cascaderProps="cascaderProps"
    :options="options"
    v-model="selectVal"
    @change="change"
    :multiple="false"
    placeholder="请选择所属机构"
    :disabled="disabled"
  ></plTreeSelectPro>
</template>

<script setup>
import { ref, watch } from "vue";
import { getParentOrgTreeList } from "@/api/index";

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    deault: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
watch(
  () => props.disabled,
  (val) => {
    console.log("val", val);
  }
);
const selectVal = ref("");
const options = ref([]);
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      selectVal.value = val;
    } else {
      selectVal.value = [];
    }
  }
);

const cascaderProps = {
  label: "orgName",
  value: "id",
  children: "children",
  checkStrictly: true,
};

// 请求接口
getParentOrgTreeList().then((res) => {
  options.value = res.data;
  selectVal.value = props.modelValue;
});

const emit = defineEmits(["change", "update:modelValue"]);
const change = (val, val2) => {
  console.log("val", val);
  emit("update:modelValue", val);
  emit("change", val2);
};
</script>

<style lang="scss" scoped></style>
