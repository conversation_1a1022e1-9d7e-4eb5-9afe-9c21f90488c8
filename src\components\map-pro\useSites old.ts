import { getDepotList } from "@/api/index";
import dwGj from "../../assets/img/dw-gj.png";
import dwGjActive from "../../assets/img/dw-gj-active.png";
import { reactive, ref } from "vue";
import { emit } from "@/utils/eventBus";
const siteList = ref<any[]>([]); // 站点列表
const visibleSiteMarkers = ref<any[]>([]); // 存储当前视图内可见的站点标记
const lastBounds = ref<any>(null); // 记录上次加载的地图边界
const isLoading = ref(false); // 防止重复加载
const loadThreshold = 0.3; // 当视图变化超过30%时重新加载
const markers = ref<any[]>([]); // 存储所有marker
const selectArray = ref<any[]>([]); // 存储选中站点
let leftSitesArray = reactive<any[]>([]); // 存储搜索站点列表
let mapBase = reactive<any>(null); // 存储地图实例
let props = reactive<any>(null);

// 添加新的处理函数
const handleMarkerClick = (marker: any) => {
  const markerData = marker.getExtData();
  const index = selectArray.value.findIndex(
    (item) => item.stopId === markerData.stopId
  );
  if (index === -1) {
    // 选中
    selectArray.value.push(markerData);
    marker.setIcon(
      new mapBase.AMap.Icon({
        image: dwGjActive,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      })
    );
    marker.setzIndex(99999);
  } else {
    // 取消选中
    selectArray.value.splice(index, 1);
    marker.setIcon(
      new mapBase.AMap.Icon({
        image: dwGj,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      })
    );
    marker.setzIndex(marker.getExtData().location ? 8 : 999);
  }
  // 更新序号
  updateSort(markerData);

  // 触发事件，会返还已选中的站点
  useSiteClick();
  emit("markerClick", selectArray.value);
};
// 设置站点标题
const labelContent = (name: any, type: any, sort?: number) => {
  let typeText = "";
  switch (type) {
    case "gd":
      typeText = "<span style='color:red'>高德</span>";
      break;
    case "site":
      typeText = "<span style='color:blue'>站点库</span>";
      break;
    case "both":
      typeText = "<span style='color:purple'>高德+站点库</span>";
      break;
    default:
      typeText = "";
  }

  if (sort) {
    return `<span class="marker-label"><span class="sort">${sort}</span>${name}-${typeText}</span>`;
  } else {
    return `<span class="marker-label">${name}-${typeText}</span>`;
  }
};

// 绘制站点
export const updateVisibleSites = () => {
  if (!props.sitesLibrary && siteList.value.length == 0) return;
  isLoading.value = true;
  const bounds = mapBase.map.value.getBounds();
  lastBounds.value = bounds;

  // 创建一个新数组来存储当前视图内的标记
  const currentVisibleMarkers: any[] = [];

  // 清除当前视图外的站点标记
  visibleSiteMarkers.value.forEach((marker: any) => {
    const position = marker.getPosition();
    if (!bounds.contains(position)) {
      mapBase.map.value.remove(marker);
      const index = markers.value.indexOf(marker);
      if (index > -1) {
        markers.value.splice(index, 1);
      }
    } else {
      // 如果标记仍在视图内，保留它
      currentVisibleMarkers.push(marker);
    }
  });

  // 更新可见标记数组
  visibleSiteMarkers.value = currentVisibleMarkers;

  // 添加新的可见站点
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();
  const newVisibleSites = siteList.value.filter((site) => {
    return (
      site.latitude <= ne.lat &&
      site.latitude >= sw.lat &&
      site.longitude <= ne.lng &&
      site.longitude >= sw.lng &&
      // 确保该站点还没有对应的标记
      !visibleSiteMarkers.value.some(
        (marker) => marker.getExtData().stopId === site.stopId
      )
    );
  });

  newVisibleSites.forEach((site) => {
    // if (
    //   selectArray.value.find((selectSite) => selectSite.stopId == site.stopId)
    // ) {
    //   return;
    // }

    const isSelected = site.selected || site.checked;

    const marker = new mapBase.AMap.Marker({
      position: [site.longitude, site.latitude],
      icon: new mapBase.AMap.Icon({
        image: isSelected ? dwGjActive : dwGj,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      }),
      zIndex: 999,
      anchor: "bottom-center",
      label: {
        content: labelContent(site.stopName, "site"),
        direction: "top",
        offset: [0, -5],
      },
      extData: { ...site, type: "site" },
    });

    marker.on("click", () => handleMarkerClick(marker));
    mapBase.map.value.add(marker);
    markers.value.push(marker);
    visibleSiteMarkers.value.push(marker);
    // 如果站点是选中状态，则需要更新序号
    updateSort(site);
  });

  isLoading.value = false;
};

// 主函数
export async function useSites(obj: any) {
  visibleSiteMarkers.value = [];
  props = obj.defineProps;
  mapBase = obj.mapDom;
  leftSitesArray = obj.leftSitesArray?.value;
  selectArray.value = leftSitesArray.filter((item) => item.checked);
  if (obj.renderSiteToMap) {
    // 渲染站点到地图
    siteList.value = obj.renderSiteToMap;
  } else if (props.sitesLibrary) {
    // 获取站点列表
    const res = await getDepotList({});
    siteList.value = res.data;
  }

  // 添加新的函数用于处理地图移动结束事件
  const handleMapMoveEnd = () => {
    if (isLoading.value) return;

    const currentBounds = mapBase.map.value.getBounds();

    // 如果是首次加载或视图变化超过阈值，则更新站点
    if (!lastBounds.value || shouldUpdateSites(currentBounds)) {
      updateVisibleSites();
    }
  };

  // 添加判断是否需要更新站点的函数
  const shouldUpdateSites = (currentBounds: any) => {
    if (!lastBounds.value) return true;

    const { lat: currentNELat, lng: currentNELng } =
      currentBounds.getNorthEast();
    const { lat: lastNELat, lng: lastNELng } = lastBounds.value.getNorthEast();
    const { lat: lastSWLat, lng: lastSWLng } = lastBounds.value.getSouthWest();

    return (
      Math.abs((currentNELat - lastNELat) / (lastNELat - lastSWLat)) >
        loadThreshold ||
      Math.abs((currentNELng - lastNELng) / (lastNELng - lastSWLng)) >
        loadThreshold
    );
  };

  updateVisibleSites();
  // 添加地图移动结束事件监听
  mapBase.map.value.on("moveend", handleMapMoveEnd);

  return {
    selectArray,
  };
}

// 选择站点
export const chooseSite = (item: any) => {
  const marker = markers.value.find((markerItem) => {
    return markerItem.getExtData().stopId === item.stopId;
  });
  if (marker) {
    handleMarkerClick(marker);
  } else {
    // 可能选中的站点不在地图上，
    if (item.checked) {
      selectArray.value.push(item);
    } else {
      const index = selectArray.value.findIndex(
        (select) => select.stopId == item.stopId
      );
      selectArray.value.splice(index, 1);
    }
    useSiteClick();
    emit("markerClick", selectArray.value);
  }
};

// 更新序号
export const updateSort = (siteItem: any) => {
  // 如果不需要显示序号，则直接返回
  if (!props.siteSort) return;

  const index = selectArray.value.findIndex(
    (item) => item.stopId === siteItem.stopId
  );

  if (index === -1) {
    // 取消选中
    const marker = markers.value.find((markerItem) => {
      return markerItem.getExtData().stopId === siteItem.stopId;
    });
    if (marker) {
      marker.setLabel({
        content: labelContent(siteItem.stopName, "site"),
      });
    }
  }

  // 更新选中的序号
  selectArray.value.forEach((item, index) => {
    const marker = markers.value.find((markerItem) => {
      return markerItem.getExtData().stopId === item.stopId;
    });
    if (marker) {
      marker.setLabel({
        content: labelContent(item.stopName, "site", index + 1),
      });
    }
  });
};

// 站点自身点击同步左侧列表选中状态
export const useSiteClick = () => {
  // 重置站点选中状态
  leftSitesArray.forEach((item) => {
    item.checked = false;
  });

  // 先找出所有未选中的站点
  const unselectedSites = leftSitesArray.filter(
    (site) =>
      !selectArray.value.find((selected) => selected.stopId === site.stopId)
  );

  // 根据 selectArray 的顺序重新排序选中的站点
  const selectedSites = selectArray.value
    .map((selected) => {
      const site = leftSitesArray.find(
        (item) => item.stopId === selected.stopId
      );
      if (site) {
        site.checked = true;
        return site;
      }
    })
    .filter(Boolean); // 过滤掉可能的 undefined

  // 重新组合数组: 选中的站点在前(按selectArray顺序),未选中的在后
  leftSitesArray.length = 0;
  leftSitesArray.push(...selectedSites, ...unselectedSites);
};

// 清除地图上的所有marker
export const clearMarkers = () => {
  markers.value.forEach((marker) => {
    mapBase.map.value.remove(marker);
  });
  markers.value = [];
  visibleSiteMarkers.value = [];
  updateVisibleSites();
};

// 根据marker移动地图
export const moveMapByMarker = () => {
  siteList.value.forEach((site) => {
    const isSelected = site.selected || site.checked;
    const marker = new mapBase.AMap.Marker({
      position: [site.longitude, site.latitude],
      icon: new mapBase.AMap.Icon({
        image: isSelected ? dwGjActive : dwGj,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      }),
      zIndex: 999,
      anchor: "bottom-center",
      label: {
        content: labelContent(site.stopName, "site"),
        direction: "top",
        offset: [0, -5],
      },
      extData: { ...site, type: "site" },
    });

    marker.on("click", () => handleMarkerClick(marker));
    mapBase.map.value.add(marker);
    markers.value.push(marker);
    visibleSiteMarkers.value.push(marker);
  });
  mapBase.map.value.setFitView(markers.value, true);
};
