{"root": ["./src/main.ts", "./src/shims-vue.d.ts", "./src/vite-env.d.ts", "./src/api/dict.ts", "./src/api/index.ts", "./src/api/request.ts", "./src/hooks/usettable.ts", "./src/router/index.ts", "./src/stores/index.ts", "./src/types/index..ts", "./src/types/pls-common.d.ts", "./src/utils/index.ts", "./src/app.vue", "./src/components/loadingpage.vue", "./src/components/header/index.vue", "./src/components/institution/index.vue", "./src/components/layout/main.vue", "./src/components/menu/index.vue", "./src/components/module/drawer-from.vue", "./src/components/module/drawer.vue", "./src/components/module/resource-permissions.vue", "./src/components/module/tree-x.vue", "./src/components/nav-tag/index copy.vue", "./src/components/nav-tag/index.vue", "./src/views/dashboard/index.vue", "./src/views/dict/code.vue", "./src/views/dict/index.vue", "./src/views/dict/module/add-child.vue", "./src/views/dict/module/add.vue", "./src/views/login/index.vue", "./src/views/organize/administered/index.vue", "./src/views/organize/employee-mgt/index.vue", "./src/views/organize/module/add-org.vue", "./src/views/organize/module/authorization.vue", "./src/views/organize/post-mgt/index.vue", "./src/views/organize/resource/index.vue", "./src/views/organize/role-management/index.vue", "./src/views/system/app/index.vue", "./src/views/system/menu/index.vue", "./src/views/system/plan/index.vue", "./src/views/tenant-mgt/index.vue"], "version": "5.6.2"}