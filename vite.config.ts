import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
// 添加 compression 插件用于 Gzip 压缩
import viteCompression from "vite-plugin-compression";
import qiankun from "vite-plugin-qiankun";

// https://vitejs.dev/config/
export default ({ mode }: any) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());
  // 打印所有环境变量
  console.log("所有环境变量:", env);

  // 单独打印 base 配置
  console.log("当前环境:", mode);
  return defineConfig({
    server: {
      host: "0.0.0.0", // 使用你的局域网IP或者特殊值'0.0.0.0'以便外部设备可以通过IP访问
      port: 5003, // 你的端口号，如果你想要的端口
      proxy: {
        "/Cgo8": {
          target: "https://www.cqbeidao.com",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
        "/TopMap": {
          target: "https://www.cqbeidao.com",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
        "/topmap": {
          target: "https://www.cqbeidao.com",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
        "/fonts": {
          target: "https://bucket-polaris-prod.xinan1.zos.ctyun.cn",
          changeOrigin: true,
          secure: false,
        },
      },
      origin: "http://localhost:5003",
    },
    optimizeDeps: {
      include: ["pls-common"],
    },
    build: {
      rollupOptions: {
        output: {
          // 优化代码分割策略
          manualChunks: {
            // 将 Vue 相关库打包在一起
            "vue-vendor": ["vue", "vue-router", "pinia"],
            // 将其他第三方库打包在一起
            vendor: ["axios", "lodash" /* 其他常用库 */],
          },
          // 根据文件类型拆分
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
        },
      },
      // 设置 chunk 大小警告的限制
      chunkSizeWarningLimit: 2000,
      sourcemap: false, // 生产环境关闭 sourcemap
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ["console.log", "console.info"], // 删除指定的函数调用
        },
        output: {
          // 压缩输出
          comments: false,
        },
      },
      // 启用 CSS 代码分割
      cssCodeSplit: true,
      // 设置资源文件体积警告阈值
      assetsInlineLimit: 4096,
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
        },
      },
    },
    define: {
      "process.env": {},
    },
    base: env.VITE_APP_URL,
    plugins: [
      vue(),
      qiankun("tenant", {
        useDevMode: mode == "development" ? true : false,
      }),
      // 添加 gzip 压缩插件
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: "gzip",
        ext: ".gz",
      }),
    ],
  });
};
