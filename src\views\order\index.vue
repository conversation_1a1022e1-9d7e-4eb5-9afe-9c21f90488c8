<template>
  <div class="order-list-big">
    <pl-drag-range :initialLeftWidth="900">
      <template #left>
        <orderList @rowClick="orderRowClick"></orderList>
      </template>
      <template #right>
        <orderReceipt :orderData="orderData"></orderReceipt>
      </template>
    </pl-drag-range>
  </div>
</template>

<script setup>
import { ref } from "vue";
import orderList from "./order-list.vue";
import orderReceipt from "./order-receipt.vue";
const orderData = ref({});
const orderRowClick = (e) => {
  orderData.value = e;
};
</script>

<style lang="scss" scoped>
.order-list-big {
  background: #fff;
}
</style>
