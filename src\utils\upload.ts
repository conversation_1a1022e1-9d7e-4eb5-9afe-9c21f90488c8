import { getOssConfig } from "@/api";
import { initOSSConfig } from "pls-common";

getOssConfig().then(async (res) => {
  const bucket = res;
  const BUCKET_NET = bucket.endpoint.split("//"); //	bucket外网地址
  const BUCKET_DOMAIN =
    BUCKET_NET[0] + `//${bucket.bucketName}.` + BUCKET_NET[1];
  initOSSConfig({
    accessKeyId: bucket.accessKey,
    secretAccessKey: bucket.secretKey,
    endpoint: bucket.endpoint,
    bucketDomain: BUCKET_DOMAIN,
    bucketName: bucket.bucketName,
    region: "default-region",
    ACL: "public-read",
  });
  // console.table({
  //   accessKeyId: bucket.accessKey,
  //   secretAccessKey: bucket.secretKey,
  //   endpoint: bucket.endpoint,
  //   bucketDomain: BUCKET_DOMAIN,
  //   bucketName: bucket.bucketName,
  //   region: "default-region",
  //   ACL: "public-read",
  // });
});
