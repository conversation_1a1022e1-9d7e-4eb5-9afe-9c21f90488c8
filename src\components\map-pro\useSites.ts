// import { getDepotList } from "@/api/index";
import dwGj from "../../assets/img/dw-gj.png";
import dwGjActive from "../../assets/img/dw-gj-active.png";
import upCar from "../../assets/img/upCar.png";
import downCar from "../../assets/img/downCar.png";

import { reactive, ref } from "vue";
import { emit } from "@/utils/eventBus";
const siteList = ref<any[]>([]); // 站点列表
const visibleSiteMarkers = ref<any[]>([]); // 存储当前视图内可见的站点标记
const lastBounds = ref<any>(null); // 记录上次加载的地图边界
const isLoading = ref(false); // 防止重复加载
const loadThreshold = 0.3; // 当视图变化超过30%时重新加载
const markers = ref<any[]>([]); // 存储所有marker
const selectArray = ref<any[]>([]); // 存储选中站点
let leftSitesArray = reactive<any[]>([]); // 存储搜索站点列表
let mapBase = reactive<any>(null); // 存储地图实例
let props = reactive<any>(null);

// 未选icon
const unSelectedIcon = ref(dwGj);
// 选中icon
const selectedIcon = ref(dwGjActive);

// 添加新的处理函数
const handleMarkerClick = (marker: any) => {
  // 如果禁止点击，直接返回
  if (!props?.allowClick) {
    return;
  }

  const markerData = marker.getExtData();
  const index = selectArray.value.findIndex(
    (item) => item.stopId === markerData.stopId
  );

  // 更新选中状态
  markerData.checked = index === -1;

  if (markerData.checked) {
    // 选中点
    selectArray.value.push(markerData);
    marker.setIcon(
      new mapBase.AMap.Icon({
        image: selectedIcon.value,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      })
    );
    marker.setzIndex(99999);
  } else {
    // 取消选中
    selectArray.value.splice(index, 1);
    marker.setIcon(
      new mapBase.AMap.Icon({
        image: unSelectedIcon.value,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      })
    );
    marker.setzIndex(999);
  }

  updateSort(markerData);
  // 传递包含选中状态的数据
  emit("markerClick", [...selectArray.value]);
};

// 设置站点标题
const labelContent = (name: any, type: any, sort?: any) => {
  let typeText = "";
  switch (type) {
    case "gd":
      typeText = "<span style='color:red'>高德</span>";
      break;
    case "site":
      typeText = "<span style='color:blue'>站点库</span>";
      break;
    case "both":
      typeText = "<span style='color:purple'>高德+站点库</span>";
      break;
    default:
      typeText = "";
  }

  if (sort) {
    return `<span class="marker-label"><span class="sort">${sort}</span>${name}-${typeText}</span>`;
  } else {
    return `<span class="marker-label">${name}-${typeText}</span>`;
  }
};

// 绘制站点
export const updateVisibleSites = (siteList: any[]) => {
  if (!mapBase?.map?.value) return;

  clearMarkers();

  isLoading.value = true;
  const bounds = mapBase.map.value.getBounds();
  lastBounds.value = bounds;

  // 创建一个新数组来存储当前视图内的标记
  const currentVisibleMarkers: any[] = [];

  // 清除当前视图外的站点标记
  visibleSiteMarkers.value.forEach((marker: any) => {
    const position = marker.getPosition();
    if (!bounds.contains(position)) {
      mapBase.map.value.remove(marker);
      const index = markers.value.indexOf(marker);
      if (index > -1) {
        markers.value.splice(index, 1);
      }
    } else {
      currentVisibleMarkers.push(marker);
    }
  });

  visibleSiteMarkers.value = currentVisibleMarkers;

  // 添加新的可见站点
  const ne = bounds.getNorthEast();
  const sw = bounds.getSouthWest();
  const newVisibleSites = siteList.filter((site: any) => {
    return (
      site.latitude <= ne.lat &&
      site.latitude >= sw.lat &&
      site.longitude <= ne.lng &&
      site.longitude >= sw.lng &&
      !visibleSiteMarkers.value.some(
        (marker) => marker.getExtData().stopId === site.stopId
      )
    );
  });

  newVisibleSites.forEach((site: any) => {
    // 检查是否在选中数组中
    const isSelected = selectArray.value.some(
      (selected) => selected.stopId === site.stopId
    );
    // console.log("site", site);

    const marker = new mapBase.AMap.Marker({
      position: [site.longitude, site.latitude],
      icon: new mapBase.AMap.Icon({
        image: isSelected ? selectedIcon.value : unSelectedIcon.value,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      }),
      zIndex: isSelected ? 99999 : 999,
      anchor: "bottom-center",
      label: {
        content: labelContent(site.stopName, "site"),
        direction: "top",
        offset: [0, -5],
      },
      extData: { ...site, type: "site", checked: isSelected },
    });

    // 判断上车点还是下车点
    if (site.startEnd === 0) {
      marker.setIcon(
        new mapBase.AMap.Icon({
          image: upCar,
          size: new mapBase.AMap.Size(34, 34),
          imageSize: new mapBase.AMap.Size(34, 34),
        })
      );
    } else if (site.startEnd === 1) {
      marker.setIcon(
        new mapBase.AMap.Icon({
          image: downCar,
          size: new mapBase.AMap.Size(34, 34),
          imageSize: new mapBase.AMap.Size(34, 34),
        })
      );
    }

    marker.on("click", () => handleMarkerClick(marker));
    mapBase.map.value.add(marker);
    markers.value.push(marker);
    visibleSiteMarkers.value.push(marker);
  });
  updateSort();

  isLoading.value = false;
};

export const setImgIcon = (icon1?: any, icon2?: any) => {
  if (icon1) {
    selectedIcon.value = icon1;
  }
  if (icon2) {
    unSelectedIcon.value = icon2;
  }
};

// 设置地图站点
export const setSiteToMap = (list: any[]) => {
  visibleSiteMarkers.value = [];
  lastBounds.value = null;
  siteList.value = list;
  // 添加新的函数用于处理地图移动结束事件
  const handleMapMoveEnd = () => {
    if (isLoading.value) return;

    const currentBounds = mapBase.map.value.getBounds();

    // 如果是首次加载或视图变化超过阈值，则更新站点
    if (!lastBounds.value || shouldUpdateSites(currentBounds)) {
      updateVisibleSites(siteList.value);
    }
  };

  // 添加判断是否需要更新站点的函数
  const shouldUpdateSites = (currentBounds: any) => {
    if (!lastBounds.value) return true;

    const { lat: currentNELat, lng: currentNELng } =
      currentBounds.getNorthEast();
    const { lat: lastNELat, lng: lastNELng } = lastBounds.value.getNorthEast();
    const { lat: lastSWLat, lng: lastSWLng } = lastBounds.value.getSouthWest();

    return (
      Math.abs((currentNELat - lastNELat) / (lastNELat - lastSWLat)) >
        loadThreshold ||
      Math.abs((currentNELng - lastNELng) / (lastNELng - lastSWLng)) >
        loadThreshold
    );
  };
  // 添加地图移动结束事件监听
  const timer = setInterval(() => {
    if (mapBase?.map.value) {
      clearInterval(timer);
      mapBase.map.value.on("moveend", handleMapMoveEnd);
      selectArray.value = siteList.value.filter(
        (site) => site.checked || site.isSelected === 1
      );
      updateVisibleSites(siteList.value);
    }
  }, 300);
};

// 主函数
export async function useSites(obj: any) {
  if (mapBase?.map?.value) {
    markers.value.forEach((marker) => {
      mapBase.map.value.remove(marker);
    });
  }

  selectArray.value = []; // 重置选中数组
  props = obj.defineProps;
  mapBase = obj.mapDom;

  if (!mapBase?.map?.value) return;
}

// 选择站点
export const chooseSite = (item: any) => {
  // 找到所有匹配的地图标记
  const matchedMarkers = markers.value.filter((markerItem) => {
    return markerItem.getExtData().stopId === item.stopId;
  });

  // 更新选中数组
  const index = selectArray.value.findIndex(
    (select) => select.stopId === item.stopId
  );
  if (item.checked && index === -1) {
    selectArray.value.push(item);
  } else if (!item.checked && index > -1) {
    selectArray.value.splice(index, 1);
  }

  // 更新 siteList 中的状态
  siteList.value.forEach((site) => {
    if (site.stopId === item.stopId) {
      site.checked = item.checked;
      site.isSelected = item.checked ? 1 : 0;
    }
  });

  // 更新可见的标记
  if (matchedMarkers.length > 0) {
    matchedMarkers.forEach((marker) => {
      const markerData = marker.getExtData();
      markerData.checked = item.checked;
      markerData.isSelected = item.checked ? 1 : 0;

      // 更新图标
      marker.setIcon(
        new mapBase.AMap.Icon({
          image: item.checked ? selectedIcon.value : unSelectedIcon.value,
          size: new mapBase.AMap.Size(34, 34),
          imageSize: new mapBase.AMap.Size(34, 34),
        })
      );
      marker.setzIndex(item.checked ? 99999 : 999);
    });
  }

  updateSort(item);
  // 发送更新后的选中数组
  emit("markerClick", selectArray.value);
};

// 更新序号
export const updateSort = (siteItem?: any) => {
  if (!props.siteSort) return;

  if (siteItem) {
    const index = selectArray.value.findIndex(
      (item) => item.stopId === siteItem.stopId
    );

    if (index === -1) {
      const marker = markers.value.find((markerItem) => {
        return markerItem.getExtData().stopId === siteItem.stopId;
      });
      if (marker) {
        marker.setLabel({
          content: labelContent(siteItem.stopName, "site"),
        });
      }
    }
  }

  selectArray.value.forEach((item, index) => {
    const marker = markers.value.find((markerItem) => {
      return markerItem.getExtData().stopId === item.stopId;
    });
    if (marker) {
      marker.setLabel({
        content: labelContent(item.stopName, "site", index + 1),
      });
    }
  });
};

// 站点自身点击同步左侧列表选中状态
export const useSiteClick = () => {
  if (props.synchronous) {
    // 编辑模式：保持所有点选中
    leftSitesArray.forEach((item) => {
      item.checked = true;
    });
    return;
  }

  // 新增模式：只显示选中的点
  leftSitesArray = selectArray.value.map((site) => ({
    ...site,
    checked: true,
  }));
};

// 清除地图上的所有marker
export const clearMarkers = () => {
  markers.value.forEach((marker) => {
    mapBase.map.value.remove(marker);
  });
  markers.value = [];
  visibleSiteMarkers.value = [];
};

// 根据marker移动地图
export const moveMapByMarker = () => {
  siteList.value.forEach((site, index) => {
    const isSelected = site.selected || site.checked;
    const marker = new mapBase.AMap.Marker({
      position: [site.longitude, site.latitude],
      icon: new mapBase.AMap.Icon({
        image: isSelected ? selectedIcon.value : unSelectedIcon.value,
        size: new mapBase.AMap.Size(34, 34),
        imageSize: new mapBase.AMap.Size(34, 34),
      }),
      zIndex: 999,
      anchor: "bottom-center",
      label: {
        content: labelContent(site.stopName, "site", index + 1),
        direction: "top",
        offset: [0, -5],
      },
      extData: { ...site, type: "site" },
    });

    marker.on("click", () => handleMarkerClick(marker));
    mapBase.map.value.add(marker);
    markers.value.push(marker);
    visibleSiteMarkers.value.push(marker);
  });
  mapBase.map.value.setFitView(markers.value, true);
};
