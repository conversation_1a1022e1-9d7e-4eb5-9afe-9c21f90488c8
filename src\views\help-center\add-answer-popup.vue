<!-- 新建问答 -->
<template>
  <div class="add-answer-popup">
    <div class="left-box">
      <div class="title">
        <pl-input v-model="formData.name" placeholder="请输入标题" />
        <Editor v-model="formData.content"></Editor>
      </div>
    </div>
    <div class="right-box">
      <pl-form :fields="formColumns" ref="formRef" :form="formData"></pl-form>
    </div>

    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <!-- <pl-button class="btn">确定并新增</pl-button> -->
      <pl-button class="btn" type="primary" @click="handleSubmit"
        >确定</pl-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import Editor from "@/components/editor/editor.vue";
import { plMessage } from "pls-common";
const emit = defineEmits(["close", "submit"]);
const formRef = ref(null);
const props = defineProps({
  categoryList: {
    type: Array,
    default: () => [],
  },
  appList: {
    type: Array,
    default: () => [],
  },
  from: {
    type: Object,
    default: () => {},
  },
  labelList: {
    type: Array,
    default: () => [],
  },
});

const formData = ref({
  ...props.from,
});
const formColumns = ref([
  {
    label: "所属应用",
    type: "select",
    prop: "appCode",
    options: props.appList,
    valueKey: "appCode",
    labelKey: "appName",
    rules: [
      {
        required: true,
        message: "请选择所属应用",
      },
    ],
  },
  {
    label: "所属类别",
    prop: "qaCategoryId",
    type: "tree-select-pro",
    options: props.categoryList,
    multiple: false,
    cascaderProps: {
      label: "categoryName",
      value: "id",
      children: "children",
    },
    nodeKey: "id",
    rules: [
      {
        required: true,
        message: "请选择所属类别",
        trigger: "change",
      },
    ],
    onChange: (val) => {
      console.log(val);
    },
  },
  {
    label: "摘要",
    type: "textarea",
    prop: "summary",
  },
  {
    label: "标签",
    type: "label-select",
    prop: "label",
    options: props.labelList,
    cascaderProps: {
      label: "labelName",
      value: "labelId",
    },
    onChange: (val) => {
      console.log(formData.value);
      console.log(val);
    },
  },
  {
    label: "状态",
    type: "radio",
    prop: "status",
    rules: [
      {
        required: true,
        message: "请选择状态",
      },
    ],
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
  {
    label: "封面",
    type: "upload",
    prop: "coverPath",
    uploadType: "image",
    count: 1,
  },
]);
const handleClose = () => {
  emit("close");
};
const handleSubmit = () => {
  if (!formData.value.name) {
    plMessage("请输入标题", "warning");
    return;
  }
  if (formData.value.content === "<p><br></p>") {
    plMessage("请输入内容", "warning");
    return;
  }

  if (formData.value.summary?.length > 50) {
    plMessage("摘要不能超过50个字", "warning");
    return;
  }

  formRef.value.confirm((data) => {
    emit("submit", data);
  });
};
</script>

<style lang="scss" scoped>
.editor-box {
  border: 1px solid var(--el-border-color);
  margin-top: 10px;
}
.add-answer-popup {
  display: flex;
  height: 100%;
  padding-bottom: 100px;
  .left-box {
    flex: 1;
    padding-right: 20px;
  }
  .right-box {
    width: 300px;
    height: 100%;
  }
}
</style>
