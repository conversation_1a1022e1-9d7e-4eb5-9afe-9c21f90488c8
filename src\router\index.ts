import { createRouter, createWeb<PERSON>istory } from "vue-router";
import { storage, plMessageBox } from "pls-common";
import { useNavtagsStore } from "@/stores";
import { logout } from "@/utils";
import { getCookie } from "@/utils/cookie";
import { qiankunWindow } from "vite-plugin-qiankun/dist/helper";
const { MODE } = import.meta.env;
const routes: any = [
  {
    path: "/",
    name: "dashboard",
  },
  {
    path: "/iframe",
    component: () => import("@/views/iframe/iframe.vue"),
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
  },
  {
    path: "/404",
    component: () => import("@/views/error/404.vue"),
    meta: {
      title: "404",
    },
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
  },
  ...(MODE === "development"
    ? [
        {
          path: "/usermanage",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "用户管理",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/user-manage/user-manage.vue"),
              meta: {
                title: "用户管理",
              },
            },
          ],
        },
        {
          path: "/notice",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "公告管理",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/notice/notice.vue"),
              meta: {
                title: "公告管理",
              },
            },
          ],
        },
        {
          path: "/actualreceiptrecord",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "实收记录",
          },
          children: [
            {
              path: "list",
              component: () =>
                import("@/views/finance/actual-receipt-record.vue"),
              meta: {
                title: "实收记录",
              },
            },
          ],
        },
        {
          path: "/tuigaiqian",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "退改签",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/order/order-examine.vue"),
              meta: {
                title: "退改签",
              },
            },
          ],
        },
        {
          path: "/arrecordsrecord",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "应收记录",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/finance/ar-records.vue"),
              meta: {
                title: "应收记录",
              },
            },
            {
              path: "refund",
              component: () => import("@/views/finance/pay-record.vue"),
              meta: {
                title: "退款记录",
              },
            },
          ],
        },
        {
          path: "/invoice",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "发票管理",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/invoice/invoice.vue"),
              meta: {
                title: "发票管理",
              },
            },
          ],
        },
        {
          path: "/balance",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "结算清单",
          },
          children: [
            {
              path: "list",
              component: () => import("@/views/balance/balance.vue"),
              meta: {
                title: "结算清单",
              },
            },
          ],
        },
        {
          path: "/message",
          component: () => import("@/components/layout/main.vue"),
          meta: {
            icon: "HomeFilled",
            level: 1,
            title: "消息管理",
          },
          children: [
            {
              path: "template",
              component: () => import("@/views/message/messageTemp.vue"),
              meta: {
                title: "消息模板",
              },
            },
            {
              path: "push",
              component: () => import("@/views/message/messagePush.vue"),
              meta: {
                title: "消息推送",
              },
            },
          ],
        },
      ]
    : []),
];

const baseUrl = qiankunWindow.__POWERED_BY_QIANKUN__ ? "/mocs" : "";

const router = createRouter({
  history: createWebHistory(baseUrl),
  routes,
});

const noPushRouters = ["/login", "/dashboard", "/404"];

// 定义一个函数来检查版本
function checkVersion() {
  let versionUrl = "";
  // 需要判断是否在qiankun环境下
  if (qiankunWindow.__POWERED_BY_QIANKUN__) {
    versionUrl =
      qiankunWindow.__INJECTED_PUBLIC_PATH__ +
      "/version.json?t=" +
      new Date().getTime();
  } else {
    versionUrl = "/version.json?t=" + new Date().getTime();
  }

  fetch(versionUrl) // 从 public/version.json 获取版本信息
    .then((response) => response.json())
    .then((data) => {
      const currentVersion = storage.getItem("appVersion");
      if (currentVersion && currentVersion !== data.version) {
        plMessageBox
          .confirm("检测到系统已有新版本，请更新！", "系统升级", {
            confirmButtonText: "立即更新",
            "show-close": false,
            showCancelButton: false,
          })
          .then(() => {
            storage.setItem("appVersion", data.version);
            logout();
          })
          .catch(() => {
            console.log("取消删除");
          });
      } else {
        storage.setItem("appVersion", data.version);
      }
    })
    .catch((error) => console.error("版本检查失败:", error));
}

// 在路由守卫中调用版本检查
router.beforeEach((to: any, _from: any, next: any) => {
  const perms = to.meta.perms;
  if (perms) {
    storage.setItem("scopeCode", perms);
  }
  // checkVersion(); // 添加版本检查

  let user = "";
  // 读取 Cookie 并解析为对象
  const userCookie: string | null = getCookie("userInfo");
  if (userCookie) {
    user = JSON.parse(userCookie);
  }

  if (!user && to.path !== "/login") {
    next("/login");
  } else {
    if (to.path === "/") {
      const routeOne = router.getRoutes()[0];
      next(routeOne.path);
    } else {
      if (noPushRouters.indexOf(to.path) == -1) {
        const navtagsStore: any = useNavtagsStore();
        navtagsStore.addTag(to);
      }
      next();
    }
  }
});

export default router;
