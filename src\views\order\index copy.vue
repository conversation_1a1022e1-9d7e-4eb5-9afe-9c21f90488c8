<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索1"
        cancelButtonText="重置"
        :fields="columns"
        :form="queryForm"
        inline
        :span="6"
        formType="1"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
        <!-- 定制旅行类型选择器插槽 -->
        <template #travelLineType>
          <pl-dict-select
            v-model="queryForm.travelLineType"
            dictCode="customized_travel_type"
            @change="handletravelLineTypeChange"
          ></pl-dict-select>
        </template>
      </pl-form>

      <div class="flex">
        <pl-button
          type="primary"
          @click="batchDispatch"
          v-has="'order_manage:batch_car'"
        >
          批量派车
        </pl-button>
        <pl-button
          type="primary"
          plain
          @click="batchArrive"
          v-has="'order_manage:batch_reach'"
        >
          批量到达
        </pl-button>
        <AudioNotify
          class="AudioNotify"
          @orderChange="handleOrderChange"
        ></AudioNotify>
      </div>
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          :row-class-name="tableRowClassName"
          @selection-change="handleSelectionChange"
        >
          <template #orderNo="{ scope }">
            <span class="order-no"
              >{{ scope.row.orderNo }}
              <span class="new" v-if="tableRowClassName(scope)"
                >未处理</span
              ></span
            >
          </template>
          <template #operate="{ scope }">
            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'close')"
              link
              type="danger"
              v-has="'order_manage:close'"
              @click="closeOrder(scope)"
              >关闭</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'contact')"
              link
              type="primary"
              @click="handlePop(scope.row, 'lx')"
              v-has="'order_manage:contact'"
              >联系</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'payment')"
              link
              type="primary"
              @click="receivePayment(scope.row)"
              v-has="'order_manage:payment'"
              >收款</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'dispatch')"
              link
              type="primary"
              @click="handlePop(scope.row, 'pc')"
              v-has="'order_manage:dispatch'"
              >派车</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'arrive')"
              link
              type="primary"
              @click="arrive(scope.row)"
              v-has="'order_manage:reach'"
              >到达</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'detail')"
              link
              @click="handleDetail(scope, 'xq')"
              v-has="'order_manage:detail'"
              >详情</pl-button
            >

            <pl-button
              v-if="showOperateButton(scope.row.orderStateNo, 'edit')"
              link
              type="primary"
              @click="handlePop(scope.row, 'bj')"
              v-has="'order_manage:edit'"
              >编辑</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 联系/派车/编辑弹窗 start -->

    <pl-dialog
      v-model="popVisible"
      width="30%"
      :title="popTitle"
      @confirm="handleDialogChange"
      :append-to-body="true"
      :showCancel="true"
    >
      <template #content>
        <FormPop
          :popTitle="popTitle"
          :detailData="detailData"
          v-if="popVisible"
          ref="formPopRef"
        ></FormPop>
      </template>
    </pl-dialog>

    <!-- 联系/派车/编辑弹窗 end -->

    <!-- 详情弹窗 start -->
    <pl-drawer v-model="detailVisible" :title="popTitle">
      <pl-scrollbar class="h100 tenant-Scrollbar">
        <DetailPop
          v-if="detailVisible"
          :detailData="detailData"
          @clickBtn="handleBtnClick"
          ref="detailPopRef"
        ></DetailPop>
        <!-- 详情弹窗 end -->
      </pl-scrollbar>
    </pl-drawer>
  </pl-card>
</template>

<script setup>
import { ref, h, onMounted } from "vue";
import { plMessageBox, plInput, plSelect, plMessage } from "pls-common";
import { useTable } from "@/hooks/usetTable";
import FormPop from "./module/form-pop.vue";
import AudioNotify from "@/components/audio-notify/index.vue";
import DetailPop from "./module/detail-pop.vue";
import { utsTraveltypeOptions } from "@/utils/index";
import {
  postPayOrder,
  postConfirmOrder,
  postDispatchOrder,
  getTravelLineSchedule,
  getTrainNoList,
} from "@/api/travel";
import {
  getOrderDetais,
  getCloseOrder,
  postArriveOrder,
  getTravelLineList,
} from "@/api/travel";
const popTitle = ref("");
const popVisible = ref(false);
const detailVisible = ref(false);
const formPopRef = ref(null);
const detailPopRef = ref(null);
const emun = {
  lx: "联系",
  pc: "派车",
  bj: "编辑",
  xq: "详情",
};
const queryForm = ref({});
const detailData = ref({});
// 使用表格hook，获取表格相关方法和数据
const {
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  loadData, // 加载数据方法
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleSearch, // 搜索方法
  handleCancel, // 重置方法
  current,
} = useTable({
  queryForm,
  list: "/sc/order/getOrderList", // 列表接口
  edit: "/sc/travelLine/updateTravelLine", // 编辑接口
  add: "/sc/travelLine/saveTravelLine", // 新增接口
  del: {
    message: "确定要删除该线路数据吗?",
    type: "message",
  },
});

// 监听到新订单时更新表格
const handleOrderChange = () => {
  loadData();
};

const selectionData = ref([]);

/**
 * 多选
 */
const handleSelectionChange = (selection) => {
  selectionData.value = selection;
};

// 刷新订单详情
const refreshDetailData = () => {
  if (detailPopRef.value) {
    detailPopRef.value.getNewDetailData();
  }
};

const handleDialogChange = () => {
  const formData = formPopRef.value.getFormData();
  if (formData.schedulingItemId) {
    if (popTitle.value == "派车") {
      postDispatchOrder({
        orderId: [formData.orderId],
        schedulingItemId: formData.schedulingItemId,
        orderRemark: formData.orderRemark,
      }).then((res) => {
        if (res.code === 200) {
          popVisible.value = false;
          detailData.value = {};
          plMessage("派车成功", "success");
          refreshDetailData();

          loadData();
        } else {
          plMessage(res.message, "error");
        }
      });
    } else if (popTitle.value == "联系") {
      postConfirmOrder({
        orderId: formData.orderId,
        schedulingItemId: formData.schedulingItemId,
        orderRemark: formData.orderRemark,
      }).then((res) => {
        if (res.code === 200) {
          popVisible.value = false;
          detailData.value = {};
          plMessage("确认成功", "success");
          refreshDetailData();
          loadData();
        } else {
          plMessage(res.message, "error");
        }
      });
    }
  } else {
    plMessage("请选择班次", "warning");
  }
};

const travelLineList = ref([]);
onMounted(() => {
  getTravelLineList().then((res) => {
    travelLineList.value = res.data;
  });
});

// 上车点数据
const boardingPoint = ref([]);
//下车点数据
const XCPoint = ref([]);

const handletravelLineTypeChange = (e) => {
  utsTraveltypeOptions(e).then((res) => {
    boardingPoint.value = res.startStopId;
    XCPoint.value = res.endStopId;
  });
};

// 详情
const handleDetail = ({ row }, type) => {
  popTitle.value = emun[type];
  getOrderDetais({
    orderId: row.orderId,
  }).then((res) => {
    if (res.code == 200) {
      detailData.value = res.data;
      detailVisible.value = true;
    } else {
      detailData.value = {};
      detailVisible.value = false;
    }
  });
};

const handlePop = (data, type) => {
  popTitle.value = emun[type];
  getOrderDetais({
    orderId: data.orderId,
  }).then((res) => {
    console.log(res);
    detailData.value = res.data;
    popVisible.value = true;
  });
};

const trainNoList = ref([]);

const columns = ref([
  {
    label: "状态",
    type: "select",
    prop: "orderStatus",
    options: [
      {
        label: "待出行",
        value: 3,
      },
      {
        label: "待派车",
        value: 2,
      },
      {
        label: "待付款",
        value: 1,
      },
      {
        label: "待确定",
        value: 0,
      },
      {
        label: "出行中",
        value: 4,
      },
      {
        label: "已完成",
        value: 5,
      },
      {
        label: "已关闭",
        value: 6,
      },
    ],
  },
  {
    label: "订单编号",
    type: "input",
    prop: "orderNo",
  },
  {
    label: "线路名称",
    type: "select",
    prop: "travelLineId",
    labelKey: "travelLineName",
    valueKey: "travelLineId",
    options: travelLineList,
  },

  {
    label: "起点",
    type: "select",
    prop: "startStopId",
    options: boardingPoint,
    labelKey: "stopName",
    valueKey: "stopId",
  },
  {
    label: "终点",
    prop: "endStopId",
    type: "select",
    options: XCPoint,
    labelKey: "stopName",
    valueKey: "stopId",
  },
  {
    label: "乘车日期",
    prop: "travelDate",
    type: "date",
    format: "YYYY-MM-DD",
    onChange: (value) => {
      queryForm.value.schedulingItemId = null;
      getTrainNoList({
        schedulingDate: value,
      }).then((res) => {
        if (res.code === 200) {
          trainNoList.value = res.data;
        }
      });
    },
  },
  {
    label: "发车班次",
    prop: "schedulingItemId",
    type: "select",
    options: trainNoList,
    labelKey: "trainNo",
    valueKey: "scheduilingItemId",
  },
  {
    label: "订单类型",
    prop: "travelLineType",
    template: "travelLineType",
  },
]);

//关闭订单
const closeOrder = ({ row }) => {
  console.log(row);
  plMessageBox
    .confirm(
      `确定关闭<span style="color:red"> ${row.orderNo}</span> 订单？`,
      "关闭",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    )
    .then(() => {
      getCloseOrder({
        orderId: row.orderId,
      }).then((res) => {
        if (res.code === 200) {
          plMessage("关闭成功", "success");
          loadData();
          refreshDetailData();
        }
      });
    })
    .catch(() => {
      console.log("取消");
    });
};
//收款
const receivePayment = (row) => {
  plMessageBox
    .confirm(
      `确定已收到<span style="color:red"> ${row.orderNo}</span> 乘车款项？`,
      "收款",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    )
    .then(() => {
      postPayOrder({
        orderId: row.orderId,
      }).then((res) => {
        if (res.code === 200) {
          plMessage("收款成功", "success");
          loadData();
          refreshDetailData();
        } else {
          plMessage(res.msg, "error");
        }
      });
    })
    .catch(() => {
      console.log("取消");
    });
};

//到达
const arrive = (row) => {
  plMessageBox
    .confirm(
      `确定<span style="color:red"> ${row.orderNo}</span> 已到达，并结束该行程吗？`,
      "到达",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    )
    .then(() => {
      postArriveOrder([row.orderId]).then((res) => {
        if (res.code === 200) {
          plMessage(`操作成功`, "success");
          refreshDetailData();
          loadData();
        }
      });
    })
    .catch(() => {
      console.log("取消");
    });
};
//批量到达
const batchArrive = () => {
  // 检查是否选择了数据
  if (selectionData.value.length === 0) {
    plMessage("请选择需要到达的订单", "warning");
    return;
  }

  // 检查所选订单的状态是否都是出行中
  const hasInvalidStatus = selectionData.value.some(
    (item) => item.orderStateNo !== 4
  );

  if (hasInvalidStatus) {
    plMessage("只能选择出行中状态的订单进行批量到达", "warning");
    return;
  }

  plMessageBox
    .confirm(
      `确定要将这${selectionData.value.length}个订单全部到达吗？`,
      "批量到达",
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    )
    .then(() => {
      const orderIds = selectionData.value.map((item) => item.orderId);
      postArriveOrder(orderIds).then((res) => {
        if (res.code === 200) {
          plMessage(`操作成功`, "success");
          refreshDetailData();
          loadData();
        }
      });
    })
    .catch(() => {
      console.log("取消");
    });
};

//详情弹窗按钮点击
const handleBtnClick = (type, data) => {
  switch (type) {
    case "dd":
      arrive(data);
      break;
    case "lx":
      handlePop(data, "lx");
      break;
    case "fk":
      receivePayment(data);
      break;
    case "pc":
      handlePop(data, "pc");
      break;
  }
};

//批量派车
const batchDispatch = async () => {
  // 检查是否选择了数据
  if (selectionData.value.length === 0) {
    plMessage("请选择需要派车的订单", "warning");
    return;
  }

  // 检查所选订单的状态是否都是待派车
  const hasInvalidStatus = selectionData.value.some(
    (item) => item.orderStateNo !== 2
  );

  if (hasInvalidStatus) {
    plMessage("只能选择待派车状态的订单进行批量派车", "warning");
    return;
  }

  // 检查所选订单的线路名称和日期是否一致
  const firstLineName = selectionData.value[0].travelLineName;
  const firstTravelDate = selectionData.value[0].travelDate;

  const hasDifferentLine = selectionData.value.some(
    (item) => item.travelLineName !== firstLineName
  );

  const hasDifferentDate = selectionData.value.some(
    (item) => item.travelDate !== firstTravelDate
  );

  if (hasDifferentLine) {
    plMessage("不能选择不同的线路进行批量派车", "warning");
    return;
  }

  if (hasDifferentDate) {
    plMessage("不能选择不同日期的订单进行批量派车", "warning");
    return;
  }
  const pbData = await getTravelLineSchedule({
    travelLineId: selectionData.value[0].travelLineId,
    schedulingDate: firstTravelDate,
  });

  if (pbData.code !== 200) return;

  const schedulingItemId = ref("");
  const content = h(
    "div",
    {
      class: "batch-dispatch",
    },
    [
      h("p", `确定要将这${selectionData.value.length}个订单全部派车吗？`),
      h(
        "div",
        {
          class: "flex",
        },
        [
          h("span", { class: "lineName" }, "线路名称："),
          h(plInput, {
            modelValue: firstLineName,
            disabled: true,
          }),
        ]
      ),
      h(
        "div",
        {
          class: "flex",
        },
        [
          h("span", { class: "lineName" }, "乘车日期："),
          h(plInput, {
            modelValue: firstTravelDate,
            disabled: true,
          }),
        ]
      ),
      h(
        "div",
        {
          class: "flex",
        },
        [
          h("span", { class: "bc" }, "班次："),
          h(plSelect, {
            options: pbData.data,
            valueKey: "schedulingItemId",
            labelKey: "schedulingNo",
            onChange: (value) => {
              schedulingItemId.value = value;
            },
          }),
        ]
      ),
    ]
  );

  plMessageBox
    .confirm(content, "批量派车", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      customClass: "batch-dispatch-dialog",
    })
    .then(() => {
      const orderIds = selectionData.value.map((item) => item.orderId);
      postDispatchOrder({
        orderId: orderIds,
        schedulingItemId: schedulingItemId.value,
      }).then((res) => {
        if (res.code === 200) {
          plMessage("派车成功", "success");
          refreshDetailData();
          loadData();
        } else {
          plMessage(res.message, "error");
        }
      });
    })
    .catch(() => {
      console.log("取消");
    });
};
//表单columns
const tableColumns = ref([
  {
    type: "selection",
    width: 50,
  },
  {
    label: "订单号",
    prop: "orderNo",
    minWidth: 300,
    template: "orderNo",
  },
  {
    label: "订单类型",
    prop: "travelLineTypeName",
    width: 120,
  },
  {
    label: "线路名称",
    prop: "travelLineName",
    minWidth: 300,
  },
  {
    label: "乘车日期",
    prop: "travelDate",
    width: 120,
  },
  {
    label: "乘车时间",
    prop: "travelTime",
    width: 120,
  },
  {
    label: "起点",
    prop: "startStopName",
    minWidth: 200,
  },
  {
    label: "终点",
    prop: "endStopName",
    width: 200,
    align: "center",
  },
  {
    label: "状态",
    prop: "orderState",
  },
  {
    label: "创建日期",
    prop: "createTime",
    width: 120,
  },
  {
    label: "操作",
    fixed: "right",
    template: "operate",
    width: 200,
    align: "right",
  },
]);

const showOperateButton = (orderStateNo, type) => {
  // 按钮显示规则对象
  const buttonRules = {
    close: [0, 1, 2, 3], // 关闭按钮
    contact: [0], // 联系按钮
    payment: [1], // 收款按钮
    dispatch: [2], // 派车按钮
    arrive: [4], // 到达按钮
    detail: [0, 1, 2, 3, 4, 5, 6], // 详情按钮
    edit: [0, 1, 2, 3, 4, 5, 6], // 编辑按钮
  };
  return buttonRules[type]?.includes(orderStateNo);
};

const tableRowClassName = ({ row }) => {
  if (row.orderStateNo === 0) {
    // 待确定状态
    return "pending-confirm-row";
  }
  return "";
};
</script>

<style lang="scss" scoped>
.AudioNotify {
  margin-left: 10px;
}
.tenant-Scrollbar {
  padding-bottom: 20px;
}
</style>
<style lang="scss">
.batch-dispatch-dialog {
  .batch-dispatch {
    .flex {
      margin-top: 10px;
      align-items: center;
    }
  }
  .lineName {
    width: 100px;
    text-align: right;
  }
  .bc {
    width: 100px;
    text-align: right;
  }
  .el-message-box__message {
    flex: 1;
  }
  .el-message-box__container {
    width: 100% !important;
  }
  .el-input,
  .pl-select {
    flex: 1;
  }
}

// 添加待确定行的样式
.pending-confirm-row {
}

// 确保hover效果仍然存在
// .pending-confirm-row:hover > td {
//   background-color: #fae3c4 !important; // hover时的颜色稍深一些
// }

.order-no {
  .new {
    background-color: #00ad76;
    color: #fff;
    border-radius: 5px;
    padding: 2px 4px;
    margin-left: 4px;
    font-size: 12px;
  }
}
</style>
