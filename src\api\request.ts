import axios from "axios";
import { plMessage, storage } from "pls-common";
import { logout } from "@/utils/index";
import { getCookie } from "@/utils/cookie";
const { VITE_API_URL, VITE_API_ADMIN } = import.meta.env;
const api = axios.create({
  baseURL: VITE_API_URL + VITE_API_ADMIN, // 设置基础 URL
  timeout: 50000, // 设置请求超时时间
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 根据 URL 判断是否需要修改 baseURL
    const customBaseURLs = [
      "/traffic/",
      "/order/",
      "/sc/",
      "/pay/",
      "/uc/",
      "/ticket/",
      "/ea/",
      "/contract/",
    ];
    if (customBaseURLs.some((prefix) => config.url?.startsWith(prefix))) {
      config.baseURL = VITE_API_URL; // 第三方接口使用不同的 baseURL
    } else {
      config.baseURL = VITE_API_URL + VITE_API_ADMIN; // 默认接口路径
    }
    const scopeCode = storage.getItem("scopeCode");
    config.headers["scopeCode"] = scopeCode;

    const userCookie: string | null = getCookie("userInfo");
    let userInfo: any = "";
    if (userCookie) {
      userInfo = JSON.parse(userCookie);
      config.headers["token"] = userInfo?.token || "";
    }
    config.headers["tenantId"] = userInfo?.tenantId || 10000;
    config.data = config.data ? config.data : {};
    config.data.tenantId = config.data.tenantId || userInfo?.tenantId;
    // config.data.orgId = userInfo?.orgId || "";

    return config;
  },
  (error) => {
    // 请求错误处理
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: any) => {
    const { code, message } = response.data;
    if (code == 200) {
      return response;
    } else if (code === 401) {
      plMessage("登录失效，请重新登录", "error");
      logout();
      return;
    } else {
      if (code) {
        plMessage(message, "error");
        if (code === 10007) {
          logout();
        }
      }
      return response;
    }
  },
  (error) => {
    plMessage(error.message || error.msg, "error");
    // 默认错误处理
    return Promise.reject(error);
  }
);

async function get(url: string, params?: any) {
  const response = await api.get(url, {
    params: params,
  });
  return response.data;
}

async function post(url: string, params?: any) {
  const response = await api.post(url, params);
  return response.data;
}

async function put(url: string, params?: any) {
  const response = await api.put(url, params);
  return response.data;
}

async function del(url: string, params?: any) {
  console.log("del", params);
  const response = await api.delete(url, params);
  return response.data;
}

const http = {
  // setApiConfig,
  post,
  put,
  del,
  get,
};

export default http;
