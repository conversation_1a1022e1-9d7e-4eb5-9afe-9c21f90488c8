function getCookieDomain(): string {
  const hostname = window.location.hostname;
  if (hostname === "localhost" || /^\d{1,3}(\.\d{1,3}){3}$/.test(hostname)) {
    return "";
  }

  const parts = hostname.split(".");
  // 如果域名只有两部分，直接返回
  if (parts.length <= 2) {
    return "." + hostname;
  }
  // 否则，取最后两部分
  const mainDomain = parts.slice(-2).join(".");
  return "." + mainDomain;
}
/**
 * 设置 Cookie 的方法
 * @param name - Cookie 的名称
 * @param value - Cookie 的值
 * @param days - 过期天数
 */
export function setCookie(name: string, value: string, days: number): void {
  let expires = "";
  if (days) {
    const date: Date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = "; expires=" + date.toUTCString();
  }

  // 动态获取 domain
  const domain = getCookieDomain();
  // 构造 cookie 字符串
  let cookieStr: string = `${name}=${encodeURIComponent(
    value
  )}${expires}; path=/`;
  if (domain) {
    cookieStr += `; domain=${domain}`;
  }
  // 根据协议判断是否添加 Secure 属性（生产环境建议启用 HTTPS 后启用）
  // if (window.location.protocol === 'https:') {
  //   cookieStr += "; Secure";
  // }
  document.cookie = cookieStr;
}

/**
 * 获取指定名称的 Cookie 值
 * @param name - Cookie 名称
 * @returns 返回 Cookie 值，如果没有则返回 null
 */
export function getCookie(name: string): string | null {
  const cookieString: string = "; " + document.cookie;
  const parts: string[] = cookieString.split("; " + name + "=");
  if (parts.length === 2) {
    return decodeURIComponent(parts.pop()?.split(";").shift() || "");
  }
  return null;
}

export function clearCookie(name: string): void {
  // 构造过期时间为过去的时间（1970 年 1 月 1 日）
  let cookieStr: string = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;

  // 根据当前环境获取 domain
  const domain = getCookieDomain();
  if (domain) {
    cookieStr += `; domain=${domain}`;
  }

  document.cookie = cookieStr;
}
