<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        :fields="columns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        @confirm="handleSearch"
        @cancel="handleCancel"
        confirmButtonText="搜索"
        cancelButtonText="重置"
      ></pl-form>

      <!-- 操作按钮开始 -->
      <div class="operation-btns fixed">
        <pl-button
          type="primary"
          :loading="exportLoading"
          @click="exportDetail"
          class="fixed-right"
        >
          导出
        </pl-button>
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表单 start -->
      <div class="card-table mt20">
        <pl-table
          :columns="payNoticeColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, payNoticeColumns)"
        >
          <!-- 业务结果 -->
          <template #serviceResult="{ scope }">
            <span v-if="scope.row.serviceResult == 0">失败</span>
            <span v-if="scope.row.serviceResult == 1">成功</span>
          </template>
          <!-- 操作 -->
          <template #operation="{ scope }">
            <pl-button link @click="handleDetail(scope.row)">详情</pl-button>
            <pl-button
              link
              type="primary"
              @click="handlePay(scope.row)"
              v-if="scope.row.serviceResult == 0"
            >
              重新执行
            </pl-button>
          </template>
        </pl-table>
      </div>
      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 表单 end -->

      <!-- 详情 start -->
      <pl-dialog
        v-model="detailVisible"
        title="微信返回值"
        width="1100"
        :showCancel="false"
        @confirm="handleDetailConfirm"
        :append-to-body="true"
        affirmText="关闭"
      >
        <template #content>
          <div class="wechat-notice-detail">
            <div class="notice-container">
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">应用ID</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.应用ID }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">商户号</div>
                  <div class="notice-value">{{ wechatNoticeData.商户号 }}</div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">设备号</div>
                  <div class="notice-value">{{ wechatNoticeData.设备号 }}</div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">随机字符串</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.随机字符串 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">签名</div>
                  <div class="notice-value">{{ wechatNoticeData.签名 }}</div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">签名类型</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.签名类型 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">业务结果</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.业务结果 == 0 ? "失败" : "成功" }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">错误代码</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.错误代码 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">错误代码描述</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.错误代码描述 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">用户标识</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.用户标识 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">交易类型</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.交易类型 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">
                    {{
                      wechatNoticeData.支付类型 == "PAYMENT"
                        ? "付款银行"
                        : "退款账户"
                    }}
                  </div>
                  <div class="notice-value">
                    {{ wechatNoticeData.付款银行 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">
                    {{
                      wechatNoticeData.支付类型 == "PAYMENT"
                        ? "支付金额"
                        : "退款金额"
                    }}
                  </div>
                  <div class="notice-value">
                   ￥{{ wechatNoticeData.订单金额 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">应结订单金额</div>
                  <div class="notice-value">
                    ￥{{ wechatNoticeData.应结订单金额 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">货币种类</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.货币种类 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">总代金券金额</div>
                  <div class="notice-value">
                    ￥{{ wechatNoticeData.总代金券金额 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">代金券使用数量</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.代金券使用数量 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">代金券类型</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.代金券类型 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">代金券ID</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.代金券ID }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">单个代金券支付金额</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.单个代金券支付金额 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">微信支付订单号</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.微信支付订单号 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">商户订单号</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.商户订单号 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">商家数据包</div>
                  <div class="notice-value">
                    {{ wechatNoticeData.商家数据包 }}
                  </div>
                </div>
                <div class="notice-item">
                  <div class="notice-label">
                    {{
                      wechatNoticeData.支付类型 == "PAYMENT"
                        ? "支付完成时间"
                        : "退款成功时间"
                    }}
                  </div>
                  <div class="notice-value">
                    {{ wechatNoticeData.支付完成时间 }}
                  </div>
                </div>
              </div>
              <div class="notice-row">
                <div class="notice-item">
                  <div class="notice-label">
                    {{
                      wechatNoticeData.支付类型 == "PAYMENT"
                        ? "支付状态"
                        : "退款状态"
                    }}
                  </div>
                  <div class="notice-value">
                    {{ wechatNoticeData.支付状态 }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </pl-dialog>
      <!-- 详情 end -->
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { plMessage } from "pls-common";
import { payNoticeColumns } from "./config/config";
import { getDictionaryData } from "@/api/dict";
import { useTable } from "@/hooks/usetTable";
import {
  getPayReExecuteBusiness,
  getPayNoticeDetail,
  getBusinessType,
  exportPayNotice,
} from "@/api";
const queryForm = ref({});

const notifyType = ref([]); // 通知类型
const businessType = ref([]); // 业务类型
onMounted(() => {
  getDictionaryData({
    dictTypeCode: "payment_notify_type",
  }).then((res) => {
    notifyType.value = res;
  });
  getBusType();
});
// 获取业务类型
const getBusType = async () => {
  const res = await getBusinessType();
  businessType.value = res.data;
};

// 使用表格hook，获取表格相关方法和数据
const {
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  loadData, // 加载数据方法
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleSearch, // 搜索方法
  handleCancel, // 重置方法
  current,
  handleSort,
} = useTable({
  queryForm,
  list: "/pay/paymentNotify/pagePaymentNotify", // 列表接口
});

const detailVisible = ref(false);
const handleDetailConfirm = () => {
  detailVisible.value = false;
};
const handleDetail = (row) => {
  console.log(row.notifyNo);

  detailVisible.value = true;
  getPayNoticeDetail(row.notifyNo).then((e) => {
    const res = e.data;
    wechatNoticeData.value = {
      支付类型: res.paymentNotifyType,
      应用ID: res.appId,
      商户号: res.mchid,
      // 设备号: "",
      随机字符串: res.nonce,
      签名: res.sign,
      签名类型: res.signType,
      业务结果: res.serviceResult,
      错误代码: res.errCode,
      错误代码描述: res.errCodeDes,
      用户标识: res.openId,
      交易类型: res.tradeType,
      付款银行:
        res.paymentNotifyType == "PAYMENT" ? res.bankType : res.refundAccount,
      订单金额:
        res.paymentNotifyType == "PAYMENT"
          ? res.payerPayAmount
          : res.payerRefundAmount,
      应结订单金额: res.payableOrderAmount,
      货币种类: res.payerCurrency,
      总代金券金额: res.couponTotal,
      代金券使用数量: "",
      代金券类型: "",
      代金券ID: "",
      单个代金券支付金额: "",
      微信支付订单号: res.transactionNo,
      商户订单号: res.outTradeNo,
      // 商家数据包: "",
      支付完成时间: res.transactionTime,
      支付状态:
        res.paymentNotifyType == "PAYMENT"
          ? res.paymentTradeStatusName
          : res.refundTradeStatusName,
    };
  });
};
const handlePay = (row) => {
  getPayReExecuteBusiness(row.notifyNo)
    .then((res) => {
      if (res.code === 200) {
        plMessage("重新执行成功", "success");
        loadData();
      }
    })
    .catch((err) => {
      console.log("err", err);
    });
};
const columns = ref([
  {
    label: "通知类型",
    type: "select",
    prop: "payNotifyTypeList",
    options: notifyType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "通知单号",
    prop: "notifyNo",
    type: "input",
  },
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "商户单号",
    prop: "outTradeNo",
    type: "input",
  },
  {
    label: "业务类型",
    type: "select",
    prop: "businessTypeList",
    labelKey: "businessTypeName",
    valueKey: "businessType",
    options: businessType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "交易日期",
    prop: "successTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (val) => {
      console.log(queryForm.value.dateType);
      if (val && val.length === 2) {
        queryForm.value.successTimeStart = val[0] + " 00:00:00";
        queryForm.value.successTimeEnd = val[1] + " 23:59:59";
      }
    },
  },
  {
    label: "业务结果",
    prop: "serviceResult",
    type: "select",
    options: [
      {
        label: "成功",
        value: 1,
      },
      {
        label: "失败",
        value: 0,
      },
    ],
  },
]);

const wechatNoticeData = ref({
  应用ID: "",
  商户号: "",
  // 设备号: "013467007045764",
  随机字符串: "",
  签名: "",
  签名类型: "",
  业务结果: "",
  错误代码: "",
  错误代码描述: "",
  用户标识: "",
  交易类型: "",
  付款银行: "",
  订单金额: "",
  应结订单金额: "",
  货币种类: "",
  总代金券金额: "",
  代金券使用数量: "",
  代金券类型: "",
  代金券ID: "",
  单个代金券支付金额: "",
  微信支付订单号: "",
  商户订单号: "",
  // 商家数据包: "123456",
  支付完成时间: "",
});

// 点击导出
const exportLoading = ref(false);
const exportDetail = async () => {
  if (!tableData.value.length) {
    plMessage("暂无可导出的数据", "warning");
    return;
  }

  exportLoading.value = true;
  try {
    const res = await exportPayNotice(queryForm.value);
    const filePath = res.data;

    window.open(filePath);
    plMessage("导出成功", "success");
  } catch (error) {
    console.log("error", error);
    plMessage(error.message, "error");
  } finally {
    exportLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.wechat-notice-detail {
  padding: 20px;

  .notice-container {
    display: flex;
    flex-direction: column;
  }

  .notice-row {
    display: flex;
    &:first-child {
      .notice-item {
        border-top: 1px solid #ebeef5;
      }
    }
  }

  .notice-item {
    flex: 1;
    display: flex;
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    &:first-child {
      border-left: 1px solid #ebeef5;
    }

    .notice-label {
      width: 150px;
      padding: 12px 15px;
      background-color: #f5f7fa;
      border-right: 1px solid #ebeef5;
      color: #606266;
      font-weight: 500;
      white-space: nowrap;
    }

    .notice-value {
      flex: 1;
      padding: 12px 15px;
      color: #606266;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}

.operation-btns {
  display: flex;
  justify-content: flex-end;
}
</style>
