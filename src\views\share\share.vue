<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="columns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
        >
          <template #operation="{ scope }">
            <pl-button link @click="handleShareDetails(scope.row)"
              >查看</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 表单 end -->
    </div>

    <!-- 查询详情弹窗 start  -->
    <pl-drawer v-model="shareDetailsVisible" title="查看">
      <share-details
        v-if="shareDetailsVisible"
        :data="shareDetailsData"
      ></share-details>
    </pl-drawer>
    <!-- 查询详情弹窗 edn -->
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import ShareDetails from "./module/share-details.vue";
import { useTable } from "@/hooks/usetTable";
const shareDetailsData = ref({});
const shareDetailsVisible = ref(false);
const handleShareDetails = (row) => {
  shareDetailsData.value = row;
  shareDetailsVisible.value = true;
};
const queryForm = ref({});
// 使用表格hook，获取表格相关方法和数据
const {
  dataTotal, // 数据总数
  tableData, // 表格数据
  tabLoading, // 表格加载状态
  // loadData, // 加载数据方法
  sizeChange, // 分页大小改变方法
  currentChange, // 当前页改变方法
  handleSearch, // 搜索方法
  handleCancel, // 重置方法
  current,
} = useTable({
  queryForm,
  list: "/uc/clientUserShare/getSharedInfo", // 列表接口
});
const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 100,
    align: "center",
  },
  {
    label: "用户名",
    prop: "userName",
    width: 150,
  },
  {
    label: "应用名称",
    prop: "appName",
    width: 150,
  },
  {
    label: "分享总数",
    prop: "shareCount",
    align: "center",
    width: 150,
  },
  {
    label: "一级分享",
    prop: "levelOneCount",
    align: "center",
  },
  {
    label: "二级分享",
    prop: "levelTwoCount",
    align: "center",
  },
  {
    label: "操作",
    template: "operation",
    fixed: "right",
    align: "center",
  },
]);

const columns = ref([
  {
    label: "用户名",
    prop: "userName",
    type: "input",
  },
  {
    label: "应用名称",
    prop: "appId",
    type: "select",
    options: [
      {
        label: "实时公交",
        value: "wx90e1211aa50c69a8",
      },
      {
        label: "定制出行",
        value: "wx6cec878dfc316d92",
      },
    ],
  },
]);
</script>

<style lang="scss" scoped></style>
