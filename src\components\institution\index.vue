<!-- 机构管理 -->
<template>
  <div class="organize-box">
    <div class="org-search">
      <pl-input placeholder="请输入关键字查询" v-model="searchKey"></pl-input>
      <pl-button class="search-btn" type="primary" plain @click="handleSearch"
        >搜索</pl-button
      >
    </div>
    <div class="org-list">
      <div class="list-item-box">
        <pl-scrollbar>
          <div
            class="org-item"
            v-for="(item, index) in tenantList"
            :key="index"
            :class="activeIndex == index ? 'active' : ''"
            @click="handleClick(index, item)"
          >
            <div
              class="pl-font pl-icon-a-qietu42 icon"
              v-if="item.tenantType === 0"
            ></div>
            <div
              class="pl-font pl-icon-a-qietu43 icon"
              v-else-if="item.tenantType === 1"
            ></div>
            <div
              class="pl-font pl-icon-a-qietu44 icon"
              v-else-if="item.tenantType === 2"
            ></div>
            <!-- <img
              class="icon"
              src="@/assets/img/ptzh.png"
              alt=""
              v-if="item.tenantType === 0"
            />
            <img
              class="icon"
              src="@/assets/img/mbzh.png"
              alt=""
              v-if="item.tenantType === 1"
            /> -->
            {{ item.tenantName }}
          </div>
        </pl-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getTenantList } from "@/api/index";
// 搜索
const searchKey = ref("");
/**
 * 搜索租户
 */
const handleSearch = () => {
  getList();
};

const tenantList = ref([]);
/**
 * 获取租户列表
 */
const getList = (cb) => {
  getTenantList({
    //   isDeedRoleList: true,
    tenantName: searchKey.value || "",
  })
    .then((res) => {
      let list = [];
      for (let k in res.data) {
        list = list.concat(res.data[k]);
      }
      tenantList.value = list;
      if (cb) {
        return cb();
      }
    })
    .catch(() => {
      emit("error");
    });
};
getList(() => {
  emit("change", tenantList.value[0]);
});

let activeIndex = ref(0);

const emit = defineEmits(["change", "error"]);

const handleClick = (index, item) => {
  activeIndex.value = index;
  emit("change", item);
};
</script>

<style scoped lang="scss">
.organize-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  .org-list {
    flex: 1;
    position: relative;

    .list-item-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }

    .org-item {
      padding-left: 20px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 5px;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      .pl-icon-a-qietu42 {
        color: #ff4c51ff;
      }
      .pl-icon-a-qietu43 {
        color: #ffb400ff;
      }
      .pl-icon-a-qietu44 {
        color: #666666ff;
      }
      .icon {
        margin-right: 10px;
      }
      &:hover {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary) !important;
        .icon {
          color: var(--el-color-primary) !important;
        }
      }

      &.active {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary) !important;
        .icon {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }
}

.org-list {
  margin-top: 15px;

  .org-item {
    padding: 10px 0;
    color: #999;
    font-size: 14px;
  }
}

.org-search {
  display: flex;

  .search-btn {
    margin-left: 10px;
  }
}
</style>
