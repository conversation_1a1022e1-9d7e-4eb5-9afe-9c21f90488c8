<template>
  <div class="layout-header" ref="layoutHeader">
    <div class="layout-header-flex">
      <div class="breadcrumb-box">
        <div class="logo-box">
          <img class="logo" src="@/assets/img/logo.png" alt="" />
        </div>
      </div>

      <div class="header-center" v-if="apps.length > 1">
        <div
          class="item"
          v-for="item in apps"
          :key="item.sysAppInfoVO.appId"
          :class="currentAppId == item.sysAppInfoVO.appId ? 'active' : ''"
          @click="handleSwitchAapp(item)"
        >
          <img :src="item.iconUrl" alt="" style="width: 26px; height: 26px" />
          <div class="name">{{ item.sysAppInfoVO.appName }}</div>
        </div>
      </div>

      <div class="header-right">
        <div class="nav-item" @click="headerLock">
          <ElTooltip content="隐藏头部">
            <div class="pl-font pl-icon-lock"></div>
          </ElTooltip>
        </div>
        <div class="nav-item">
          <ElTooltip content="更换主题色">
            <div class="pl-font pl-icon-seban"></div>
          </ElTooltip>
        </div>
        <div class="nav-item" @click="handleFullScreen">
          <ElTooltip :content="isFullScreen ? '退出全屏' : '全屏'">
            <div class="pl-font pl-icon-quanping"></div>
          </ElTooltip>
        </div>
        <div class="nav-item">
          <ElTooltip content="操作指南">
            <div class="pl-font pl-icon-wenhao"></div>
          </ElTooltip>
        </div>
        <div class="nav-item">
          <ElTooltip content="消息通知">
            <div class="pl-font pl-icon-tongzhi"></div>
          </ElTooltip>
        </div>
        <el-dropdown>
          <div class="user-box">
            <img
              class="user-img"
              src="http://hyuu.oss-cn-shenzhen.aliyuncs.com/Y52016146512698711.png"
              alt=""
            />
            <span class="user-name">{{ userInfo.username }}</span>
            <pl-icon name="ArrowDown"></pl-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <el-dropdown-item>首页</el-dropdown-item>
              <el-dropdown-item>个人中心</el-dropdown-item> -->
              <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import axios from "axios";
import { ref, onMounted } from "vue";
import { storage } from "pls-common";
import { logout } from "@/utils/index";
import { getDictionaryData } from "@/api/dict";
import { getCookie } from "@/utils/cookie";
const hostConfig = ref([]);

// import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
  breadcrumbItems: {
    type: Array,
    default: () => [],
  },
});
// const emit = defineEmits(["onCollapseChange"]);
// 伸缩
// const handleRetract = () => {
//     emit("onCollapseChange", !props.isCollapse);
// };
let userAuthMenu = storage.getItem("userAuthMenu") || [];
let userInfo = ref(JSON.parse(getCookie("userInfo")) || {});
const apps = ref([]);
const currentAppId = ref(import.meta.env.VITE_APP_ID);
onMounted(async () => {
  const plsConfig = await axios.get("/pls-config.json");
  hostConfig.value = plsConfig.data.hostConfig;
  let currentHost = window.location.host;
  userAuthMenu.forEach((item) => {
    hostConfig.value.find((config) => {
      if (config.APPID == item.sysAppInfoVO.appId) {
        item.url = currentHost.replace(/^[^.]+/, config.host);
        item.iconUrl = config.iconUrl;
      }
    });
  });
  apps.value = userAuthMenu;
});

/**
 * 全屏
 */
const isFullScreen = ref(false);
import screenfull from "screenfull";
const handleFullScreen = () => {
  screenfull.toggle();
  isFullScreen.value = !screenfull.isFullscreen;
};

const emit = defineEmits(["lock"]);
/**
 * 锁定头部
 */
const headerLock = () => {
  emit("lock");
};

const appPort = ref({});

if (import.meta.env.MODE === "development") {
  getDictionaryData({
    dictTypeCode: "localhost_port",
  }).then((res) => {
    res.forEach((item) => {
      appPort.value[item.remark] = item.value;
    });
  });
}

/**
 * 切换应用
 */
const handleSwitchAapp = (item) => {
  currentAppId.value = item.sysAppInfoVO.appId;
  const treeList = item.treeList;
  let obj = {
    appId: currentAppId.value,
    list: treeList,
  };

  storage.setItem("menuList", obj);
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  function getLocal() {
    return (
      protocol +
      "//" +
      hostname +
      ":" +
      hostConfig.value.find((config) => config.APPID == currentAppId.value).port
    );
  }
  console.log(getLocal());
  window.location.href =
    import.meta.env.MODE === "development"
      ? getLocal()
      : protocol + "//" + item.url;
};
</script>

<style scoped lang="scss">
.header-center {
  flex: 1;
  padding: 0 24px;
  display: flex;

  .item {
    width: 90px;
    height: 68px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    cursor: pointer;
    &.active,
    &:hover {
      background-color: var(--el-color-primary-light-8);
      border-radius: 8px;
      .name {
        color: var(--el-color-primary);
      }
    }
    .name {
      font-size: 14px;
      margin-top: 8px;
      color: #3d3d3d;
      // 显示一行 超出隐藏
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      text-align: center;
      padding: 0 5px;
    }
  }
}
.logo-box {
  height: 60px;
  display: flex;
  align-items: center;
  padding-left: 20px;
}

.logo {
  width: 130px;
}
:deep(.el-tooltip__trigger:focus-visible) {
  outline: unset;
}

.layout-header {
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
  height: 100%;

  .breadcrumb-box {
    display: flex;
    align-items: center;
    height: 100%;
    width: 180px;
    .fold-box {
      padding: 0 10px;
      height: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }

  .layout-header-flex {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-right {
    padding-right: 15px;
    display: flex;
    align-items: center;
    height: 100%;

    .nav-item {
      height: 100%;
      padding: 0 10px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .pl-font {
        font-size: 24px;
      }
    }
  }

  .user-box {
    display: flex;
    align-items: center;
    color: #b5bece;

    .user-name {
      margin-right: 10px;
      font-size: 12px;
    }

    .user-img {
      width: 40px;
      margin-right: 10px;
    }
  }
}
</style>
