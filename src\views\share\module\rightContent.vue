<template>
  <div class="content-details">
    <pl-form
      :fields="columns"
      inline
      :form="queryForm"
      confirmButtonText="搜索"
      cancelButtonText="重置"
      @confirm="handleSearch"
      @cancel="handleCancel"
    ></pl-form>
    <div class="content-table">
      <pl-table
        :columns="tableColumns"
        :data="tableData"
        class="table"
        v-loading="tabLoading"
      ></pl-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { postCommissionList } from "@/api/travel";
const queryForm = ref({});
const tabLoading = ref(false);
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

watch(
  () => props.data,
  (newVal) => {
    console.log(newVal);
    getDetailsData();
  },
  {
    deep: true,
  }
);
const handleSearch = () => {
  getDetailsData();
};
const handleCancel = () => {
  queryForm.value = {};
  getDetailsData();
};
const getDetailsData = () => {
  tabLoading.value = true;
  postCommissionList({
    sharerClientUserId: props.data.sharerClientUserId,
    level: props.data.level,
    ...queryForm.value,
  }).then((res) => {
    if (res.code === 200 && res.data.length) {
      tableData.value = res.data;
    } else {
      tableData.value = [];
    }
    tabLoading.value = false;
  });
};

const columns = ref([
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "下单时间",
    prop: "orderTime",
    type: "date",
    format: "YYYY-MM-DD",
  },
]);
const tableColumns = ref([
  {
    label: "订单号",
    prop: "orderNo",
  },
  {
    label: "订单金额",
    prop: "orderAmount",
  },
  {
    label: "抽佣金额",
    prop: "commissionAmount",
  },
  {
    label: "下单时间",
    prop: "orderTime",
  },
]);
const tableData = ref([]);
</script>

<style lang="scss" scoped>
.content-details {
  display: flex;
  flex-direction: column;
  height: 100%;
  .content-table {
    flex: 1;
    position: relative;
    .table {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
