<template>
  <div class="login-container">
    <img class="logo" src="@/assets/img/logo.png" alt="" />
    <div class="title-box">
      <img class="login-bj" src="@/assets/img/login-bj.png" alt="" />
      <p class="h-title">智慧物流平台，解决最后一公里</p>
      <p class="h-desc">玩转快递物流运输！</p>
    </div>

    <div class="login-box">
      <!-- 注册or登录切换 start-->
      <!-- <div
        class="login-switch"
        @click="isRegister = true"
        v-if="!isRegister"
      ></div>
      <div class="login-switch login" @click="isRegister = false" v-else></div> -->
      <!-- 注册or登录切换 end-->

      <!-- <div class="wechat-login">
        <div class="title">扫码登录</div>
        <div class="erweima">
          <img src="@/assets/img/login-ewm.png" alt="" />
        </div>
        <div class="tips">支持微信扫一扫登录</div>
      </div> -->
      <div class="login-form" v-show="!isRegister">
        <div class="login-tabs">
          <div
            class="tab"
            :class="loginType == 1 ? 'active' : ''"
            @click="tabLoginCheck(1)"
          >
            账号密码登录
          </div>
          <!-- <div
            class="tab"
            :class="loginType == 2 ? 'active' : ''"
            @click="tabLoginCheck(2)"
          >
            手机号登录
          </div> -->
        </div>
        <el-form
          ref="ruleFormRef"
          class="form-box"
          :rules="rules"
          :model="form"
        >
          <template v-if="loginType == 1">
            <el-form-item prop="userName" class="form-item">
              <el-input
                class="input"
                v-model="form.userName"
                placeholder="请输入账号"
              ></el-input>
            </el-form-item>
            <el-form-item prop="userPassword" class="form-item">
              <el-input
                class="input"
                v-model="form.userPassword"
                placeholder="请输入密码"
                type="password"
                show-password
              ></el-input>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item prop="userPhone" class="form-item">
              <el-input
                class="input"
                v-model="form.userPhone"
                placeholder="请输入手机号码"
                type="number"
              >
                <template v-slot:prepend>+86</template>
              </el-input>
            </el-form-item>

            <el-form-item prop="code" class="form-item">
              <el-input
                class="input"
                v-model="form.code"
                placeholder="请输入验证码"
                type="number"
              >
              </el-input>
              <pl-button
                plain
                class="get-code"
                :loading="codeLoading"
                :type="codeType ? 'info' : 'primary'"
                :disabled="codeType"
                @click="getCode"
                >{{ codeTxt }}</pl-button
              >
            </el-form-item>
          </template>
        </el-form>
        <pl-button
          :loading="loginLoading"
          type="primary"
          class="login-btn"
          @click="handleLogin"
          >登录</pl-button
        >
        <!-- <router-link to="/" class="forgot">忘记密码？</router-link> -->
      </div>
      <div class="login-form" v-show="isRegister">
        <div class="login-tabs">
          <div class="tab active">账号密码注册</div>
        </div>
        <el-form
          ref="ruleFormRef2"
          class="form-box"
          :rules="rules"
          :model="form"
        >
          <el-form-item prop="userName" class="form-item">
            <el-input
              class="input"
              v-model="form.userName"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <el-form-item prop="userPassword" class="form-item">
            <el-input
              class="input"
              v-model="form.userPassword"
              placeholder="请输入登录密码"
            ></el-input>
          </el-form-item>
          <el-form-item prop="userPassword2" class="form-item">
            <el-input
              class="input"
              v-model="form.userPassword2"
              placeholder="请再次输入登录密码"
            ></el-input>
          </el-form-item>
          <el-form-item prop="phone" class="form-item">
            <div class="flex">
              <el-input
                class="input"
                v-model="form.phone"
                placeholder="请输入手机号码"
              >
                <template v-slot:prepend>+86</template>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item prop="code" class="form-item">
            <el-input
              class="input"
              v-model="form.code"
              placeholder="请输入验证码"
            ></el-input>
            <pl-button
              plain
              class="get-code"
              :loading="codeLoading"
              :type="codeType ? 'info' : 'primary'"
              :disabled="codeType"
              @click="getCode"
              >{{ codeTxt }}</pl-button
            >
          </el-form-item>
        </el-form>
        <div class="user-xy">
          <pl-checkbox></pl-checkbox>
          我已阅读并同意
          <router-link to="/" class="xy-link">《用户协议》</router-link>
          <router-link to="/" class="xy-link">《隐私政策》</router-link>
          <router-link to="/" class="xy-link">《产品服务协议》</router-link>
        </div>
        <pl-button type="primary" class="login-btn" @click="handleLogin"
          >注册</pl-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElForm, ElFormItem, ElInput } from "element-plus";
import { useRouter } from "vue-router";
import { apiLogin, apiGetAuth } from "@/api/auth";
import { storage, plNotification, plMessage } from "pls-common";
import { generateRoutes } from "@/utils/index";
import { setCookie, clearCookie } from "@/utils/cookie";
const router = useRouter();
const ruleFormRef = ref();

console.log();
const form = reactive({
  userName: import.meta.env.MODE === "development" ? "13635444856" : "",
  userPassword: import.meta.env.MODE === "development" ? "123456" : "",
  phone: "",
  userPassword2: "",
  code: "",
});

/**
 * 登录按钮状态
 */
const loginLoading = ref(false);

// 登录or注册
let isRegister = ref(false);
// 账号or手机号登录
let loginType = ref(1);
// 获取验证码
let codeTxt = ref("获取验证码");
let codeTime = ref(60);
let codeType = ref(false); //按钮状态
let codeLoading = ref(false);
const getCode = () => {
  codeLoading.value = true;
  const timer = setInterval(() => {
    if (codeTime.value <= 0) {
      codeTime.value = 60;
      codeType.value = false;
      codeTxt.value = "获取验证码";
      clearInterval(timer);
    } else {
      codeLoading.value = false;
      codeType.value = true;
      codeTime.value--;
      codeTxt.value = `${codeTime.value}s重新获取`;
    }
  }, 1000);
};

// 表单规则
const rules = reactive({
  userName: [{ required: true, message: "请输入账号" }],
  userPassword: [{ required: true, message: "请输入密码" }],
  userPassword2: [{ required: true, message: "请输入密码" }],
});

const getGreetingMessage = () => {
  const hour = new Date().getHours();
  let timeGreeting = "";

  // 修改时间段问候语
  if (hour >= 5 && hour < 12) {
    timeGreeting = "早上好";
  } else if (hour >= 12 && hour < 17) {
    // 修改下午时段
    timeGreeting = "下午好";
  } else if (hour >= 17 && hour < 19) {
    // 新增傍晚时段
    timeGreeting = "傍晚好";
  } else {
    timeGreeting = "晚上好";
  }

  // 针对不同时段的随机问候语
  const morningMsgs = [
    "新的一天开始啦！ 🌞",
    "愿您精神饱满地开启新的一天！ ✨",
    "祝您今天工作顺利！ 🎯",
    "今天也要元气满满哦！ 💪",
    "清晨的阳光真好，愿您心情愉快！ 🌅",
    "又是充满希望的一天！ 🌈",
    "今天也要加油鸭！ 🦆",
  ];

  const afternoonMsgs = [
    "记得喝杯咖啡提提神哦！ ☕",
    "午后时光，继续加油！ 💫",
    "保持好心情，工作愉快！ 😊",
    "来杯下午茶放松一下吧！ 🫖",
    "愿您度过惬意的下午！ 🌤",
    "记得适当休息，劳逸结合！ 🎵",
    "阳光正好，干劲十足！ ⭐",
  ];

  const eveningMsgs = [
    "辛苦了！ 👋",
    "感谢您的付出！ 🌟",
    "夜晚也要保重身体哦！ 🌙",
    "今天也在努力呢！ 💪",
    "愿您工作顺利！ ✨",
    "夜色正好，继续加油！ 🌃",
    "记得适时补充能量哦！ 🍵",
    "注意劳逸结合！ 🎶",
    "保重身体，平安顺遂！ 🍀",
  ];

  // 添加傍晚专属问候语
  const duskMsgs = [
    "夕阳无限好，愿您心情愉快！ 🌅",
    "傍晚时分，记得放松一下！ 🌆",
    "今天辛苦了，稍作休息吧！ ☕",
    "美好的一天即将结束！ 🌞",
    "享受宁静的傍晚时光！ 🎵",
    "晚霞很美，愿您心情也一样美好！ 🌄",
  ];

  let messages;
  if (hour >= 5 && hour < 12) {
    messages = morningMsgs;
  } else if (hour >= 12 && hour < 17) {
    messages = afternoonMsgs;
  } else if (hour >= 17 && hour < 19) {
    messages = duskMsgs; // 使用傍晚问候语
  } else {
    messages = eveningMsgs;
  }

  const randomMsg = messages[Math.floor(Math.random() * messages.length)];
  return {
    timeGreeting,
    randomMsg,
  };
};

// 登录按钮
const handleLogin = () => {
  ruleFormRef.value.validate((e) => {
    if (e) {
      loginLoading.value = true;
      apiLogin({
        loginAccount: form.userName,
        password: form.userPassword,
      }).then((res) => {
        if (res.code === 200 && res.data.token) {
          let userInfo = res.data.user;
          userInfo.token = res.data.token;
          setCookie("userInfo", JSON.stringify(userInfo), 7);
          storage.setItem("userInfo", userInfo);
          // plMessage("登录成功", "success");
          const { timeGreeting, randomMsg } = getGreetingMessage();

          //获取授权
          apiGetAuth().then(async (res) => {
            if (res.code === 200) {
              loginLoading.value = false;
              storage.setItem("userAuthMenu", res.data);
              if (JSON.stringify(res.data) !== "[]") {
                const appId = import.meta.env.VITE_APP_ID;
                const appInfo = res.data.find(
                  (item) => item.sysAppInfoVO.appId == appId
                );

                if (
                  !appInfo ||
                  !appInfo.treeList ||
                  appInfo.treeList.length == 0
                ) {
                  plMessage("该账户没有此系统权限", "warning");
                  clearCookie("userInfo");
                  return;
                }

                plNotification({
                  title: "登录成功",
                  message: `${timeGreeting}，${randomMsg}`,
                  type: "success",
                  duration: 3000,
                });
                // 生成菜单
                await generateRoutes(appInfo.treeList);
                storage.setItem("menuList", {
                  appId: appInfo.sysAppInfoVO.appId,
                  list: appInfo.treeList,
                });
                setTimeout(() => {
                  router.push("/");
                }, 500);
              }
            } else {
              storage.removeItem("userInfo");
              loginLoading.value = false;
            }
          });
        } else {
          loginLoading.value = false;
        }
      });
    }
  });
};

/**
 * 登录方式切换
 */
const tabLoginCheck = (e) => {
  loginType.value = e;
};
</script>

<style scoped lang="scss">
.get-code {
  height: 36px;
  margin-left: 10px;
  .el-button--primary.is-plain {
    background: #fff;
  }
}

.title-box {
  margin-right: 160px;

  .hello {
    opacity: 0;
    animation: wellcom 1s ease-in-out forwards;
  }

  .h-title {
    opacity: 0;
    animation: wellcom 1s 1s ease-in-out forwards;
    margin-top: 26px;
  }

  .h-desc {
    opacity: 0;
    animation: wellcom 1s 2s ease-in-out forwards;
  }

  @keyframes wellcom {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  p {
    color: #5c9986;
    font-size: 24px;
    text-align: center;
    line-height: 37px;
    font-weight: 500;
  }
}

.login-form {
  width: 470px;
  padding-left: 80px;
}

.wechat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-right: 40px;
  border-right: 1px solid var(--el-border-color);

  .title {
    margin-bottom: 10px !important;
    font-size: 26px;
    color: #333;
  }

  .tips {
    color: #666666;
    font-size: 16px;
  }
}

.other {
  text-align: center;
  margin-top: 40px;
  color: var(--el-color-info);

  .register {
    padding-left: 5px;
    color: var(--el-color-primary-light-3);
  }
}

.login-btn {
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  font-size: 18px;
  margin-top: 12px;
  width: 100%;
  font-weight: 500;
}

.form-box {
  .form-item {
    margin-bottom: 20px;

    .flex {
      flex: 1;
      align-items: center;
    }
  }

  .input {
    height: 36px;
    flex: 1;
  }
}
.forgot {
  display: block;
  text-align: right;
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
  color: var(--w-color-666);
  margin-top: 20px;
}
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url("@/assets/img/login-big-bg.png");
  background-size: 100% 100%;
  position: relative;
}

.login-box {
  box-shadow: 0 0 10px 0 #0000001a;
  background-color: #fff;
  border-radius: 10px;
  padding: 80px;
  display: flex;
  position: relative;
  .login-switch {
    position: absolute;
    top: 0;
    right: 0;
    background: url("@/assets/img/login-check-1.png");
    background-size: cover;
    width: 70px;
    height: 64px;
    cursor: pointer;
    &:hover {
      opacity: 0.6;
    }
    &.login {
      background: url("@/assets/img/login-check.png");
    }
  }
  .title {
    font-size: 24px;
    margin-bottom: 40px;
  }
}

.logo {
  width: 132px;
  position: absolute;
  top: 40px;
  left: 40px;
}
.login-bj {
  width: 351px;
  height: 419px;
}
.erweima img {
  width: 150px;
}
.login-tabs {
  display: flex;
  margin-bottom: 38px;
  .tab {
    font-size: 18px;
    line-height: 24px;
    margin-right: 24px;
    position: relative;
    cursor: pointer;
    &.active {
      color: var(--el-color-primary);
      &:after {
        content: "";
        background-color: var(--el-color-primary);
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 100%;
        height: 2px;
      }
    }
  }
}

.user-xy {
  font-size: 12px;
  .xy-link {
    color: var(--el-color-primary);
  }
}
</style>
