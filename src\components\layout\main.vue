<template>
  <div class="h100" :class="isQianKun ? 'qiankun-main' : ''">
    <loadingPage v-if="loading"></loadingPage>
    <plContainer v-else :headerShow="headerShow" :isCollapse="isCollapse">
      <!-- 菜单栏 start -->
      <template #menu>
        <div class="left-menu">
          <layoutMenu :isCollapse="isCollapse"></layoutMenu>
        </div>
      </template>
      <!-- 菜单栏 end -->
      <!-- header start -->
      <template #header>
        <plHeader
          :breadcrumbItems="breadcrumbItems"
          @lock="headerLock"
          v-if="!isQianKun"
        ></plHeader>
        <ElTooltip content="显示头部">
          <div
            class="ios-rail"
            :class="lockType == 1 ? 'hide' : lockType == 2 ? 'show' : ''"
            @click="headerLock"
          ></div>
        </ElTooltip>
      </template>
      <!-- header end -->
      <template #main>
        <div class="layout-main">
          <ElTooltip :content="tipContent">
            <div class="fold-box" @click="handleFoldMenu">
              <pl-icon
                :name="isCollapse ? 'ArrowRight' : 'ArrowLeft'"
                :size="10"
                color="#A8ABB2FF"
              ></pl-icon>
            </div>
          </ElTooltip>

          <layoutNavTag @tagRefreshPage="refreshPage"></layoutNavTag>
          <!-- 主内容 start -->
          <router-view v-slot="{ Component }">
            <div class="layout-view">
              <transition name="fade" mode="out-in">
                <component
                  v-if="componentIsShow"
                  :is="Component"
                  class="h100 p-re"
                />
              </transition>
            </div>
          </router-view>
          <!-- 主内容 end -->
        </div>
      </template>
    </plContainer>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { plContainer } from "pls-common";
import plHeader from "@/components/header/index.vue";
import layoutMenu from "@/components/menu/index.vue";
import layoutNavTag from "@/components/nav-tag/index.vue";
import loadingPage from "../loadingPage.vue";
import { qiankunWindow } from "vite-plugin-qiankun/dist/helper";
let isCollapse = ref(false);
let tipContent = ref("隐藏侧边栏");
let isQianKun = ref(false);
const loading = ref(false);
if (qiankunWindow.__POWERED_BY_QIANKUN__) {
  isQianKun.value = true;
} else {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
}

let componentIsShow = ref(true);

// 面包屑
const breadcrumbItems = ref([
  {
    path: "/dashboard",
    title: "首页",
  },
]);

/**
 * 刷新页面的辅助函数。
 *
 * 该函数首先隐藏组件，然后等待一段时间后重新显示组件并刷新页面。
 * 这种方式可以提供一个平滑的用户体验，避免直接刷新导致的页面跳动。
 *
 * 注意：该函数使用了Vue的响应式系统来更新组件的显示状态，并利用setTimeout来延迟页面刷新。
 */
const refreshPage = () => {
  // 隐藏组件
  componentIsShow.value = false;
  // 设置延迟，500毫秒后执行刷新操作
  setTimeout(() => {
    // 重新显示组件
    componentIsShow.value = true;
    // 刷新页面
    // router.go(0);
  }, 500);
};
const headerShow = ref(true);
const lockType = ref(0);
/**
 * 头部锁屏
 */
const headerLock = () => {
  headerShow.value = !headerShow.value;
  lockType.value = headerShow.value ? 1 : 2;
};

/**
 * 折叠菜单
 */
const handleFoldMenu = () => {
  isCollapse.value = !isCollapse.value;
  tipContent.value = isCollapse.value ? "显示侧边栏" : "隐藏侧边栏";
};
</script>

<style scoped lang="scss">
.qiankun-main {
  :deep(.el-header.header) {
    display: none;
  }
}

.fold-box {
  position: absolute;
  width: 22px;
  height: 22px;
  background: #fff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.08);
  border-radius: 11px 11px 11px 11px;
  border: 1px solid #efefef;
  top: 30px;
  transform: translateY(-50%);
  left: -11px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 4;
  cursor: pointer;
}
.ios-rail {
  height: 4px;
  background-color: black;
  width: 100px;
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 10px;
  cursor: pointer;
  opacity: 0;
  z-index: -1;
  transition: all 0.3s ease-in-out;
  &.show {
    z-index: 9999;
    animation: show 0.3s 0.5s linear forwards;
  }
  &.hide {
    z-index: 9999;
    animation: hide 0.3s linear forwards;
  }
  &:hover {
    transform: translateX(-50%) scale(1.2);
  }
  @keyframes hide {
    0% {
      opacity: 1;
      width: 100px;
    }
    50% {
      opacity: 0.5;
      width: 50px;
    }
    100% {
      opacity: 0;
      width: 0px;
    }
  }
  @keyframes show {
    0% {
      opacity: 0;
      width: 0px;
    }
    50% {
      opacity: 0.5;
      width: 50px;
    }
    100% {
      opacity: 1;
      width: 100px;
    }
  }
}
.p-re {
  position: relative;
}

.layout-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 22;
  .layout-view {
    flex: 1;
  }
}

.layout-view {
  padding: 15px;
}

.left-menu {
  position: relative;
  height: 100%;
  border-right: 1px solid var(--el-border-color);
  background-color: var(--el-color-primary);
}

.layout-header {
  background-color: var(--el-color-white);
  height: 100%;
}
</style>
<style>
.fade-enter-active {
  transition: all 0.3s ease-out;
}

.fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.fade-enter-from,
.fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
