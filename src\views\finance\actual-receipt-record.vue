<!-- 实收记录 -->
<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="reset()"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <div>
          <pl-button type="primary" @click="handleAdd()">新增</pl-button>
          <pl-button @click="toWriteOff">批量核销</pl-button>
        </div>
        <pl-button type="primary" @click="handleExport" :loading="btnLoading"
          >导出</pl-button
        >
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="actualConfColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, actualConfColumns)"
          @selection-change="selectionChange"
        >
          <!-- 订单类型 -->
          <template #orderType="{ scope }">
            <div class="order-type" :class="scope.row.orderType">
              {{ scope.row.orderTypeName }}
            </div>
          </template>
          <!-- 核销方式 -->
          <template #verificationWay="{ scope }">
            <div v-if="scope.row.verificationWay == 0">自动确认</div>
            <div v-if="scope.row.verificationWay == 1">手动确认</div>
          </template>
          <!-- 创建方式 -->
          <template #createWay="{ scope }">
            <div v-if="scope.row.verificationWay == 0">自动创建</div>
            <div v-if="scope.row.verificationWay == 1">手动创建</div>
          </template>
          <!-- 交易凭证 -->
          <template #voucherPath="{ scope }">
            <img class="voucher-img" :src="scope.row.voucherPath" alt="" />
          </template>
          <!-- 操作 -->
          <template #operation="{ scope }">
            <pl-button
              type="primary"
              link
              @click="affirmReceipt(scope.row)"
              v-if="
                scope.row.receiptStatusName == '待确认' ||
                scope.row.receiptStatusName == '未到账'
              "
              >确认收款</pl-button
            >
            <pl-button
              link
              color="#c08ee2"
              @click="toWriteOff(scope.row)"
              v-if="
                scope.row.receiptStatusName == '已到账' &&
                scope.row.verificationStatusName !== '已核销'
              "
              >核销</pl-button
            >
            <pl-button
              link
              type="danger"
              :loading="scope.row.checkLoading"
              @click="check(scope.row)"
              v-if="scope.row.paymentWayName !== '现金'"
              >查单</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit"
      >
      </drawerFrom>

      <!-- 核销弹窗 -->
      <pl-drawer v-model="moduleOffVisible" title="核销">
        <WriteOff
          :visible="moduleOffVisible"
          :isSingle="isSingle"
          :rowData="rowData"
          @close="moduleOffVisible = false"
          @offDrawer="WriteOffClose"
        ></WriteOff>
      </pl-drawer>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import {
  getBusinessType,
  addActualRecord,
  confirmReceipt,
  checkOrder,
  actualList,
} from "@/api/index";
import { actualConfColumns } from "./config/config";
import { getDictionaryData } from "@/api/dict";
import { plMessage, plMessageBox } from "pls-common";
import WriteOff from "./module/WriteOff.vue";
import PAY from "@/api/pay";

const orderType = ref([]); //订单类型
const paymentWay = ref([]); //收款方式
onMounted(() => {
  //组件加载完成后执行事件
  getDictionaryData({
    dictTypeCode: "order_type",
  }).then((res) => {
    orderType.value = res.filter((item) => item?.value != "TK");
  });
  getDictionaryData({
    dictTypeCode: "payment_way",
  }).then((res) => {
    paymentWay.value = res;
  });

  getBusType();
});

// 获取业务类型
const businessType = ref([]); //业务类型
const getBusType = async () => {
  const res = await getBusinessType();
  businessType.value = res.data;
};

// 查询表单的数据
const queryForm = ref({ isVerificationPage: false });

// 使用 useTable 钩子管理表格相关逻辑
const {
  loadData,
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  handleAdd,
  handleSort,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/pay/receiptRecord/pageReceiptRecord",
  add: "/pay/receiptRecord/saveReceiptRecord",
  queryForm,
});

const reset = async () => {
  tabLoading.value = true;
  queryForm.value = { isVerificationPage: false };
  const params = {
    ...queryForm.value,
    current: 1,
    limit: 10,
  };
  try {
    const { data: res } = await actualList(params);
    tableData.value = res.records;
    dataTotal.value = res.total;
    current.value = res.current;
  } catch (error) {
    console.log(error);
  } finally {
    tabLoading.value = false;
  }
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "实收单号",
    prop: "receiptNo",
    type: "input",
  },
  {
    label: "订单类型",
    prop: "orderTypeList",
    type: "select",
    options: orderType,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "交易流水号",
    prop: "transactionNo",
    type: "input",
  },
  {
    label: "付款方名称",
    prop: "payerName",
    type: "input",
  },
  {
    label: "更新时间",
    prop: "updateTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      queryForm.value.updateTimeStart = e[0] + " 00:00:00";
      queryForm.value.updateTimeEnd = e[1] + " 23:59:59";
    },
  },
  {
    label: "创建时间",
    prop: "createTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      queryForm.value.createTimeStart = e[0] + " 00:00:00";
      queryForm.value.createTimeEnd = e[1] + " 23:59:59";
    },
  },
  {
    label: "业务类型",
    type: "select",
    prop: "businessTypeList",
    options: businessType,
    labelKey: "businessTypeName",
    valueKey: "businessType",
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "收款方式",
    type: "select",
    prop: "paymentWayList",
    options: paymentWay,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
]);

// 弹窗表单配置
const baseDrawerFields = ref([
  {
    label: "订单类型",
    prop: "orderType",
    type: "select",
    options: orderType,
    placeholder: "请选择订单类型",
    rules: [{ required: true, message: "请选择订单类型" }],
  },
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
    placeholder: "请输入订单号",
    rules: [{ required: true, message: "请输入订单号" }],
  },
  {
    label: "付款方名称",
    prop: "payerName",
    type: "input",
    placeholder: "请输入付款方名称",
    rules: [{ required: true, message: "请输入付款方名称" }],
  },
  {
    label: "收款方式",
    prop: "paymentWay",
    type: "select",
    options: paymentWay,
    placeholder: "请选择收款方式",
    rules: [{ required: true, message: "请选择收款方式" }],
  },
  {
    label: "支付账户",
    prop: "payerBankNo",
    type: "input",
    placeholder: "请输入支付账户",
    rules: [{ required: true, message: "请输入支付账户" }],
  },
  {
    label: "实收金额（元）",
    prop: "receiptAmount",
    type: "number",
    placeholder: "请输入实收金额",
    rules: [{ required: true, message: "请输入实收金额" }],
  },
  {
    label: "交易流水号",
    prop: "transactionNo",
    type: "input",
    placeholder: "请输入交易流水号",
    rules: [{ required: true, message: "请输入交易流水号" }],
  },
  {
    label: "业务类型",
    prop: "businessType",
    type: "select",
    options: businessType,
    labelKey: "businessTypeName",
    valueKey: "businessType",
    placeholder: "请选择业务类型",
    rules: [{ required: true, message: "请选择业务类型" }],
  },
  {
    label: "关联合同",
    prop: "covenantNo",
    type: "input",
    placeholder: "请输入关联合同编号",
  },
  {
    label: "账单备注",
    prop: "remark",
    type: "input",
    placeholder: "请输入账单备注",
  },
  {
    label: "交易凭证",
    prop: "voucherPath",
    type: "upload",
    fileType: "image",
  },
]);

// 动态计算drawerFields，根据收款方式过滤字段
const drawerFields = computed(() => {
  const fields = [...baseDrawerFields.value];
  // 如果收款方式是现金，隐藏交易流水号和支付账户字段
  if (fromData.value.paymentWay === "XJ") {
    return fields.filter(
      (field) => field.prop !== "transactionNo" && field.prop !== "payerBankNo"
    );
  }
  return fields;
});

// 确认收款
const affirmReceipt = (row) => {
  plMessageBox
    .confirm("是否确认收款？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(async () => {
      try {
        const params = {
          receiptRecordId: row.receiptRecordId,
          receiptStatus: "YDZ",
        };
        const res = await confirmReceipt(params);
        if (res.code == 200) {
          plMessage("确认收款成功", "success");
          loadData();
        }
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};

// 点击核销
const moduleOffVisible = ref(false);
const isSingle = ref(false); //  是否为单条核销
const rowData = ref({}); // 单条的核销数据
const toWriteOff = (row) => {
  isSingle.value = row?.orderNo ? true : false;
  if (isSingle.value) {
    rowData.value = row;
  }
  moduleOffVisible.value = true;
};

const WriteOffClose = () => {
  moduleOffVisible.value = false;
  loadData();
};

// 点击查单
const check = async (row) => {
  console.log("row", row);
  try {
    row.checkLoading = true;
    const res = await checkOrder({
      outTradeNo: row.outTradeNo,
      paymentWay: row.paymentWay,
    });
    console.log(res);
    if (res.code == 200) {
      plMessageBox
        .confirm(res.data.tradeStateDesc || res.message, "查单成功", {
          showCancelButton: false,
          showConfirmButton: false,
        })
        .then(async () => {})
        .catch(() => {});
    }
  } catch (error) {
    console.log(error);
  } finally {
    row.checkLoading = false;
  }
};

// 导出
const btnLoading = ref(false);
const selectedRows = ref([]); // 选中的行数据
const selectionChange = (selection) => {
  selectedRows.value = selection;
};

const handleExport = () => {
  if (!tableData.value.length) {
    plMessage("暂无可导出的数据", "warning");
    return;
  }

  btnLoading.value = true;
  let receiptRecordIdList = [];
  if (selectedRows.value.length === 0) {
    receiptRecordIdList = [];
  } else {
    receiptRecordIdList = selectedRows.value.map(
      (item) => item.receiptRecordId
    );
  }
  const params = {
    ...queryForm.value,
    receiptRecordIdList,
  };
  PAY.exportReceiptRecord(params)
    .then((res) => {
      console.log(res);
      if (res.code == 200) {
        window.open(res.data);
        plMessage(res.message, "success");
      }
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

/**
 * 新增提交
 */
const handleDrawerSubmit = async () => {
  console.log(drawerTitle.value);
  const params = { ...fromData.value };
  params.receiptStatus = "DQR";
  params.orgId = JSON.parse(localStorage.getItem("userInfo"))?.orgId || "56";

  // 如果是现金支付，清空交易流水号和支付账户的校验
  if (params.paymentWay === "XJ") {
    params.transactionNo = undefined;
    params.payerBankNo = undefined;
  }

  if (drawerTitle.value == "新增") {
    console.log(params);
    try {
      const res = await addActualRecord(params);
      console.log(res);
      if (res.code == 200) {
        plMessage("新增成功", "success");
        drawerVisible.value = false;
        loadData();
      }
    } catch (error) {
      console.log(error);
    }
  }
};
</script>

<style lang="scss" scoped>
.operation-btns {
  display: flex;
  justify-content: space-between;
}

.voucher-img {
  width: 50px;
  height: 70px;
  border-radius: 4px;
}

.order-type {
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  width: 48px;
  display: flex;
  align-content: center;
  justify-content: center;
}
.TK {
  background: rgba(245, 63, 27, 0.1);
  color: rgb(245, 63, 27);
}
.PT {
  background: rgba(27, 118, 245, 0.1);
  color: rgb(27, 118, 245);
}
.BJ {
  background: rgba(3, 192, 120, 0.1);
  color: rgb(3, 192, 120);
}
</style>
