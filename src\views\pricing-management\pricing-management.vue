<template>
  <pl-card>
    <plDragRange :initialLeftWidth="600">
      <template #left>
        <scheme @click="handleRowClick"></scheme>
      </template>
      <template #right>
        <pricing :pricingPlanId="pricingPlanId"></pricing>
      </template>
    </plDragRange>
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import scheme from "./scheme.vue";
import pricing from "./pricing.vue";
const pricingPlanId = ref("");
const handleRowClick = (row) => {
  pricingPlanId.value = row.pricingPlanId;
};
</script>

<style lang="scss" scoped></style>
