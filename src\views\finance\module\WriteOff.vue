<template>
  <div class="h100">
    <div class="range">
      <pl-drag-range :initialLeftWidth="800" class="drag-ranage">
        <template #left>
          <!-- 左侧实收列表 -->
          <div class="form">
            <!-- Head -->
            <div class="form-head">
              <pl-button
                type="primary"
                @click="leftAddRow"
                v-if="!props.isSingle"
                >新增</pl-button
              >
              <div style="height: 32px" v-else></div>
              <div class="form-head-count">
                <div class="form-head-count-text">
                  <span class="form-head-count-text--title">实收金额：</span>
                  <span class="form-head-count-text--price">
                    {{ leftOriginPrice }}元
                  </span>
                </div>
                <div class="form-head-count-text">
                  <span class="form-head-count-text--title">未核销：</span>
                  <span class="form-head-count-text--price">
                    {{ leftOriginUnverifiedPrice }}元
                  </span>
                </div>
              </div>
            </div>
            <!-- Table -->
            <div class="card-table mt20">
              <pl-table
                :columns="actualColumns"
                :data="actualData"
                class="table"
                v-loading="actualLoading"
              >
                <template #operate="{ scope }">
                  <pl-icon
                    v-if="!props.isSingle"
                    name="Remove"
                    :size="18"
                    color="#f45555"
                    style="cursor: pointer"
                    @click="leftDeleteRow(scope.row)"
                  ></pl-icon>
                </template>
              </pl-table>
            </div>
          </div>
        </template>

        <template #right>
          <!-- 右侧应收核销列表 -->
          <div class="form">
            <div class="form-head">
              <pl-button type="primary" @click="rightAddRow">新增</pl-button>
              <div class="form-head-count">
                <div class="form-head-count-text">
                  <span class="form-head-count-text--title">应收金额：</span>
                  <span class="form-head-count-text--price">
                    {{ rightOriginPrice }}元
                  </span>
                </div>
                <div class="form-head-count-text">
                  <span class="form-head-count-text--title">未核销：</span>
                  <span class="form-head-count-text--price">
                    {{ rightOriginUnverifiedPrice }}元
                  </span>
                </div>
              </div>
            </div>
            <!-- Table -->
            <div class="card-table mt20">
              <pl-table
                :columns="arColumns"
                :data="arData"
                class="table"
                v-loading="arLoading"
              >
                <!-- 订单号 -->
                <template #orderNo="{ scope }">
                  <div class="table-text">{{ scope.row.orderNo }}</div>
                </template>
                <!-- 应收单号 -->
                <template #receivableNo="{ scope }">
                  <div class="table-text">{{ scope.row.receivableNo }}</div>
                </template>
                <!-- 核销金额填写 -->
                <template #amount="{ scope }">
                  <pl-input
                    v-model="scope.row.amount"
                    type="number"
                    :class="{ 'error-border': scope.row.hasError }"
                    @input="input($event, scope.row)"
                    placeholder="请输入核销金额"
                  >
                    <template #prefix>
                      <span>@</span>
                    </template>
                  </pl-input>
                </template>
                <!-- 操作 -->
                <template #operate="{ scope }">
                  <pl-icon
                    name="Remove"
                    :size="18"
                    color="#f45555"
                    style="cursor: pointer"
                    @click="rightDeleteRow(scope.row)"
                  ></pl-icon>
                </template>
              </pl-table>
            </div>
          </div>
        </template>
      </pl-drag-range>
    </div>

    <!-- 确定/取消 -->
    <div class="operation-btns">
      <pl-button style="width: 200px" @click="cancel"> 取消 </pl-button>
      <pl-button
        type="primary"
        style="width: 200px"
        @click="confirm"
        :loading="confirmLoading"
      >
        确定
      </pl-button>
    </div>

    <div style="height: 48px"></div>

    <!-- 左侧新增弹窗 -->
    <pl-dialog
      v-model="leftDialogShow"
      title="新增实收"
      append-to-body
      align-center
      width="1300px"
      :showCancel="true"
      :loading="leftDialogLoad"
      @confirm="leftAddConfirm"
      @cancel="leftDialogShow = false"
    >
      <template #content>
        <!-- 搜索条件 -->
        <pl-form
          confirmButtonText="搜索"
          cancelButtonText="重置"
          :fields="leftFormColumns"
          :form="leftQueryForm"
          inline
          :span="6"
          clear
          @confirm="leftSearch"
          @cancel="leftResetSearch"
        ></pl-form>
        <!-- 搜索表格 -->
        <pl-table
          ref="leftSearchTableRef"
          :columns="leftSearchColumns"
          :data="leftSearchData"
          class="table"
          v-loading="leftSearchLoading"
          @selection-change="leftSearchSelection"
        >
          <!-- 订单类型 -->
          <template #orderType="{ scope }">
            <div class="order-type" :class="scope.row.orderType">
              {{ scope.row.orderTypeName }}
            </div>
          </template>
        </pl-table>
        <!-- 分页 -->
        <pl-pagination
          :currentPage="leftQueryForm.current"
          :total="leftDataTotal"
          @size-change="leftSizeChange"
          @current-change="leftCurrentChange"
        ></pl-pagination>
      </template>
    </pl-dialog>

    <!-- 右侧新增弹窗 -->
    <pl-dialog
      v-model="rightDialogShow"
      title="新增应收"
      append-to-body
      align-center
      width="1300px"
      :showCancel="true"
      :loading="rightDialogLoad"
      @confirm="rightAddConfirm"
      @cancel="rightDialogShow = false"
    >
      <template #content>
        <!-- 搜索条件 -->
        <pl-form
          confirmButtonText="搜索"
          cancelButtonText="重置"
          :fields="rightFormColumns"
          :form="rightQueryForm"
          inline
          :span="6"
          clear
          @confirm="rightSearch"
          @cancel="rightResetSearch"
        ></pl-form>
        <!-- 搜索表格 -->
        <pl-table
          ref="rightSearchTableRef"
          :columns="rightSearchColumns"
          :data="rightSearchData"
          class="table"
          v-loading="rightSearchLoading"
          @selection-change="rightSearchSelection"
        >
        </pl-table>
        <!-- 分页 -->
        <pl-pagination
          :currentPage="rightQueryForm.current"
          :total="rightDataTotal"
          @size-change="rightSizeChange"
          @current-change="rightCurrentChange"
        ></pl-pagination>
      </template>
    </pl-dialog>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from "vue";
import { actualColumns, arColumns } from "./config";
import { getDictionaryData } from "@/api/dict";
import { actualList, arList, verifyReceipt } from "@/api/index";
import { plMessage, plMessageBox } from "pls-common";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isSingle: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Object,
    default: () => {},
  },
});
const orderType = ref([]); // 订单类型
const verificationStatus = ref([]); // 核销状态

const emit = defineEmits(["update:visible", "offDrawer"]);

// ^ 左侧新增按钮点击
const leftAddRow = () => {
  // 获取订单类型/核销状态
  getDict();
  // 获取列表数据
  getActualList();
  leftDialogShow.value = true;
};

// 左侧新增弹窗
const leftDialogLoad = ref(false); // 弹窗确认按钮加载
const leftDialogShow = ref(false);
// 左侧新增弹窗确认
const leftAddConfirm = () => {
  if (leftCurSelection.value.length) {
    actualData.value = leftCurSelection.value;
    leftDialogShow.value = false;
    // 实收金额计算
    leftOriginPrice.value = actualData.value.reduce((acc, item) => {
      return acc + Number(item.receiptAmount);
    }, 0);
    // 未核销金额计算
    leftOriginUnverifiedPrice.value = actualData.value.reduce((acc, item) => {
      return acc + Number(item.unverifiedAmount);
    }, 0);
  }
};
// 左侧搜索条件
const leftQueryForm = ref({ limit: 10, current: 1, isVerificationPage: true });
const leftFormColumns = ref([
  {
    label: "订单类型",
    prop: "orderTypeList",
    type: "select",
    options: orderType,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "核销状态",
    prop: "verificationStatusList",
    type: "select",
    options: verificationStatus,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "付款方名称",
    prop: "payerName",
    type: "input",
  },
]);

// 左侧实收金额
const actualData = ref([]);
const actualLoading = ref(false);
const leftOriginPrice = ref(0); // 实收金额
const leftOriginUnverifiedPrice = ref(0); // 未核销金额
// 获取实收列表
const leftSearchTableRef = ref(null);
const getActualList = async () => {
  leftSearchLoading.value = true;
  try {
    const params = { ...leftQueryForm.value };
    const res = await actualList(params);
    if (res.data.records.length) {
      leftSearchData.value = res.data.records || [];
      // 赋值总条数
      leftDataTotal.value = res.data.total;
    } else {
      leftSearchData.value = [];
      leftDataTotal.value = 0;
    }
    // 复选选中
    nextTick(() => {
      // 将actualData列表中等于searchData的数据选中
      leftSearchData.value.forEach((item) => {
        const isInActualData = actualData.value.some(
          (actualItem) => actualItem.receiptRecordId === item.receiptRecordId
        );
        if (isInActualData) {
          leftSearchTableRef.value.toggleRowSelection(item, true);
        }
      });
    });
  } catch (error) {
    console.log(error);
  } finally {
    leftSearchLoading.value = false;
  }
};
// 删除行
const leftDeleteRow = (row) => {
  plMessageBox
    .confirm("是否确认删除？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(() => {
      actualData.value = actualData.value.filter(
        (item) => item.receiptRecordId !== row.receiptRecordId
      );
      // 实收金额计算
      leftOriginPrice.value = actualData.value.reduce((acc, item) => {
        return acc + Number(item.receiptAmount);
      }, 0);
      // 未核销金额计算
      leftOriginUnverifiedPrice.value = actualData.value.reduce((acc, item) => {
        return acc + Number(item.unverifiedAmount);
      }, 0);
    })
    .catch(() => {});
};
// 获取订单类型/核销状态
const getDict = async () => {
  const res = await getDictionaryData({
    dictTypeCode: "order_type",
  });
  orderType.value = res;
  const res2 = await getDictionaryData({
    dictTypeCode: "verification_status",
  });
  verificationStatus.value = res2.filter((item) => item.value != "YHX");
};

// 左侧弹窗搜索表格
const leftSearchData = ref([]);
const leftSearchLoading = ref(true);
const leftSearchColumns = ref([
  { type: "selection", width: 80 },
  {
    label: "实收单号",
    prop: "receiptNo",
    minWidth: 180,
  },
  {
    label: "订单号",
    prop: "orderNo",
    minWidth: 180,
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
    template: "orderType",
    minWidth: 150,
  },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    minWidth: 150,
  },
  {
    label: "实收金额",
    prop: "receiptAmount",
    price: true,
    minWidth: 150,
    align: "center",
  },
  {
    label: "未核销金额",
    prop: "unverifiedAmount",
    price: true,
    minWidth: 150,
    align: "center",
  },
  {
    label: "付款方名称",
    prop: "payerName",
    minWidth: 150,
  },
]);
// 点击搜索
const leftSearch = () => {
  leftQueryForm.value.current = 1;
  getActualList();
};
// 点击重置
const leftResetSearch = () => {
  leftQueryForm.value = { limit: 10, current: 1, isVerificationPage: true };
  getActualList();
};
// 搜索表格选择
const leftCurSelection = ref([]); // 当前选择
const leftSearchSelection = (selection) => {
  leftCurSelection.value = selection;
};
// 分页内容
const leftDataTotal = ref(0);
const leftSizeChange = (size) => {
  leftQueryForm.value.limit = size;
  getActualList();
};
const leftCurrentChange = (current) => {
  leftQueryForm.value.current = current;
  getActualList();
};

// ^ 右侧新增按钮点击
const rightAddRow = () => {
  getDict();
  getArList();
  rightDialogShow.value = true;
};
// 右侧新增弹窗
const rightDialogLoad = ref(false); // 弹窗确认按钮加载
const rightDialogShow = ref(false);
// 右侧新增弹窗确认
const rightAddConfirm = () => {
  if (rightCurSelection.value.length) {
    arData.value = rightCurSelection.value;
    rightDialogShow.value = false;
    // 应收金额计算
    rightOriginPrice.value = arData.value.reduce((acc, item) => {
      return acc + Number(item.receivableAmount);
    }, 0);
    // 未核销金额计算
    rightOriginUnverifiedPrice.value = arData.value.reduce((acc, item) => {
      return acc + Number(item.unverifiedAmount);
    }, 0);
  }
};
// 右侧搜索条件
const rightQueryForm = ref({ limit: 10, current: 1, isVerificationPage: true });
const rightFormColumns = ref([
  {
    label: "",
    prop: "likeString",
    type: "input",
    placeholder: "请输入订单号、合同编号、付款方名称",
  },
  {
    label: "订单类型",
    prop: "orderTypeList",
    type: "select",
    options: orderType,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "核销状态",
    prop: "verificationStatusList",
    type: "select",
    options: verificationStatus,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
]);
// 右侧搜索表格
const arLoading = ref(false);
const arData = ref([]);
const rightOriginPrice = ref(0); // 应收金额
const rightOriginUnverifiedPrice = ref(0); // 未核销金额
const rightSearchData = ref([]);
const rightSearchLoading = ref(true);
const rightSearchTableRef = ref(null);
const rightSearchColumns = ref([
  { type: "selection", width: 80 },
  {
    label: "应收单号",
    prop: "receivableNo",
    minWidth: 140,
  },
  {
    label: "订单号",
    prop: "orderNo",
    minWidth: 140,
  },
  {
    label: "付款方名称",
    prop: "payerName",
    minWidth: 120,
  },
  {
    label: "应收金额(元)",
    prop: "receivableAmount",
    price: true,
    minWidth: 140,
    align: "center",
  },
  {
    label: "未核销金额(元)",
    prop: "unverifiedAmount",
    price: true,
    minWidth: 140,
    align: "center",
  },
  {
    label: "到期日期",
    prop: "expirationTime",
    minWidth: 160,
  },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    minWidth: 120,
  },
  {
    label: "核销时间",
    prop: "verificationTime",
    minWidth: 160,
  },
]);
// 右侧搜索表格选择
const rightCurSelection = ref([]); // 当前选择
const rightSearchSelection = (selection) => {
  rightCurSelection.value = selection;
};
// 分页内容
const rightDataTotal = ref(0);
const rightSizeChange = (size) => {
  rightQueryForm.value.limit = size;
  getArList();
};
const rightCurrentChange = (current) => {
  rightQueryForm.value.current = current;
  getArList();
};
const getArList = async () => {
  rightSearchLoading.value = true;
  try {
    const res = await arList(rightQueryForm.value);
    rightSearchData.value = res.data.records || [];
    rightDataTotal.value = res.data.total;
    // 复选选中
    nextTick(() => {
      // 将arData列表中等于rightSearchData的数据选中
      rightSearchData.value.forEach((item) => {
        const isInArData = arData.value.some(
          (arItem) => arItem.receivableRecordId === item.receivableRecordId
        );
        if (isInArData) {
          rightSearchTableRef.value.toggleRowSelection(item, true);
        }
      });
    });
  } catch (error) {
    console.log(error);
  } finally {
    rightSearchLoading.value = false;
  }
};
// 右侧表格搜索/重置
const rightSearch = () => {
  rightQueryForm.value.current = 1;
  getArList();
};
const rightResetSearch = () => {
  rightQueryForm.value = { limit: 10, current: 1, isVerificationPage: true };
  getArList();
};

// 右侧删除行
const rightDeleteRow = (row) => {
  plMessageBox
    .confirm("是否确认删除？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(() => {
      arData.value = arData.value.filter(
        (item) => item.receivableRecordId !== row.receivableRecordId
      );
      // 应收金额计算
      rightOriginPrice.value = arData.value.reduce((acc, item) => {
        return acc + Number(item.receivableAmount);
      }, 0);
      // 未核销金额计算
      rightOriginUnverifiedPrice.value = arData.value.reduce((acc, item) => {
        return acc + Number(item.unverifiedAmount);
      }, 0);
    })
    .catch(() => {});
};
// 右侧核销金额输入
const input = (e, row) => {
  if (Number(e) > Number(row.unverifiedAmount)) {
    row.hasError = true;
    plMessage("核销金额不能大于未核销金额", "error");
  } else {
    row.hasError = false;
  }
};

// ^ 确定按钮
const confirmLoading = ref(false);
const confirm = async () => {
  // 清理所有 hasError, 只保留大于未核销金额的错误
  arData.value.forEach((item) => {
    if (item.amount && Number(item.amount) <= Number(item.unverifiedAmount)) {
      item.hasError = false;
    }
  });
  // 检查是否有输入验证未通过的记录
  const hasError = arData.value.some((item) => item.hasError);
  if (hasError) {
    plMessage("核销金额不能大于未核销金额", "error");
    return;
  }
  // 检查是否有核销金额为空的记录，并赋值 hasError
  let hasEmptyAmount = false;
  arData.value.forEach((item) => {
    if (!item.amount || item.amount === "") {
      item.hasError = true;
      hasEmptyAmount = true;
    }
  });
  if (hasEmptyAmount) {
    plMessage("请填写所有应收记录的核销金额", "error");
    return;
  }
  // 金额校验
  if (!actualData.value.length) {
    plMessage("请选择实收核销记录", "warning");
    return;
  }
  if (!arData.value.length) {
    plMessage("请选择应收核销记录", "warning");
    return;
  }
  // 校验右侧核销金额总和不能大于左侧未核销金额
  const allPrice = arData.value.reduce((acc, item) => {
    return acc + Number(item.amount || 0);
  }, 0);
  if (allPrice > leftOriginUnverifiedPrice.value) {
    plMessage("应收核销总金额不能大于实收未核销金额", "error");
    return;
  }

  confirmLoading.value = true;
  try {
    const receiptRecordIdList = actualData.value.map((i) => i.receiptRecordId);
    const receivableRecordVerificationItemList = arData.value.map((i) => {
      return {
        receivableRecordId: i.receivableRecordId,
        verifiedAmount: i.amount,
      };
    });
    const params = {
      receiptRecordIdList,
      receivableRecordVerificationItemList,
    };
    const res = await verifyReceipt(params);
    if (res.code == 200) {
      plMessage("核销成功", "success");
      cancel();
    }
  } catch (error) {
    console.log(error);
  } finally {
    confirmLoading.value = false;
  }
};
const cancel = () => {
  // 清空左侧数据
  actualData.value = [];
  leftOriginPrice.value = 0;
  leftOriginUnverifiedPrice.value = 0;

  // 清空右侧数据
  arData.value = [];
  rightOriginPrice.value = 0;
  rightOriginUnverifiedPrice.value = 0;

  // 清空左侧弹窗数据
  leftSearchData.value = [];
  leftDataTotal.value = 0;
  leftCurSelection.value = [];

  // 清空右侧弹窗数据
  rightSearchData.value = [];
  rightDataTotal.value = 0;
  rightCurSelection.value = [];

  // 重置查询表单
  leftQueryForm.value = { limit: 10, current: 1, isVerificationPage: true };
  rightQueryForm.value = { limit: 10, current: 1, isVerificationPage: true };

  // 关闭弹窗
  leftDialogShow.value = false;
  rightDialogShow.value = false;

  emit("offDrawer");
};

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.isSingle) {
        // 单条核销
        actualData.value = [props.rowData];
        // 金额赋值
        leftOriginPrice.value = props.rowData.receiptAmount;
        leftOriginUnverifiedPrice.value = props.rowData.unverifiedAmount;
        console.log("单条核销", actualData.value);
      } else {
        // 批量核销
        console.log("批量核销");
      }
    } else {
      console.log("关闭");
      cancel();
    }
  }
);

// 左侧新增按钮取消
watch(
  () => leftDialogShow.value,
  (newVal) => {
    if (!newVal) {
      setTimeout(() => {
        leftQueryForm.value = {
          limit: 10,
          current: 1,
          isVerificationPage: true,
        };
        leftSearchData.value = [];
        leftDataTotal.value = 0;
      }, 400);
    }
  }
);

// 右侧新增按钮取消
watch(
  () => rightDialogShow.value,
  (newVal) => {
    if (!newVal) {
      setTimeout(() => {
        rightQueryForm.value = {
          limit: 10,
          current: 1,
          isVerificationPage: true,
        };
        rightSearchData.value = [];
        rightDataTotal.value = 0;
      }, 400);
    }
  }
);
</script>

<style lang="scss" scoped>
.form {
  display: flex;
  height: 100%;
  flex-direction: column;
  .form-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 10px;
    .form-head-count {
      display: flex;
      align-items: center;
      gap: 20px;
      .form-head-count-text {
        &--title {
          display: inline-block;
          font-size: 13px;
          color: #161616;
        }
        &--price {
          font-size: 16px;
          font-weight: 600;
          color: #f45555;
        }
      }
    }
  }
}

.card-table {
  flex: 1;
  width: 100%;
  .table-text {
    font-size: 12.5px;
    color: #161616;
  }
}

.operation-btns {
  width: 100%;
  height: 48px;
  background: #fff;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 0 20px;
  position: absolute;
  bottom: 0;
  left: 0;
}
.range {
  height: calc(100% - 48px);
}

.error-border {
  :deep(.el-input__wrapper) {
    border: 1px solid #f45555 !important;
    box-shadow: 0 0 0 0px #f45555 !important;
  }
}

.order-type {
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  width: 48px;
  display: flex;
  align-content: center;
  justify-content: center;
}
.TK {
  background: rgba(245, 63, 27, 0.1);
  color: rgb(245, 63, 27);
}
.PT {
  background: rgba(27, 118, 245, 0.1);
  color: rgb(27, 118, 245);
}
.BJ {
  background: rgba(3, 192, 120, 0.1);
  color: rgb(3, 192, 120);
}
</style>
