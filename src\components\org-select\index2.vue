<template>
  <pl-cascader
    v-model="selectVal"
    :options="options"
    :cascaderProps="cascaderProps"
    @change="change"
    placeholder="请选择所属机构"
    filterable
  ></pl-cascader>
</template>

<script setup>
import { ref, watch } from "vue";
import { getParentOrgTreeList } from "@/api/index";
import { findNodeAndParentsById } from "@/utils/index";
/**
 * 根据id回显
 */
const getName = (id) => {
  if (!id) return;
  const ids = findNodeAndParentsById(options.value, id);
  selectVal.value = ids;
};

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    deault: "",
  },
});
const options = ref([]);

const selectVal = ref(props.modelValue);
watch(
  () => props.modelValue,
  (val) => {
    selectVal.value = val;
    getName(val);
  }
);

const cascaderProps = {
  label: "orgName",
  value: "id",
  children: "children",
  checkStrictly: true,
};

// 请求接口
getParentOrgTreeList().then((res) => {
  options.value = res.data;
  getName(selectVal.value);
});

const emit = defineEmits(["change", "update:modelValue"]);
const change = (val, val2, ref) => {
  ref.togglePopperVisible();
  emit("update:modelValue", val2.orgId);
  emit("change", val2);
};
</script>

<style lang="scss" scoped></style>
