import request from "./request";
/**
 * 获取联系人详情
 */
export const getContactsDetail = (params: any) => {
  return request.get(`/contacts/getContactsDetail/${params.contactsId}`);
};
/**
 * 获取联系人列表
 */
export const getContactsBaseList = () => {
  return request.get(`/contacts/getContactsBaseList`);
};
/**
 * 获取客户基础信息列表
 */
export const getCustomerList = (params: any) => {
  return request.get(`/customer/getCustomerBaseList`, params);
};

/**
 * 获取客户详情
 */
export const getCustomerDetail = (params: any) => {
  return request.get(`/customer/getCustomerDetail/${params.customerId}`);
};

/**
 * 新增客户
 */
export const postAddCustomer = (params: any) => {
  return request.post(`/contacts/addSysContacts`, params);
};

/**
 * 解绑定客户下级
 */
export const getUnbindCustomerSub = (params: any) => {
  return request.get(`/customerSubUpdate/unbindCustomerSub`, params);
};

/**
 * 绑定客户下级
 */
export const getBindCustomerSub = (params: any) => {
  return request.get(`/customerSubUpdate/bindCustomerSub`, params);
};

/**
 * 导出客户信息
 */
export const postExportCustomer = (params: any) => {
  return request.post(`/customer/exportCustomer`, params);
};

/**
 * 查看合同详情
 */
export const postCovenantDetail = (params: any) => {
  return request.post(`/contract/covenant/covenantDetail/${params.covenantId}`);
};

/**
 * 合同归档
 */
export const postCovenantArchive = (params: any) => {
  return request.post(
    `/contract/covenant/covenantArchive/${params.covenantId}`
  );
};

/**
 * 产品列表
 */
export const getProductList = (params: any) => {
  return request.get(`/contract/product/getProductList`, params);
};

/**
 * 根据产产品获取服务
 */
export const getServiceList = (params: any) => {
  return request.post(`/contract/service/getServiceList`, params);
};
