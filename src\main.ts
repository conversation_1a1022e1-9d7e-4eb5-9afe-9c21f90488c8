import { createApp } from "vue";
import "./style.scss";
import App from "./App.vue";
import router from "./router";
import {
  ElLoadingDirective,
  ElTooltip,
  ElDropdown,
  ElPopover,
} from "element-plus";
import "element-plus/theme-chalk/src/loading.scss";
import "element-plus/theme-chalk/src/steps.scss";
import "element-plus/theme-chalk/src/step.scss";
import plsCommon, { plMessage, setMapConfig, storage } from "pls-common";
import { createPinia } from "pinia";
import piniaPluginPersist from "pinia-plugin-persist"; // pinia持久化插件
import { generateRoutes } from "@/utils/index";
import { apiGetAuth } from "@/api/auth";
import "@/utils/upload.ts";
import { setupPermissionDirective } from "@/hooks/usePermission";
import { getCookie } from "@/utils/cookie";
import {
  renderWithQiankun,
  qiankunWindow,
} from "vite-plugin-qiankun/dist/helper";
/**
 * 注册抽屉组件
 */
import plDrawer from "@/components/module/drawer.vue";
import citySelectPro from "@/components/city-select-pro/city-select-pro.vue";

//字典下拉选项
import plDictSelect from "@/components/dict-select/select.vue";

// 机构管理组件
import orgSelect from "@/components/org-select/index.vue";
// 标签选择组件
import labelSelect from "@/components/label-select/index.vue";
// 线路类型组件
import lineType from "@/components/line-type/line-type.vue";
// 线路列表组件
import lineList from "@/components/line-type/line-list.vue";

/**
 * 地图配置
 */
setMapConfig({
  key: "55a839a05a7725b07f45cc4212962fe0",
  securityJsCode: "4613089017cfd4ec12c5322c3160d257",
});

const userCookie: string | null = getCookie("userInfo");
let userInfo: any = "";
if (userCookie) {
  userInfo = JSON.parse(userCookie);
}
let app: any = null;
const createAPP = () => {
  // 状态管理器
  app = createApp(App);
  app.component("pl-drawer", plDrawer);
  app.component("pl-dict-select", plDictSelect);
  app.component("orgSelect", orgSelect);
  app.component("labelSelect", labelSelect);
  app.component("pl-line-type", lineType);
  app.component("pl-line-list", lineList);
  app.component("pl-city-select", citySelectPro);
  const pinia = createPinia();
  pinia.use(piniaPluginPersist);
  app.directive("loading", ElLoadingDirective);
  app.use(ElTooltip);
  app.use(ElDropdown);
  app.use(ElPopover);
  app.provide("plMessage", plMessage);
  plsCommon.install(app);
  app.use(router);
  app.use(pinia);
  setupPermissionDirective(app);
  // 在挂载这里需要做判断，判断是在qiankun环境下还是独立环境下

  if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
    // 独立环境
    app.mount("#app");
  } else if (isQiankunMounted && pendingAppMount) {
    isAppMounted = true;
    // qiankun环境，并且子应用已经挂载
    pendingAppMount();
  }
};

// 确保样式在 head 中的辅助函数
function ensureStylesInHead() {
  // 延迟执行，确保所有资源都已加载
  setTimeout(() => {
    // 查找所有 CSS 链接和样式标签
    const styles = document.querySelectorAll('link[rel="stylesheet"], style');
    styles.forEach((style) => {
      // 检查是否已在 head 中
      if (!document.head.contains(style)) {
        // 克隆并添加到 head
        const clonedStyle = style.cloneNode(true) as HTMLElement;
        document.head.appendChild(clonedStyle);

        // 可选：从原位置移除（取决于你的需求）
        // style.parentNode?.removeChild(style);
      }
    });

    console.log("CSS styles have been moved to head");
  }, 100);
}

async function initApp() {
  if (userInfo) {
    try {
      const res = await apiGetAuth({});
      storage.setItem("userAuthMenu", res.data);
      const appId = import.meta.env.VITE_APP_ID;
      const appInfo = res.data.find(
        (item: any) => item.sysAppInfoVO.appId == appId
      );

      await generateRoutes(appInfo.treeList);

      // 在路由生成后再挂载应用
      createAPP();
    } catch (error) {
      console.error("初始化路由失败:", error);
    }
  } else {
    createAPP();
  }
}

let isQiankunMounted = false; // 标识子应用是否已经挂载
let pendingAppMount: any = null; //子应用挂载函数
let isAppMounted = false; // 标识子应用是否已经初始化
renderWithQiankun({
  async mount(props) {
    qiankunWindow.__INJECTED_PUBLIC_PATH__ = props.baseUrl;
    isQiankunMounted = true;
    pendingAppMount = () => {
      // 这一步很重要，因为我们项目中的路由是需要请求接口动态加载的，如果直接执行挂载函数，会导致路由加载失败，所以我们需要在接口请求成功后再执行挂载函数。
      app.mount(props.container ? props.container : "#app");
      ensureStylesInHead();
    };
    if (!app && isAppMounted) {
      initApp();
    }
  },
  bootstrap: async () => {
    console.log("子应用 bootstrap");
  },
  unmount: async () => {
    console.log("子应用 unmount");
    app.unmount();
    app._container.innerHTML = "";
    app = null;
  },
  update: async () => {
    console.log("子应用 update");
  },
});

initApp();
