<template>
  <ul class="nav-tags">
    <pl-dropdown
      trigger="manual"
      @command="handleClickTag($event, index, item)"
      v-for="(item, index) in navtags"
      :key="index"
      ref="dropdownRef"
      :id="item.meta.title"
      @visible-change="handleChange($event, item.meta.title, index)"
      :dropdown="dropdown"
      size="small"
    >
      <li
        class="tag-item"
        :class="isActive() == index ? 'active' : ''"
        @click="goToPage(item.path)"
        @contextmenu.prevent="showContextMenu(index)"
      >
        <template v-if="isActive() == index">
          <div class="pl-font pl-icon-shuaxin" @click.stop="refreshPage"></div>
        </template>
        {{ item.meta.title }}

        <template v-if="isActive() == index && index > 0">
          <pl-icon
            name="Close"
            color="#999999FF"
            :size="16"
            class="close"
            @click.stop="closePage(item, true)"
          ></pl-icon>
        </template>
      </li>
    </pl-dropdown>
  </ul>
</template>

<script setup>
import { ref, watch } from "vue";
import { useNavtagsStore } from "@/stores";
import { useRouter, useRoute } from "vue-router";
import { plIcon, plDropdown } from "pls-common";
// 右键菜单
const dropdown = ref([
  {
    name: "关闭右侧标签页",
    event: "right",
    show: false,
  },
  {
    name: "关闭左侧标签页",
    event: "left",
    show: false,
  },
  {
    name: "关闭其他",
    event: "other",
    show: false,
  },
  {
    name: "全部关闭",
    event: "all",
    show: false,
  },
  {
    name: "刷新",
    event: "refresh",
    show: true,
  },
]);
const dropdownRef = ref(null);
const showContextMenu = (index) => {
  // // 按钮显示判断
  if (index > 0 && index == navtags.value.length - 1) {
    dropdownIsShow(["关闭右侧标签页"]);
  } else if (index == 0 && navtags.value.length == 1) {
    dropdownIsShow([
      "关闭左侧标签页",
      "关闭右侧标签页",
      "关闭其他",
      "全部关闭",
    ]);
  } else if (index == 0 || index == 1) {
    console.log(111111);
    dropdownIsShow(["关闭左侧标签页"]);
  } else {
    dropdownIsShow([]);
  }
  dropdownRef.value[index].handleOpen();
};

const handleClickTag = (e, index) => {
  if (e.event == "all") {
    // 关不全部
    navtags.value.forEach((item) => {
      if (item.path == "/dashboard") return;
      closePage(item);
    });
    closePageCallback();
  } else if (e.event == "other") {
    // 关闭其他
    navtags.value.forEach((item) => {
      if (item.path == "/dashboard" || item.path == navtags.value[index].path)
        return;
      closePage(item);
    });
    closePageCallback();
  } else if (e.event == "left") {
    // 关闭左侧
    navtags.value.forEach((item, i) => {
      if (i < index && item.path != "/dashboard") {
        closePage(item);
      }
    });
    closePageCallback();
  } else if (e.event == "right") {
    // 关闭右侧
    navtags.value.forEach((item, i) => {
      if (i > index && item.path != "/dashboard") {
        closePage(item);
      }
    });
    closePageCallback();
  } else if (e.event == "refresh") {
    refreshPage();
  }
};
const handleChange = (visible, name) => {
  if (visible) {
    dropdownRef.value.forEach((item) => {
      if (item.$attrs.id == name) return;
      item.handleClose();
    });
  }
};

// 控制按钮隐藏
const dropdownIsShow = (arr) => {
  dropdown.value.map((item) => {
    if (arr.includes(item.name)) {
      item.show = false;
    } else {
      item.show = true;
    }
  });
};

// 路由
const router = useRouter();
const route = useRoute();

// emit
let emit = defineEmits(["tagDeleteChange", "tagRefreshPage"]);

const navtagsStore = useNavtagsStore();
let navtags = ref(navtagsStore.navtags);

// 监听导航标签的变化
watch(
  () => navtagsStore.navtags,
  (newValue) => {
    navtags.value = newValue;
  },
  {
    deep: true,
  }
);

/**
 * 判断导航项是否处于激活状态。
 *
 * 该函数通过比较当前路由路径与导航项的路径，来确定导航项是否激活。
 * 如果路由路径与某个导航项的路径匹配，则返回该导航项在数组中的索引。
 * 如果没有匹配的路径，则返回0，表示没有导航项处于激活状态。
 *
 * @param {Object} item 导航项对象，包含导航项的路径等信息。
 * @returns {number} 如果导航项激活，返回其在数组中的索引；否则返回0。
 */
const isActive = () => {
  for (let i = 0; i < navtags.value.length; i++) {
    if (navtags.value[i].path == route.path) {
      return i;
    }
  }
  return 0;
};

const closePage = (item, cb) => {
  navtagsStore.removeTag(item);
  if (cb) {
    closePageCallback();
  }
};
// 判断关闭后路由跳转
const closePageCallback = () => {
  if (navtagsStore.navtags.length == 1) {
    goToPage("/");
  } else {
    goToPage(navtagsStore.navtags[1].path);
  }
};

/**
 * 导航到指定的页面路径。
 *
 * 该函数用于程序中的页面导航，通过传入一个路径，它将利用Vue Router的能力将当前的浏览上下文导航到新的路径。
 * 这是非常有用的功能，特别是在单页面应用程序中，需要根据用户的操作或逻辑条件来动态切换页面内容时。
 *
 * @param {string} path - 需要导航到的页面路径。这个路径应该是Vue Router配置中定义的有效路径。
 */
const goToPage = (path) => {
  router.push(path);
};

const refreshPage = () => {
  emit("tagRefreshPage");
};
</script>

<style scoped lang="scss">
// 右键菜单
.right-menu {
  position: absolute;
  bottom: -10px;
  background: #fff;
  padding: 10px;
  box-shadow: 0 3px 10px #ccc;
  left: 0;
  transform: translateY(100%);
  border-radius: 5px;

  .menu-item {
    font-size: 12px;
    padding: 5px;

    &:hover {
      cursor: pointer;
      background-color: var(--el-color-primary);
      color: var(--el-color-white);
      border-radius: 5px;
    }
  }
}

.nav-tags {
  display: flex;
  position: relative;
  z-index: 2;
  height: 60px;
  background-color: var(--el-color-white);
  padding: 0 24px;
  align-items: center;
  .tag-item {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    font-size: 12px;
    background-color: var(--el-color-white);
    border-radius: 5px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #efefefff;
    .pl-icon-shuaxin {
      font-size: 16px;
      margin-right: 8px;
      &:hover {
        opacity: 0.6;
      }
    }
    &:hover {
      cursor: pointer;
      color: var(--el-color-primary);
    }

    .refresh-icon {
      margin-right: 5px;

      &:hover {
        background-color: #86b7e9;
        border-radius: 50%;
      }
    }

    .close {
      position: relative;
      right: -5px;
      cursor: pointer;
      padding: 1.5px;

      &:hover {
        opacity: 0.6;
      }
    }

    &.active {
      background: var(--el-color-primary-light-8);
      border-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary);
    }
  }
}
</style>
