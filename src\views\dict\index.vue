<template>
  <div class="h100">
    <pl-row class="h100">
      <pl-col :span="24" class="h100 pl20">
        <pl-card v-loading="leftTableLoading">
          <div class="h100 flex over-hide">
            <!-- 字典容器 -->
            <div class="code-contetn">
              <div class="content">
                <pl-form
                  :fields="ltFields"
                  inline
                  confirmButtonText="搜索"
                  cancelButtonText="重置"
                  @confirm="handleDictTypeSearch"
                  clear
                  @cancel="handleDictTypeReset"
                ></pl-form>

                <!-- 按钮 -->
                <div class="btn-box">
                  <pl-button plain type="primary" @click="handleAdd"
                    >新增</pl-button
                  >
                  <!-- <pl-button type="primary">导出</pl-button> -->
                </div>

                <!-- 表格 -->
                <div class="table-box">
                  <pl-table
                    :columns="ltColumns"
                    :data="ltBigData.records"
                    class="table"
                    @row-click="handleRowClick"
                  >
                    <template #operations="{ scope }">
                      <div class="flex">
                        <pl-button link @click.stop="handleEdit(scope)"
                          >编辑</pl-button
                        >
                        <pl-button
                          type="danger"
                          link
                          @click.stop="handleDelete(scope)"
                          >删除</pl-button
                        >
                      </div>
                    </template>
                  </pl-table>
                </div>

                <!-- 分页 -->
                <pl-pagination
                  :total="ltBigData.total"
                  @current-change="ltCurrentChange"
                  @size-change="ltSizeChange"
                ></pl-pagination>
              </div>
              <div class="content">
                <pl-form
                  :fields="rtFields"
                  inline
                  confirmButtonText="搜索"
                  cancelButtonText="重置"
                  @confirm="handleDictSearch"
                  @cancel="handleDictReset"
                  clear
                ></pl-form>

                <!-- 按钮 -->
                <div class="btn-box">
                  <pl-button plain @click="handleAddChild">新增</pl-button>
                </div>

                <!-- 表格 -->
                <div class="table-box">
                  <pl-table
                    :columns="rtColumns"
                    :data="lrData.records"
                    class="table"
                  >
                    <template #status="{ scope }">
                      <div class="success-color" v-if="scope.row.status === 1">
                        启用
                      </div>
                      <div
                        class="erroe-color"
                        v-else-if="scope.row.status === 0"
                      >
                        启用
                      </div>
                    </template>
                    <template #operations="{ scope }">
                      <div class="flex">
                        <pl-button link @click="handleEditChild(scope)"
                          >编辑</pl-button
                        >
                        <pl-button
                          type="danger"
                          link
                          @click="handleDeleteChild(scope)"
                          >删除</pl-button
                        >
                      </div>
                    </template>
                  </pl-table>
                </div>

                <!-- 分页 -->
                <pl-pagination
                  :total="lrData.total"
                  @current-change="lrCurrentChange"
                  @size-change="lrSizeChange"
                ></pl-pagination>
              </div>
            </div>
          </div>
          <!-- 新增编码 -->
          <add-bm-form
            v-model="bmFormPop"
            :title="title"
            :type="type"
            :form="bmFromData"
            :tenantId="tenantId"
            @change="init"
          ></add-bm-form>

          <!-- 新增编码值 -->
          <add-child
            v-model="childFormPop"
            :configId="configId"
            :title="title"
            :type="type"
            :form="childFromData"
            :tenantId="tenantId"
            @change="getDictDetails"
          ></add-child>
        </pl-card>
      </pl-col>
    </pl-row>
  </div>
</template>

<script setup>
import { ref } from "vue";
import addBmForm from "./module/sys-dict-add.vue";
import addChild from "./module/sys-dict-add-child.vue";
import { plMessageBox, plMessage } from "pls-common";
import http from "@/api/request";
// 接口
import { postSysConfig } from "@/api/dict";
let tenantId = ref("");

// 新增编码
let bmFormPop = ref(false);
let title = ref("");
let type = ref("");

// 新增编码值
let childFormPop = ref(false);

// 左侧搜索表单
const ltFields = ref([
  {
    label: "类型名称",
    prop: "configName",
    type: "text",
    placeholder: "请输入类型名称",
  },
  {
    label: "编码类型",
    prop: "configCode",
    type: "input",
    placeholder: "请输入编码类型",
  },
]);

//  左侧表格
let leftTableLoading = ref(true);
const ltColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 80,
  },
  {
    label: "类型名称",
    prop: "configName",
  },
  {
    label: "编码类型",
    prop: "configCode",
    width: 200,
  },
  {
    label: "创建时间",
    prop: "createTime",
    width: 200,
  },
  {
    label: "操作",
    template: "operations",
    fixed: "right",
    width: "100",
  },
]);

// 表格数据
const ltBigData = ref({
  total: 0,
  current: 1,
  records: [],
  pages: 0,
});

let current = ref(1);
let limit = ref(10);
function init(obj) {
  postSysConfig({
    current: current.value,
    limit: limit.value,
    configCode: obj && obj.configCode ? obj.configCode : "",
    configName: obj && obj.configName ? obj.configName : "",
    tenantId: tenantId.value,
  })
    .then((res) => {
      ltBigData.value = res.data;
      leftTableLoading.value = false;
    })
    .catch(() => {
      leftTableLoading.value = false;
    });
}

/**
 * 字典类型条件查询
 */
const handleDictTypeSearch = (res) => {
  init(res);
};

/**
 * 重置字典类型条件查询
 */
const handleDictTypeReset = () => {
  init();
};

let lrData = ref({
  current: 1,
  records: [],
  total: 0,
});

/**
 * 左侧某一行点击事件
 */
let configId = ref("");
const handleRowClick = (e) => {
  configId.value = e.configId;
  rightCurrent.value = 1;
  getDictDetails();
};

/**
 * 查询字典详情
 */
let lrLimit = ref(10);
function getDictDetails(obj) {
  http
    .post("/tenant/configItem/pageConfigItem", {
      configId: configId.value,
      limit: lrLimit.value,
      tenantId: tenantId.value,
      current: rightCurrent.value,
      ...obj,
    })
    .then((res) => {
      if (res.code == 200) {
        lrData.value.current = res.data.current;
        lrData.value.records = res.data.records;
        lrData.value.total = res.data.total;
      }
    });
}

/**
 * 根据条件查询字典
 */
const handleDictSearch = (res) => {
  getDictDetails(res);
};
/**
 * 重置字典条件查询
 */
const handleDictReset = () => {
  getDictDetails();
};

let rightCurrent = ref(1);

/**
 * 右侧字典分页
 */
const lrCurrentChange = (e) => {
  rightCurrent.value = e;
  getDictDetails();
};

/**
 * 右侧字典数量变化
 */
const lrSizeChange = (e) => {
  lrLimit.value = e;
  getDictDetails();
};

// 右侧表格
const rtFields = ref([
  {
    label: "编码名称",
    prop: "itemCode",
    type: "text",
    placeholder: "请输入编码名称",
  },
  {
    label: "编码值",
    prop: "itemValue",
    placeholder: "请输入编码值",
    type: "text",
  },
]);

// 右侧table

let rtColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 80,
  },
  {
    label: "配置项",
    prop: "itemCode",
    width: 200,
  },
  {
    label: "参数值",
    prop: "itemValue",
    width: 200,
  },
  {
    label: "状态",
    prop: "status",
    width: 200,
    template: "status",
  },
  {
    label: "描述",
    prop: "remark",
    width: 200,
  },
  {
    label: "操作",
    template: "operations",
    fixed: "right",
    width: "100",
  },
]);

/**
 * 新增
 */
let bmFromData = {};
const handleAdd = () => {
  bmFormPop.value = true;
  title.value = "新增编码";
  type.value = "add";
  bmFromData = {};
};

/**
 * 编辑
 */
const handleEdit = (e) => {
  bmFromData = JSON.parse(JSON.stringify(e.row));
  bmFormPop.value = true;
  title.value = "编辑编码";
  type.value = "edit";
};

/**
 * 分页页码变化
 */
const ltCurrentChange = (e) => {
  current.value = e;
  init();
};

/**
 * 分页条数变化
 */
const ltSizeChange = (e) => {
  limit.value = e;
  init();
};

/**
 * 删除
 */
const handleDelete = ({ row }) => {
  plMessageBox
    .confirm("确定要删除该编码数据吗?", {
      title: "删除编码",
    })
    .then(() => {
      http.get(`/config/deleteConfig/${row.configId}`).then((res) => {
        if (res.code == 200) {
          plMessage(res.message, "success");
          init();
        } else {
          plMessage(res.message, "error");
        }
      });
    })
    .catch((e) => {
      console.log(e);
    });
};

/**
 * 新增编码值
 */
let childFromData = {};
const handleAddChild = () => {
  title.value = "新增编码值";
  childFormPop.value = true;
  type.value = "add";
  childFromData = {
    dictSort: 10,
  };
};

/**
 * 编辑编码值
 */
const handleEditChild = (e) => {
  title.value = "编辑编码值";
  childFormPop.value = true;
  type.value = "edit";
  childFromData = JSON.parse(JSON.stringify(e.row));
};

/**
 * 删除编码值
 */

const handleDeleteChild = ({ row }) => {
  plMessageBox
    .confirm("确定要删除该编码数据吗?", {
      title: "删除编码",
    })
    .then(() => {
      http
        .get(`/configItem/deleteConfigItem/${row.configId}/${row.itemCode}`)
        .then((res) => {
          console.log(res);
          if (res.code == 200) {
            plMessage(res.message, "success");
            getDictDetails();
          } else {
            plMessage(res.message, "error");
          }
        });
    })
    .catch((e) => {
      console.log(e);
    });
};

init();
</script>

<style scoped lang="scss">
.code-contetn {
  flex: 1;
  display: flex;
}

.content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-right: 20px;

  &:last-child {
    margin-right: 0;
    position: relative;
    transition: all 0.3s;
    max-width: 50%;

    &.hide {
      overflow: hidden;
      max-width: 0%;
    }

    &::after {
      content: "";
      position: absolute;
      left: -10px;
      top: 0;
      height: 100%;
      width: 1px;
      background-color: var(--el-border-color);
    }
  }

  .table-box {
    flex: 1;
    position: relative;
    margin-top: 20px;

    .table {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
