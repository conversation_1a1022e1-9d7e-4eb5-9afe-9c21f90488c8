<template>
  <div class="pl-tree-select-pro">
    <el-popover trigger="click" :width="treeWidth" :disabled="disabled">
      <template #reference>
        <div class="cascader-wrapper el-select">
          <div
            class="el-select__wrapper el-tooltip__trigger el-tooltip__trigger"
            ref="selectRef"
            @click="handleClick"
            :class="disabled ? 'is-disabled' : ''"
          >
            <plIcon class="arrow-down" name="ArrowDown" :size="14" />
            <div class="placeholder" v-if="selectedNodes.length === 0">
              {{ placeholder }}
            </div>

            <!-- 单选时显示文本形式 -->
            <template v-if="!multiple && selectedNodes.length > 0">
              <div class="single-select-text el-select__selected-item">
                {{ selectedNodes[0][props.cascaderProps.label] }}
              </div>
            </template>
            <!-- 多选时显示tag形式 -->
            <template v-if="multiple">
              <div class="el-select__selection is-near position">
                <div
                  class="el-select__selected-item el-select__selected-item"
                  ref="selectedItemRef"
                  v-for="item in selectedNodes"
                  :key="item[props.cascaderProps.value]"
                >
                  <span
                    class="el-tag is-closable el-tag--info el-tag--default el-tag--light"
                  >
                    <span class="el-tag__content">
                      <span class="el-select__tags-text">{{
                        item[props.cascaderProps.label]
                      }}</span>
                    </span>
                    <el-icon>
                      <Close @click.stop="handleClose(item)" />
                    </el-icon>
                  </span>
                </div>
              </div>
              <div class="el-select__selection is-near">
                <!-- 数据列表 start -->
                <!-- 数据列表 start -->
                <template
                  v-for="(item, index) in selectedNodes"
                  :key="item[props.cascaderProps.value]"
                >
                  <div
                    class="el-select__selected-item"
                    v-show="isItemShow(index)"
                  >
                    <span
                      class="el-tag is-closable el-tag--info el-tag--default el-tag--light"
                    >
                      <span class="el-tag__content">
                        <span class="el-select__tags-text">{{
                          item[props.cascaderProps.label]
                        }}</span>
                      </span>
                      <el-icon v-if="!disabled">
                        <Close @click.stop="handleClose(item)" />
                      </el-icon>
                    </span>
                  </div>
                </template>

                <!-- 超出时显示数量 start -->
                <div class="el-select__selected-item" v-if="isOverflow">
                  <el-popover trigger="hover" :popper-style="{ width: 'auto' }">
                    <template #reference>
                      <span
                        class="el-tag is-closable el-tag--info el-tag--default el-tag--light"
                      >
                        <span class="el-tag__content">
                          <span class="el-select__tags-text"
                            >+ {{ overflowCount }}</span
                          >
                        </span>
                      </span>
                    </template>
                    <div class="el-select__selected-item-big">
                      <template
                        v-for="(item, index) in selectedNodes"
                        :key="item[props.cascaderProps.value]"
                      >
                        <!-- 只显示溢出的项目 -->
                        <div
                          class="el-select__selected-item"
                          v-if="index >= overflowStartIndex"
                        >
                          <span
                            class="el-tag is-closable el-tag--info el-tag--default el-tag--light"
                          >
                            <span class="el-tag__content">
                              <span class="el-select__tags-text">{{
                                item[props.cascaderProps.label]
                              }}</span>
                            </span>
                            <el-icon v-if="!disabled">
                              <Close @click.stop="handleClose(item)" />
                            </el-icon>
                          </span>
                        </div>
                      </template>
                    </div>
                  </el-popover>
                </div>
                <!-- 超出时显示数量 end -->

                <!-- 数据列表 end -->
              </div>
            </template>
          </div>
        </div>
      </template>
      <div class="cascader-tree-wrapper">
        <el-input
          class="cascader-tree-search"
          v-model="searchValue"
          placeholder="请输入关键字筛选"
        />
        <el-scrollbar :max-height="`${height}px`">
          <el-tree
            v-if="!multiple"
            ref="treeRef"
            :data="options"
            :node-key="nodeKey"
            filter
            :filter-node-method="filterNode"
            :props="cascaderProps"
            v-model="selectedNodes"
            :check-on-click-node="checkOnClickNode"
            :render-content="renderContent"
          />

          <el-tree
            v-if="multiple"
            show-checkbox
            ref="treeRef"
            :data="options"
            :node-key="nodeKey"
            filter
            :filter-node-method="filterNode"
            :props="cascaderProps"
            v-model="selectedNodes"
            :check-on-click-node="checkOnClickNode"
            :expand-on-click-node="false"
            @check="handleMultipleCheckChange"
          />
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import {
  ElTree,
  ElInput,
  ElPopover,
  ElIcon,
  ElScrollbar,
  ElCheckbox,
} from "element-plus";
import { plIcon } from "pls-common";
import { getAreaList } from "@/api/index";
import { ref, watch, onMounted, nextTick, onBeforeUnmount } from "vue";
import { findNodeAndParentsById } from "@/utils/index";
import { Close } from "@element-plus/icons-vue";
const searchValue = ref("");
const treeRef = ref(null);
const selectedNodes = ref([]);
const selectRef = ref(null);
const selectedItemRef = ref([]);
const isOverflow = ref(false); // 是否超出
const overflowCount = ref(0); // 超出数量
const overflowStartIndex = ref(0); // 从第几个开始超出
const props = defineProps({
  width: {
    type: Number,
    default: 400,
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => "",
  },
  cascaderProps: {
    type: Object,
    default: () => ({ label: "name", value: "id", children: "children" }),
  },
  nodeKey: {
    type: String,
    default: "id",
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "请选择所属区域",
  },
  // 是否需要选中父级
  selectParent: {
    type: Boolean,
    default: false,
  },
  // 是否严格遵循父子不互相关联
  checkStrictly: {
    type: Boolean,
    default: false,
  },
  // 是否在点击节点的时候选中
  checkOnClickNode: {
    type: Boolean,
    default: false,
  },
  // 是否显示复选框
  showCheckbox: {
    type: Boolean,
    default: true,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  valueType: {
    type: String,
    default: "array",
  },
  height: {
    type: Number,
    default: 300,
  },
  // 是否不限制只限最后一级
  checkLastLevel: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const handleMultipleCheckChange = (data, { checkedNodes }) => {
  let level3 = checkedNodes.filter((item) => item.level == 3);
  selectedNodes.value = level3;
  let obj = {
    checkedNodes: checkedNodes,
    checkedKeys: level3.map((item) => item.id),
  };
  emit("change", obj);
};

const renderContent = (h, { node, store }) => {
  return h(
    "span",
    {
      class: "custom-tree-node",
    },
    [
      node.isLeaf || props.checkLastLevel
        ? h("span", { class: "checkbox-container" }, [
            h(ElCheckbox, {
              modelValue: node.checked,
              onChange: (checked) => {
                if (props.multiple) {
                  store.setChecked(node, checked);
                  selectedNodes.value.push(node.data);
                  updateParentCheckStatus(node, checked);
                } else {
                  store.setChecked(node, checked);
                  // 单选模式
                  if (checked) {
                    // 清除之前选中的节点
                    if (selectedId) {
                      const prevNode = treeRef.value.getNode(selectedId);
                      if (prevNode) {
                        treeRef.value.setChecked(prevNode, false);
                        updateParentCheckStatus(prevNode, false);
                      }
                    }
                    // 设置新的选中节点
                    selectedNodes.value = [node.data];
                    selectedId = node.data[props.cascaderProps.value];
                    treeRef.value.setChecked(node, true);
                    updateParentCheckStatus(node, true);
                  } else {
                    // 取消选中当前节点
                    selectedNodes.value = [];
                    selectedId = "";
                    treeRef.value.setChecked(node, false);
                    updateParentCheckStatus(node, false);
                  }
                }
                updataModelValue();
              },
            }),
          ])
        : null,
      h("span", null, node.label),
    ]
  );
};

const updateParentCheckStatus = (node) => {
  let parentNode = node.parent;
  while (parentNode) {
    const children = parentNode.childNodes;
    const checkedChildren = children.filter((child) => child.checked);
    const indeterminateChildren = children.filter(
      (child) => child.indeterminate
    );

    if (checkedChildren.length === children.length) {
      treeRef.value.setChecked(parentNode, true);
      parentNode.indeterminate = false;
    } else if (checkedChildren.length > 0 || indeterminateChildren.length > 0) {
      treeRef.value.setChecked(parentNode, false);
      parentNode.indeterminate = true;
    } else {
      treeRef.value.setChecked(parentNode, false);
      parentNode.indeterminate = false;
    }

    parentNode = parentNode.parent;
  }
};
const options = ref([]);
onMounted(() => {
  getAreaList().then((res) => {
    options.value = res;
  });
  if (props.modelValue) {
    echoAssignment(props.modelValue);
  }
});

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      if (val.length == 0) {
        selectedNodes.value = [];
        treeRef.value.setCheckedNodes(selectedNodes.value);
      } else {
        if (!props.multiple) {
          echoAssignment(val);
          // 单选时处理回显
        }
      }
    } else {
      selectedNodes.value = [];
      treeRef.value.setCheckedNodes(selectedNodes.value);
    }
  }
);

watch(
  () => props.options,
  () => {
    if (props.modelValue) {
      echoAssignment(props.modelValue);
    }
  }
);

const treeWidth = ref(props.width);
const handleClick = () => {
  if (props.disabled) return;
  treeWidth.value = selectRef.value.offsetWidth;
};

const newCheckStrictly = ref(props.checkStrictly);
const newSelectParent = ref(props.selectParent);

if (!props.multiple && !props.checkStrictly) {
  newCheckStrictly.value = true;
  newSelectParent.value = true;
}

const emit = defineEmits([
  "update:modelValue",
  "update:formData",
  "change",
  "computedWidth",
]);
const offsetWidth = ref(0);

// 1. 添加一个 MutationObserver 来监听元素尺寸变化
const initWidthObserver = () => {
  if (!selectRef.value) return;

  const observer = new MutationObserver(() => {
    const newWidth = selectRef.value?.offsetWidth;
    if (newWidth && newWidth !== offsetWidth.value) {
      offsetWidth.value = newWidth;
      computedWidth();
    }
  });

  observer.observe(selectRef.value, {
    attributes: true,
    childList: true,
    subtree: true,
  });

  // 在组件卸载时清理 observer
  onBeforeUnmount(() => {
    observer.disconnect();
  });
};

// 2. 修改 onMounted 逻辑
onMounted(() => {
  // 初始化 observer
  initWidthObserver();

  // 使用 requestAnimationFrame 确保 DOM 完全渲染
  requestAnimationFrame(() => {
    if (selectRef.value?.offsetWidth) {
      offsetWidth.value = selectRef.value.offsetWidth;
      computedWidth();
    }
  });
});

// 3. 添加 resize 监听以处理窗口大小变化
onMounted(() => {
  window.addEventListener("resize", () => {
    if (selectRef.value?.offsetWidth) {
      offsetWidth.value = selectRef.value.offsetWidth;
      computedWidth();
    }
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", computedWidth);
  });
});

const echoAssignment = (val) => {
  if (!val) return;
  console.log(selectedId);
  nextTick(() => {
    if (!props.multiple) {
      let province = options.value.find((item) => item.id == val[0]); // 省
      let city = province.children.find((item) => item.id == val[1]); // 市
      let district = city.children.find((item) => item.id == val[2]); // 区
      selectedNodes.value = [district];
      selectedId = district.id;
      let prevNode = treeRef.value.getNode(selectedId);
      if (prevNode) {
        treeRef.value.setChecked(prevNode, true);
      }
    }
  });
};
watch(
  () => selectedItemRef.value,
  () => {
    computedWidth();
  },
  {
    deep: true,
  }
);

const isItemShow = (index) => {
  if (overflowStartIndex.value > 0) {
    return index < overflowStartIndex.value;
  } else {
    return true;
  }
};

// 4. 修改 computedWidth 函数，添加宽度检查
const computedWidth = () => {
  if (!selectRef.value?.offsetWidth) return;

  nextTick(() => {
    let accumulatedWidth = 0; // 累加的宽度
    const threshold = offsetWidth.value - 80; // 预留80px给"+n"显示
    let overflowIndex = -1; // 记录溢出开始的索引，-1表示无溢出

    // 重置溢出相关状态
    overflowCount.value = 0;
    isOverflow.value = false;
    overflowStartIndex.value = 0;

    if (selectedItemRef.value.length > 0) {
      // 遍历每个标签，计算累加宽度
      for (let i = 0; i < selectedItemRef.value.length; i++) {
        const itemWidth = selectedItemRef.value[i].offsetWidth + 6; // 标签宽度 + 间距
        if (accumulatedWidth + itemWidth > threshold) {
          overflowIndex = i;
          console.log(overflowIndex, "超出了");
          break; // 找到溢出点后停止循环
        }
        accumulatedWidth += itemWidth;
      }

      // 设置溢出状态
      if (overflowIndex !== -1) {
        isOverflow.value = true;
        overflowStartIndex.value = overflowIndex;
        overflowCount.value = selectedItemRef.value.length - overflowIndex;
      }
    }
    emit("computedWidth", {
      offsetWidth: offsetWidth.value,
      selectedItemRef: selectedItemRef.value,
      accumulatedWidth: accumulatedWidth,
      isOverflow: isOverflow.value,
      overflowCount: overflowCount.value,
      overflowStartIndex: overflowStartIndex.value,
    });
  });
};

onMounted(() => {
  computedWidth();
});

let selectedId = "";
const updataModelValue = () => {
  let sendValArray = [];
  selectedNodes.value.forEach((item) => {
    const ids = findNodeAndParentsById(
      options.value,
      item[props.cascaderProps.value]
    );
    sendValArray.push({
      pathString: ids.join(","),
      pathArray: ids,
      code: item.code,
      level: item.level,
      name: item.name,
      id: item.id,
      parentId: item.parentId,
    });
  });
  if (sendValArray.length > 0) {
    let formData = props.formData;
    formData.province = sendValArray[0].pathArray[0];
    formData.city = sendValArray[0].pathArray[1];
    formData.district = sendValArray[0].pathArray[2];
    formData.areaIds = sendValArray[0].pathArray;
    emit("update:formData", formData);
  }
  emit("change", sendValArray);
};

watch(
  () => searchValue.value,
  (val) => {
    treeRef.value.filter(val);
  }
);
const filterNode = (value, data) => {
  if (!value) return true;
  return data[props.cascaderProps.label].includes(value);
};

const handleClose = (item) => {
  const prevNode = treeRef.value.getNode(item.id);
  if (prevNode) {
    treeRef.value.setChecked(prevNode, false);
    updateParentCheckStatus(prevNode, false);
  }

  selectedNodes.value = selectedNodes.value.filter((node) => node !== item);

  let obj = {
    checkedNodes: selectedNodes.value,
    checkedKeys: selectedNodes.value.map((item) => item.id),
  };
  emit("change", obj);
};
</script>

<style lang="scss" scoped>
:deep(.custom-tree-node) {
  display: flex;
  align-items: center;
  .checkbox-container {
    margin-right: 5px;
  }
}
.el-select__selected-item-big {
  display: flex;
}
.el-tooltip__trigger {
  position: relative;
  .arrow-down {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%) translateX(-50%);
    color: var(--el-select-input-color);
  }
}
.single-select-text {
  color: var(--el-text-color-regular);
  font-size: var(--el-font-size-base);
}
.el-select__selection {
  white-space: nowrap;
  display: block;
  overflow: hidden;
  &.position {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    opacity: 0;
    transform: translateX(-1000000px);
  }
}
.el-select__selected-item {
  display: inline-flex;
  margin-right: 6px;
  flex-shrink: 0;
}
.pl-tree-select-pro {
  width: 100%;
}
.placeholder {
  color: #b5b5b5;
  font-size: 14px;
}
.cascader-tree-wrapper {
  width: 100%;
}
.cascader-tree-search {
  margin-bottom: 10px;
}
.cascader-wrapper {
  width: 100%;
}
</style>
