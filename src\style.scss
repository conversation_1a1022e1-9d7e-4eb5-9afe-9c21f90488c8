* {
  box-sizing: border-box;
}
a {
  text-decoration: none;
}
.flex {
  display: flex;
}

/* 饿了么样式重写 */

.el-header {
  background-color: var(--el-color-white);
  --el-header-padding: 0 !important;
  transition: all 0.5s linear;
  --el-header-height: 88px !important;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.08);
  &.lock {
    --el-header-height: 0px !important;
  }
}
.el-menu {
  border-right: none !important;
}
:root {
  --el-aside-width: 180px;
  --el-color-primary: #00ad77 !important;
  --el-padding-15: 15px;
  --w-color-666: #666 !important;
  --el-color-primary-light-3: #00ad767a !important;
  --el-color-primary-dark-2: #00ad76ce !important;
  --el-color-primary-light-8: #00ad771a !important;
  --el-color-primary-light-9: #00ad771a !important;
  --el-color-primary-light-5: #00ad767a !important;
}
.el-sub-menu.is-active .el-sub-menu__title {
  color: var(--el-color-white);
}
.el-menu-item {
  color: var(--el-color-white) !important;
}
.el-menu-item:hover,
.el-menu-item.is-active,
.el-sub-menu__title:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
.el-form--inline .el-form-item {
  margin-right: 0 !important;
}
.el-input-group__append,
.el-input-group__prepend {
  background: none !important;
}
.table-address {
  // 只显示一行，超出省略
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* 自定义样式 */
html,
body,
#app {
  height: 100%;
  overflow: hidden;
}
body {
  background: #f9f9f9;
}
.warp {
  background: #fff;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
}
.h100 {
  height: 100%;
}
.mt20 {
  margin-top: 20px;
}
.mt50 {
  margin-top: 50px;
}
.pl20 {
  padding-left: 20px;
}
.card-flex {
  display: flex;
  flex-direction: column;
  height: 100%;
  .card-table {
    flex: 1;
    position: relative;
    .table {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
.flex {
  display: flex;
}
.over-hide {
  overflow: hidden;
}
.warp {
  position: relative;
}
.layout-view {
  overflow: hidden;
}

.menu-fun {
  padding-bottom: 68px;
  .menu-fun-title {
    font-weight: bold;
    margin-bottom: 20px;
    position: relative;
    padding-left: 10px;
    &::before {
      position: absolute;
      content: "";
      width: 5px;
      height: 17px;
      background-color: var(--el-color-primary);
      left: 0;
      top: 3px;
    }
  }
  .menu-items {
    .item-title {
      color: var(--el-text-color-regular);
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .item-child {
        margin-right: 10px;
      }

      .label {
        font-size: 14px;
        margin-right: 10px;
      }

      .input {
        width: 200px;
      }
    }
  }
}

.success-color {
  color: var(--el-color-success);
}
.error-color {
  color: var(--el-color-danger);
}

/* google、safari */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

.get-code {
  &.el-button--primary.is-plain {
    background: #fff;
    &:hover {
      background-color: var(--el-color-primary) !important;
    }
  }
}

.el-menu {
  background-color: var(--el-color-primary) !important;
  .el-sub-menu__title {
    color: var(--el-color-white);
  }
}

.el-main.main {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
  overflow: unset;
}
.big-card {
  padding: 20px !important;
  border-radius: 8px;
}
.el-menu--collapse .el-sub-menu.is-active .el-sub-menu__title {
  color: #fff !important;
}
.el-sub-menu.is-active .el-sub-menu__title {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
.el-tabs--border-card > .el-tabs__header {
  background-color: var(--el-color-primary-light-8) !important;
}
.el-date-editor {
  width: 100% !important;
}
.drawer-w {
  .title {
    margin-bottom: 20px;
    font-weight: bold;
    position: relative;
    padding-left: 10px;
    font-size: 16px;
    &::after {
      position: absolute;
      content: "";
      width: 5px;
      height: 17px;
      background-color: var(--el-color-primary);
      left: 0;
      top: 3px;
    }
  }
}

.button-box {
  display: flex;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  justify-content: center;
  z-index: 2;
  height: 48px;
  background-color: #fff;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
  align-items: center;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;

  .btn {
    width: 200px;
  }
}

.resource-permissions {
  padding: 24px 24px 24px;
}

#upRoute,
#downRoute,
.el-scrollbar__view {
  height: 100%;
}

::-webkit-scrollbar {
  width: 4px; /* 纵向滚动条的宽度 */
  height: 12px; /* 横向滚动条的高度 */
}

/* 设置横向滚动条 */
::-webkit-scrollbar:horizontal {
  height: 4px; /* 横向滚动条的高度 */
}

/* 设置横向滚动条的轨道 */
::-webkit-scrollbar-track:horizontal {
  background: #f1f1f1; /* 轨道背景颜色 */
}

/* 设置横向滚动条的滑块 */
::-webkit-scrollbar-thumb:horizontal {
  background: #888; /* 滑块颜色 */
}

/* 设置滑块在悬停时的样式 */
::-webkit-scrollbar-thumb:horizontal:hover {
  background: #555; /* 悬停时滑块颜色 */
}

/* 滚动条轨道部分 */
::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

/* 滚动条滑块部分 */
::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}

/* 鼠标悬停在滑块上时 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.el-table__body-wrapper .current-row .el-table-fixed-column--right {
  background-color: rgb(229, 247, 241) !important;
}
.pd-24 {
  padding-bottom: 24px;
}
.el-button:focus-visible {
  display: none;
}

textarea {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

// 标签
.label-tag {
  display: inline-block;
  color: #fff;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  padding: 0 8px;
  border-radius: 4px;
  margin-right: 8px;
  background-color: #ff9800;
  &.QCZ {
    background-color: var(--el-color-primary);
  }
  &.QY {
    background-color: var(--el-color-warning);
  }
  &.XX {
    background-color: var(--el-color-success);
  }
  &.ZD {
    background-color: var(--el-color-primary);
  }
  &.QX {
    background-color: var(--el-color-primary);
  }
  &.CCD {
    background-color: var(--el-color-danger);
  }
}

img {
  -webkit-user-select: none; /* Chrome, Safari, Opera */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Standard syntax */
  pointer-events: none; /* 这将阻止所有鼠标事件，包括拖动 */
}

.color-red {
  background-color: var(--el-color-danger);
  color: #fff;
}
.color-green {
  background-color: var(--el-color-success);
  color: #fff;
}
.color-blue {
  background-color: var(--el-color-primary);
  color: #fff;
}
.color-yellow {
  background-color: var(--el-color-warning);
  color: #fff;
}
.color-purple {
  background-color: #00ad767a;
  color: #fff;
}
.color-orange {
  background-color: #ff9800;
  color: #fff;
}
.color-pink {
  background-color: #e91e63;
  color: #fff;
}
.el-select__input-wrapper {
  max-width: 10px !important;
}
