<template>
  <div class="h100">
    <div class="map-container" ref="mapContainer">
      <!-- 表单容器 -->
      <template v-if="$slots.customForm">
        <slot name="customForm"></slot>
      </template>
      <template v-else>
        <div class="form-container" v-if="formFields.length > 0">
          <pl-form
            :fields="fields"
            inline
            :span="formSpan"
            confirmButtonText="搜索"
            cancelButtonText="重置"
            clear
            @confirm="handleSearch"
            @cancel="handleCancel"
          >
            <!-- Form插槽 -->
            <template
              v-for="field in formFields"
              :key="field.prop"
              v-slot:[field.template]
            >
              <div class="form-item-wrapper" v-if="field.template">
                <slot :name="field.template"></slot>
              </div>
            </template>
          </pl-form>
        </div>
      </template>

      <!-- 左侧站点列表容器 -->
      <template v-if="$slots.customSiteList">
        <slot name="customSiteList"></slot>
      </template>
      <template v-else>
        <div class="site-list-left" v-if="leftSitesArray.length > 0">
          <pl-scrollbar class="h100">
            <div
              class="site-list-item"
              v-for="item in leftSitesArray"
              :key="item"
            >
              <pl-checkbox
                v-model="item.checked"
                @change="handleSiteChange($event, item)"
                v-if="siteCheckbox"
              ></pl-checkbox>
              <div class="site-info" @click="handleSiteClick(item)">
                <div class="site-name">
                  {{ item.stopName }}
                  <el-tag v-if="siteTag" size="small">
                    {{ item.stopType || item.labelName }}
                  </el-tag>
                </div>
                <div class="site-address" v-if="item.address">
                  {{ item.address }}
                </div>
              </div>
              <pl-icon
                name="Delete"
                color="#ff504d"
                style="cursor: pointer"
                v-if="siteDelete"
                @click.stop="handleDelete(item)"
              ></pl-icon>
            </div>
          </pl-scrollbar>
        </div>
      </template>

      <!-- 确定按钮 -->
      <pl-button
        type="primary"
        class="confirm-btn"
        v-if="siteConfirm"
        @click="handleConfirm"
      >
        确定
      </pl-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch } from "vue";
import { useMap } from "pls-common";
import { useFence } from "./useFence";
import {
  useSites,
  chooseSite,
  clearMarkers,
  setSiteToMap,
  setImgIcon,
} from "./useSites";
import { on } from "@/utils/eventBus";
import { ElTag } from "element-plus";
import _ from "lodash";

const props = defineProps({
  formFields: {
    // 表单配置
    type: Array,
    default: () => [],
  },
  formSpan: {
    // 表单区域宽度
    type: Number,
    default: 6,
  },
  areaFence: {
    // 区域电子围栏数组
    type: [Array, String],
    default: () => [],
  },
  renderSiteToMap: {
    // 渲染到地图上的站点
    type: Array,
    default: () => [],
  },
  lists: {
    // 左侧站点列表
    type: Array,
    default: () => [],
  },
  synchronous: {
    // 地图上的站点和左侧站点列表是否同步(数据一样),不同步时，左侧列表展示选中的站点
    type: Boolean,
    default: false,
  },
  siteSort: {
    // 是否显示选中站点的序号
    type: Boolean,
    default: false,
  },
  sitesLibrary: {
    // 是否显示站点库
    type: Boolean,
    default: false,
  },
  siteTag: {
    // 站点标签
    type: Boolean,
    default: false,
  },
  siteDelete: {
    // 站点删除按钮
    type: Boolean,
    default: false,
  },
  // 是否显示复选框
  siteCheckbox: {
    type: Boolean,
    default: false,
  },
  // 是否显示确定按钮
  siteConfirm: {
    type: Boolean,
    default: false,
  },
  // 地图缩放按钮显示
  mapToolBar: {
    type: Boolean,
    default: false,
  },
  // 地图尺
  mapScale: {
    type: Boolean,
    default: false,
  },
  // 是否可以选择区域外站点
  isSelectAreaOutside: {
    type: Boolean,
    default: false,
  },
  allowClick: {
    type: Boolean,
    default: true,
  },
  setFitView: {
    type: Boolean,
    default: false,
  },
});

const selectArray = ref([]);
const fields = ref(props.formFields); // 表单配置

// 分开管理地图点和左侧列表
let mapSites = reactive(props.renderSiteToMap); // 地图上的点
let leftSitesArray = ref(props.lists); // 左侧列表

watch(
  () => props.lists,
  (e) => {
    leftSitesArray.value = e;
  }
);

// 监听点击事件
on("markerClick", (data) => {
  // 遍历新数据
  data.forEach((newItem) => {
    // 在老数据中找到相同 stopId 的项
    const oldItem = leftSitesArray.value.find(
      (oldItem) => oldItem.stopId === newItem.stopId
    );
    if (oldItem) {
      // 同步老数据的字段到新数据
      Object.keys(oldItem).forEach((key) => {
        if (!(key in newItem)) {
          newItem[key] = oldItem[key];
        }
      });
    }
  });
  // 更新 leftSitesArray
  leftSitesArray.value = data;
  emit("update:lists", data);
});

watch(
  () => props.areaFence,
  () => {
    const propData = _.cloneDeep(props);
    propData.areaFence = parseFenceData(props.areaFence);
    console.log(propData);
    useFence(mapBase, propData);
  },
  {
    deep: true,
  }
);

const emit = defineEmits([
  "formSearch",
  "formCancel",
  "confirm",
  "siteListChange",
  "update:lists",
]);

let mapBase = reactive(null);

const mapContainer = ref(null);

// 地图初始化
const mapInit = async () => {
  mapBase = await useMap(mapContainer, {
    scale: props.mapScale,
    toolBar: props.mapToolBar,
  });

  // 地图初始化后，更新 mapSites 并设置站点
  mapSites = reactive(props.renderSiteToMap);
  setUseSites();

  // 区域电子围栏
  if (props.areaFence) {
    const propData = _.cloneDeep(props);
    propData.areaFence = parseFenceData(props.areaFence);
    useFence(mapBase, propData);
  }
};

// 电子围栏转换
function parseFenceData(coordinates) {
  // 如果是字符串，先解析成数组
  const coordArray =
    typeof coordinates === "string" ? JSON.parse(coordinates) : coordinates;

  // 如果是多边形数组（multiple为true的情况）
  if (Array.isArray(coordArray[0]) && Array.isArray(coordArray[0][0])) {
    // 去重，因为可能有重复的多边形
    const uniquePolygons = coordArray
      .map(JSON.stringify)
      .filter((item, index, self) => self.indexOf(item) === index)
      .map(JSON.parse);

    return uniquePolygons;
  }

  // 如果是单个多边形（multiple为false的情况）
  return [coordArray];
}

const setUseSites = async () => {
  await useSites({
    mapDom: mapBase,
    defineProps: props,
    renderSiteToMap: mapSites,
    leftSitesArray,
  });
};

const getMapBase = () => {
  return mapBase;
};

// 在组件挂载时初始化
onMounted(() => {
  leftSitesArray.value = [];
  // 确保地图实例被清理
  if (mapBase?.map?.value) {
    mapBase.map.value.destroy();
  }
  mapBase = null;
  mapInit();
});

// 监听 props.renderSiteToMap 的变化
watch(
  () => props.renderSiteToMap,
  (newVal) => {
    // 只在地图初始化后更新数据
    if (mapBase?.map?.value) {
      mapSites = reactive(newVal);
      setUseSites();
    }
  },
  { deep: true }
);

// 选择站点
const handleSiteClick = async (item) => {
  mapBase.setCenter(item.longitude, item.latitude);
  mapBase.map.value.setZoom(18);
};

// 选中状态变化时更新左侧列表
const handleSiteChange = async (event, item) => {
  item.checked = event;
  chooseSite(item);

  if (event) {
    // 选中时添加到左侧列表
    if (!leftSitesArray.value.some((site) => site.stopId === item.stopId)) {
      leftSitesArray.value.push(item);
    }
  } else {
    // 取消选中时从左侧列表移除
    const index = leftSitesArray.value.findIndex(
      (site) => site.stopId === item.stopId
    );
    if (index > -1) {
      leftSitesArray.value.splice(index, 1);
    }
  }

  emit("siteListChange", leftSitesArray.value);
};

// 删除站点
const handleDelete = (item) => {
  // 确保 checked 和 isSelected 状态都被更新
  item.checked = false;
  item.isSelected = 0;

  // 调用 chooseSite 更新地图点状态
  chooseSite(item);
  //
};

// 确定按钮
const handleConfirm = () => {
  emit("confirm", selectArray.value);
};

// 表单点击事件
const handleSearch = (data) => {
  clearMarkers();
  emit("formSearch", data);
};

// 表单重置事件
const handleCancel = (data) => {
  emit("formCancel", data);
};

// 监听 formFields 变化
watch(
  () => props.formFields,
  (newVal) => {
    fields.value = newVal;
  },
  { deep: true, immediate: true }
);

const setSite = (list) => {
  leftSitesArray.value = list;
};
const getData = () => {
  return leftSitesArray.value;
};

defineExpose({
  getMapBase,
  handleSiteClick,
  setSiteToMap,
  setSite,
  setImgIcon,
  getData,
});
</script>

<style lang="scss" scoped>
.confirm-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10;
}
.site-list-left {
  position: absolute;
  left: 10px;
  top: 60px;
  width: 20%;
  height: 80%;
  background-color: #fff;
  z-index: 99;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 10px;
  .site-list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e6e6e6;
    cursor: pointer;
    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  .site-info {
    min-height: 40px;
    flex: 1;
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .site-name {
      font-weight: 600;
    }
    .site-address {
      margin-top: 5px;
      color: var(--el-text-color-secondary);
    }
  }
}
:deep(.sort) {
  background-color: #ff504d;
  color: #fff;
  border-radius: 5px;
  padding: 2px 3px;
  line-height: 1;
  font-size: 12px;
  margin-right: 5px;
}
.form-container {
  position: relative;
  z-index: 9;
  padding: 10px;
}
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}
:deep(.amap-marker-label) {
  border: none;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  text-shadow: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}
:deep(.marker-label) {
  display: inline-block;
  text-align: center;
  width: 100%;
  font-size: 13px;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  border: none;
}
.form-item-wrapper {
  position: relative;
  z-index: 9;
  width: 100%;
}
</style>
