<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'menu_service_list:btn_service_add'"
          >新增</pl-button
        >
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, tableColumns)"
        >
          <template #status="{ scope }">
            <span class="success-color" v-if="scope.row.status === 1"
              >启用</span
            >
            <span class="error-color" v-else>停用</span>
          </template>
          <template #operation="{ scope }">
            <pl-button
              link
              @click="clickDetails(scope.row, 'details')"
              v-has="'menu_service_list:btn_service_detail'"
              >查看</pl-button
            >
            <pl-button
              type="primary"
              link
              @click="clickDetails(scope.row, 'edit')"
              v-has="'menu_service_list:btn_service_edit'"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope, 'serviceId')"
              v-has="'menu_service_list:btn_service_del'"
              >删除</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit"
      >
      </drawerFrom>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { autoCode } from "@/utils/index";
import { getLabelList } from "@/api/index";
import { getProductList } from "@/api/contacts";
const FWLXLabelList = ref([]);
getLabelList({
  categoryCode: "FWLX",
}).then((res) => {
  if (res.code == 200 && res.data) {
    FWLXLabelList.value = res.data;
  }
});

// 产品列表
const productList = ref([]);
getProductList().then((res) => {
  if (res.code == 200 && res.data) {
    productList.value = res.data;
  }
});

// 查看详情
const clickDetails = (row, type) => {
  if (row.serviceType) {
    row.serviceType = `${row.serviceType}`;
  }
  if (type == "details") {
    handleDetail(row);
  } else {
    handleEdit(row);
  }
};

onMounted(() => {
  //组件加载完成后执行事件
  // 客户状态
});

// 加载状态控制
const tabLoading = ref(false);
// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  handleAdd,
  handleEdit,
  handleDetail,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
  handleDrawerSubmit,
} = useTable({
  list: "/contract/service/pageService",
  add: "/contract/service/saveService",
  edit: "/contract/service/updateService",
  delete: "/contract/service/deleteService/",
  del: {
    message: "确定要删除该服务项目吗？",
  },
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "服务名称",
    prop: "serviceName",
    type: "input",
  },
  {
    label: "服务类型",
    prop: "fwlx",
    type: "label-select",
    options: FWLXLabelList,
    cascaderProps: {
      label: "labelName",
      value: "labelId",
    },
    onChange: (val) => {
      if (val) {
        queryForm.value.serviceTypeList = val.split(",");
      }
    },
  },
  {
    label: "所属产品",
    prop: "productIdList",
    type: "select",
    options: productList,
    labelKey: "productName",
    valueKey: "productId",
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "状态",
    prop: "status",
    type: "select",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
]);

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "服务名称",
    prop: "serviceName",
    type: "input",
    input: (e) => {
      fromData.value.serviceSign = autoCode(e);
    },
    rules: [
      {
        required: true,
        message: "请输入服务名称",
        trigger: "blur",
      },
    ],
  },
  {
    label: "服务标识",
    prop: "serviceSign",
    type: "input",
    disabled: true,
    rules: [
      {
        required: true,
        message: "请输入服务标识",
        trigger: "blur",
      },
    ],
  },
  {
    label: "服务类型",
    prop: "serviceType",
    type: "label-select",
    options: FWLXLabelList,
    cascaderProps: {
      label: "labelName",
      value: "labelId",
    },
    multiple: false,
    rules: [
      {
        required: true,
        message: "请选择服务类型",
        trigger: "change",
      },
    ],
  },
  {
    label: "所属产品",
    prop: "productId",
    type: "select",
    options: productList,
    labelKey: "productName",
    valueKey: "productId",
  },
  {
    label: "状态",
    prop: "status",
    type: "radio",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
  {
    label: "描述",
    prop: "remark",
    type: "textarea",
  },
]);

// 表格列配置
const tableColumns = ref([
  { label: "序号", type: "index", width: 80 },
  { label: "服务名称", prop: "serviceName", sortable: "custom" },
  { label: "服务标识", prop: "serviceSign", sortable: "custom" },
  {
    label: "所属产品",
    prop: "productName",
    sortable: "custom",
    sortProp: "productId",
  },
  { label: "所属类型", prop: "serviceTypeName", sortable: "custom" },
  { label: "描述", prop: "remark", sortable: "custom" },
  { label: "状态", prop: "status", sortable: "custom", template: "status" },
  // { label: "创建时间", prop: "createTime", sortale: true },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped></style>
