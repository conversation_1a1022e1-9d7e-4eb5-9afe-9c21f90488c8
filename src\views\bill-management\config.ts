import { ref } from "vue";

export const tableColumns = ref([
  { label: "序号", type: "index", width: 60 },
  { label: "票号", prop: "ticketNo", minWidth: 200, sortable: "custom" },
  { label: "乘车人", prop: "passName", minWidth: 90, sortable: "custom" },
  { label: "联系方式", prop: "passPhone", minWidth: 120, sortable: "custom" },
  {
    label: "发车时间",
    prop: "departureTime",
    minWidth: 160,
  },
  { label: "起点", prop: "startStopName", minWidth: 160 },
  { label: "终点", prop: "endStopName", minWidth: 160 },
  { label: "座位号", prop: "seatNum", minWidth: 100, sortable: "custom" },
  {
    label: "票价（元）",
    prop: "price",
    price: true,
    minWidth: 120,
    sortable: "custom",
  },
  { label: "线路班次", prop: "schedulingNo", minWidth: 120 },
  {
    label: "订单状态",
    minWidth: 100,
    template: "orderStatus",
  },
  {
    label: "票务状态",
    minWidth: 100,
    template: "ticketStatus",
  },
  {
    label: "检票状态",
    prop: "ticketCheckStatusName",
    minWidth: 100,
  },
  { label: "操作", template: "operation", minWidth: 220, fixed: "right" },
]);
