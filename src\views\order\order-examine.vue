<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        formType="1"
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <template #operation="{ scope }">
            <pl-button
              link
              type="primary"
              @click="handleDetail(scope.row, 'sh')"
              v-if="scope.row.reviewStatus == 0"
              v-has="'men_refund_list:btn_refund'"
              >审核</pl-button
            >
            <pl-button
              link
              @click="handleDetail(scope.row)"
              v-has="'men_refund_list:btn_detail'"
              >查看</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <!-- <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit(event)"
      >
      </drawerFrom> -->

      <pl-drawer
        v-model="drawerVisible"
        :title="drawerTitle"
        @close="drawerVisible = false"
      >
        <pl-scrollbar>
          <div class="order-drawer-box" v-if="drawerVisible">
            <div>
              <div class="drawer-w">
                <div class="title">订单信息</div>
                <pl-form
                  :fields="orderColumns"
                  :form="fromData"
                  inline
                  :disabled="formDisabled"
                  :isButtonShow="false"
                ></pl-form>
              </div>
              <div class="drawer-w">
                <div class="title">金额信息</div>
                <pl-form
                  :fields="priceColumns"
                  :form="fromData"
                  inline
                  :disabled="formDisabled"
                  :isButtonShow="false"
                ></pl-form>
              </div>
              <div
                class="drawer-w"
                v-if="!isReview && fromData.reviewStatus != 0"
              >
                <div class="title">审核信息</div>
                <pl-form
                  :fields="examineColumns"
                  :form="fromData"
                  inline
                  :disabled="formDisabled"
                  :isButtonShow="false"
                ></pl-form>
              </div>
            </div>
            <plStatusFlow
              :data="statusFlowData"
              class="status-flow"
              v-if="statusFlowData.length"
            >
              <template #content="{ item }">
                <div class="flow-info">
                  <div class="item">操作人: {{ item.optName }}</div>
                  <div class="item optTime">操作时间: {{ item.optTime }}</div>
                  <div class="item remark" v-if="item.remark">
                    备注: {{ item.remark }}
                  </div>
                </div>
              </template>
            </plStatusFlow>
          </div>
        </pl-scrollbar>
        <div class="button-box" v-if="isReview">
          <pl-button class="btn" @click="handleOrderReview(false)"
            >驳回</pl-button
          >
          <!-- <pl-button class="btn">确定并新增</pl-button> -->
          <pl-button class="btn" type="primary" @click="handleOrderReview(true)"
            >通过</pl-button
          >
        </div>
      </pl-drawer>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, h } from "vue";
import { useTable } from "@/hooks/usetTable";
import { getDictionaryData } from "@/api/dict";
import { plInput, plMessageBox, plMessage } from "pls-common";
import { OD } from "@/api/order";

const business_type = ref([]);
const order_type = ref([]);
const fromData = ref({});
const applyUser = ref([]);
const examineUser = ref([]);

/**
 * 获取申请人和用户列表
 */
OD.orderApplyUserList().then((res) => {
  applyUser.value = res.data.filter((item) => item.reviewType == 2);
  examineUser.value = res.data.filter((item) => item.reviewType == 1);
});

// 业务类型
getDictionaryData({
  dictTypeCode: "business_type",
}).then((res) => {
  business_type.value = res;
});

// 订单类型
getDictionaryData({
  dictTypeCode: "order_type",
}).then((res) => {
  order_type.value = res;
});
/**
 * 审批
 */
const handleOrderReview = (status) => {
  let reason = ref("");
  if (!status) {
    plMessageBox
      .confirm(
        h("div", null, [
          h("div", null, "确定驳回该订单吗？"),
          h(plInput, {
            type: "textarea",
            modelValue: reason,
            placeholder: "请输入驳回原因",
            class: "reject-input",
          }),
        ]),
        "驳回",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "驳回",
          cancelButtonText: "取消",
        }
      )
      .then(() => {
        postRejectOrder({
          reviewDescribe: reason.value,
          status,
        });
      })
      .catch(() => {});
  } else {
    plMessageBox
      .confirm("确定要给订单退款吗？", "温馨提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
      .then(() => {
        postRejectOrder({
          status,
        });
      })
      .catch(() => {});
  }
};

/**
 * 驳回接口
 */
const postRejectOrder = (obj) => {
  OD.orderReview({
    orderNo: fromData.value.orderNo,
    reviewNo: fromData.value.reviewNo,
    reviewDescribe: obj.reviewDescribe,
    reviewStatus: obj.status ? 1 : 2,
    orderType: fromData.value.orderType,
    appCode: "fengjie_scenic_area",
  }).then((res) => {
    if (res.code == 200) {
      plMessage("操作成功", "success");
      drawerVisible.value = false;
      loadData();
    }
  });
};

const isReview = ref(false);

/**
 * 查看详情
 */
const handleDetail = (row, type) => {
  isReview.value = type ? true : false;
  formDisabled.value = true;
  OD.orderReviewDetail(row.reviewNo).then((res) => {
    if (res.code == 200) {
      fromData.value = res.data;
      drawerTitle.value = "审核详情";
      drawerVisible.value = true;
      if (
        res.data.orderStatusRecordList &&
        res.data.orderStatusRecordList.length > 0
      ) {
        statusFlowData.value = res.data.orderStatusRecordList.map((item) => {
          return {
            label: item.orderStatusName || "",
            iconStatus: "success",
            statusColor: "green",
            lineColor: "green",
            optName: item.optName || "",
            optTime: item.optTime,
          };
        });
      }
    }
  });
};

const statusFlowData = ref([]);

const orderColumns = ref([
  {
    label: "业务单号",
    prop: "reviewNo",
    type: "input",
  },
  {
    label: "关联订单",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    type: "input",
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
    type: "input",
  },
  {
    label: "退款规则",
    prop: "refundRule",
    type: "input",
  },
  {
    label: "订单状态",
    prop: "orderStatus",
    type: "input",
  },
  {
    label: "发起渠道",
    prop: "orderPlatform",
    type: "input",
  },
  {
    label: "发起人",
    prop: "applyUserName",
    type: "input",
  },
  {
    label: "发起时间",
    prop: "applyTime",
    type: "input",
  },
  {
    label: "退款方式",
    prop: "xxx",
    type: "input",
  },
  {
    label: "退款笔数",
    prop: "refundSort",
    type: "input",
  },
  {
    label: "退款原因",
    prop: "refundDescribe",
    type: "input",
  },
]);
const priceColumns = ref([
  {
    label: "订单金额",
    prop: "orderAmount",
    type: "input",
    unit: "元",
  },
  {
    label: "申请退款金额",
    prop: "applyAmount",
    type: "input",
    unit: "元",
  },
]);
const examineColumns = ref([
  {
    label: "审核结果",
    prop: "reviewStatusName",
    type: "input",
  },
  {
    label: "审核时间",
    prop: "reviewTime",
    type: "input",
  },
  {
    label: "审核人",
    prop: "reviewUserName",
    type: "input",
  },
  {
    label: "驳回原因",
    prop: "reviewDescribe",
    type: "input",
  },
]);

// 加载状态控制
const tabLoading = ref(false);
// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  current,
  loadData,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/order/review/orderRefundPage",
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "业务单号",
    prop: "reviewNo",
    type: "input",
    placeholder: "请输入名称",
  },
  {
    label: "关联订单",
    prop: "orderNo",
    type: "input",
    placeholder: "请输入关联订单",
  },
  {
    label: "业务类型",
    prop: "businessType",
    type: "select",
    options: business_type,
    placeholder: "请选择业务类型",
  },
  {
    label: "订单类型",
    prop: "orderType",
    type: "select",
    options: order_type,
    placeholder: "请选择订单类型",
  },
  {
    label: "审核状态",
    prop: "reviewStatus",
    type: "select",
    options: [
      {
        label: "待审核",
        value: 0,
      },
      {
        label: "通过",
        value: 1,
      },
      {
        label: "驳回",
        value: 2,
      },
    ],
    placeholder: "请选择审核状态",
  },
  {
    label: "审核人",
    prop: "reviewUserId",
    type: "select",
    labelKey: "userName",
    valueKey: "userId",
    options: examineUser,
    placeholder: "请选择审核人",
  },
  {
    label: "发起人",
    prop: "applyUserId",
    type: "select",
    options: applyUser,
    labelKey: "userName",
    valueKey: "userId",
    placeholder: "请选择发起人",
  },
]);

// 表格列配置
const tableColumns = ref([
  { label: "序号", type: "index", width: 80 },
  { label: "业务单号", prop: "reviewNo", sortable: "custom", minWidth: 150 },
  { label: "关联订单", prop: "orderNo", sortable: "custom", minWidth: 150 },
  { label: "订单类型", prop: "orderTypeName", sortable: "custom" },
  { label: "业务类型", prop: "businessTypeName", sortable: "custom" },
  {
    label: "原订单金额（元）",
    prop: "orderAmount",
    sortable: "custom",
    price: true,
    align: "center",
    minWidth: 150,
  },
  {
    label: "申请金额（元）",
    prop: "applyAmount",
    sortable: "custom",
    price: true,
    align: "center",
    minWidth: 150,
  },
  { label: "审核状态", prop: "reviewStatusName", sortable: "custom" },
  { label: "申请时间", prop: "applyTime", sortable: "custom", minWidth: 150 },
  { label: "审核时间", prop: "reviewTime", sortable: "custom" },
  { label: "审核人", prop: "reviewUserName", sortable: "custom" },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped>
.flow-info {
  .item {
    margin-bottom: 5px;
    &.remark {
      font-size: 12px;
    }
    &.optTime {
      color: #999;
      font-size: 12px;
    }
  }
}
.el-message-box__message {
  width: 100%;
  flex: 1;
}
.order-drawer-box {
  display: flex;
  padding-bottom: 70px;
  .status-flow {
    padding-top: 20px;
  }
}
</style>
<style lang="scss">
.el-message-box__message {
  flex: 1;
}
.reject-input {
  width: 100%;
  height: 100px;
  margin-top: 15px;
  .el-textarea__inner {
    height: 100%;
  }
}
</style>
