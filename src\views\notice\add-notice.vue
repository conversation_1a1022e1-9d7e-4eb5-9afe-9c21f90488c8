<!-- 新建问答 -->
<template>
  <div class="add-answer-popup">
    <div class="left-box">
      <div class="title">
        <pl-input v-model="formData.title" placeholder="请输入标题" />
      </div>

      <Editor v-model="formData.content"></Editor>
    </div>
    <div class="right-box">
      <pl-form :fields="formColumns" ref="formRef" :form="formData">
        <template #recipientIds>
          <PublicSelect
            multiple
            v-model="formData.recipientIds"
            showCustom
            :disabled="false"
            nodeKey="userId"
            :customOptions="userList"
            :casProps="{
              label: 'userName',
              value: 'userId',
            }"
            :checkAll="checkAll"
          >
            <template #head>
              <div class="check-all">
                <pl-checkbox
                  v-model="checkAll"
                  :indeterminate="isIndeterminate"
                ></pl-checkbox>
                <div style="margin-left: 10px">选择全部</div>
              </div>
            </template>
          </PublicSelect>
        </template>
      </pl-form>
    </div>

    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <!-- <pl-button class="btn">确定并新增</pl-button> -->
      <pl-button
        class="btn"
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        >确定</pl-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import Editor from "@/components/editor/editor.vue";
import { plMessage } from "pls-common";
import { getDriverUserList, getClientUserList } from "@/api/index";
import PublicSelect from "@/components/PublicSelect/PublicSelect.vue";
const emit = defineEmits(["close", "submit"]);
const formRef = ref(null);
const checkAll = ref(false);
const isIndeterminate = ref(false);
const formData = ref({});

// 监听新增时所属客户选择变化
watch(
  () => formData.value.recipientIds,
  (val) => {
    if (Array.isArray(val)) {
      const checkedCount = val.length;
      const totalCount = userList.value.length;

      // 只更新 isIndeterminate 状态，不更新 checkAll
      isIndeterminate.value = checkedCount > 0 && checkedCount < totalCount;
    }
  },
  { deep: true }
);
const props = defineProps({
  categoryList: {
    type: Array,
    default: () => [],
  },
  appList: {
    type: Array,
    default: () => [],
  },
  noticeType: {
    type: Array,
    default: () => [],
  },
  from: {
    type: Object,
    default: () => ({}),
  },
});

formData.value = { ...props.from };
if (formData.value.isAllRecipient) {
  checkAll.value = true;
}

if (formData.value.appCode) {
  const { userType } = props.appList.find(
    (item) => item.appCode === formData.value.appCode
  );
  if (userType === 1) {
    getDriverUserList().then((res) => {
      if (res.code == 200) {
        userList.value = userList.value.concat(
          res.data
            .filter((item) => item.phone && item.username)
            .map((item) => {
              return {
                userName: item.username,
                userId: item.userId,
              };
            })
        );
      }
    });
  } else if (userType === 2) {
    getClientUserList().then((res) => {
      if (res.code == 200) {
        userList.value = userList.value.concat(
          res.data
            .filter((item) => item.phone && item.nickname)
            .map((item) => {
              return {
                userName: item.nickname,
                userId: item.clientUserId,
              };
            })
        );
      }
    });
  }
}

const userList = ref([]);
const formColumns = ref([
  {
    label: "所属应用",
    type: "select",
    prop: "appCode",
    options: props.appList,
    valueKey: "appCode",
    labelKey: "appName",
    rules: [
      {
        required: true,
        message: "请选择所属应用",
      },
    ],
    onChange: (_, item) => {
      let { userType } = item;
      userList.value = [];
      if (userType === 1) {
        getDriverUserList().then((res) => {
          if (res.code == 200) {
            userList.value = userList.value.concat(
              res.data
                .filter((item) => item.phone)
                .map((item) => {
                  return {
                    userName: item.username,
                    userId: item.userId,
                  };
                })
            );
            console.log(userList.value);
          }
        });
      } else if (userType === 2) {
        getClientUserList().then((res) => {
          if (res.code == 200) {
            userList.value = userList.value.concat(
              res.data
                .filter((item) => item.phone)
                .map((item) => {
                  return {
                    userName: item.nickname,
                    userId: item.clientUserId,
                  };
                })
            );
          }
        });
      }
    },
  },

  {
    label: "公告类型",
    type: "select",
    prop: "noticeType",
    options: props.noticeType,
    rules: [
      {
        required: true,
        message: "请选择公告类型",
      },
    ],
  },
  {
    label: "接收人",
    template: "recipientIds",
    prop: "recipientIds",
    options: userList,
    valueKey: "userId",
    labelKey: "userName",
    rules: [
      {
        required: true,
        message: "请选择接收人",
      },
    ],
  },
]);
const handleClose = () => {
  emit("close");
};
const loading = ref(false);
const handleSubmit = () => {
  if (!formData.value.title) {
    plMessage("请输入标题", "warning");
    return;
  }
  if (formData.value.content === "<p><br></p>") {
    plMessage("请输入内容", "warning");
    return;
  }
  formRef.value.confirm((data) => {
    loading.value = true;

    data.isAllRecipient = checkAll.value && !isIndeterminate.value ? 1 : 0;
    setTimeout(() => {
      loading.value = false;
    }, 1500);
    emit("submit", data);
  });
};
</script>

<style lang="scss" scoped>
.editor-box {
  border: 1px solid var(--el-border-color);
  margin-top: 10px;
}
.add-answer-popup {
  display: flex;
  height: 100%;
  padding-bottom: 60px;
  .left-box {
    flex: 1;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    .editor-box {
      flex: 1;
    }
  }
  .right-box {
    width: 400px;
    height: 100%;
  }
}
.check-all {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 13px;
  color: #999;
}
</style>
