<template>
  <div class="h100">
    <div class="map-container" ref="mapContainer">
      <!-- 表单容器 -->
      <template v-if="$slots.customForm">
        <slot name="customForm"></slot>
      </template>
      <template v-else>
        <div class="form-container" v-if="formFields.length > 0">
          <pl-form
            :fields="fields"
            inline
            :span="formSpan"
            confirmButtonText="搜索"
            cancelButtonText="重置"
            clear
            @confirm="handleSearch"
            @cancel="handleCancel"
          >
            <!-- Form插槽 -->
            <template
              v-for="field in formFields"
              :key="field.prop"
              v-slot:[field.template]
            >
              <div class="form-item-wrapper" v-if="field.template">
                <slot :name="field.template"></slot>
              </div>
            </template>
          </pl-form>
        </div>
      </template>

      <!-- 左侧站点列表容器 -->
      <template v-if="$slots.customSiteList">
        <slot name="customSiteList"></slot>
      </template>
      <template v-else>
        <div class="site-list-left" v-if="leftSitesArray.length > 0">
          <pl-scrollbar class="h100">
            <div
              class="site-list-item"
              v-for="item in leftSitesArray"
              :key="item"
            >
              <pl-checkbox
                v-model="item.checked"
                @change="handleSiteChange($event, item)"
                v-if="siteCheckbox"
              ></pl-checkbox>
              <div class="site-info" @click="handleSiteClick(item)">
                <div class="site-name">
                  {{ item.stopName }}
                  <el-tag v-if="siteTag" size="small">{{
                    item.stopType
                  }}</el-tag>
                </div>
                <div class="site-address" v-if="item.address">
                  {{ item.address }}
                </div>
              </div>
              <pl-icon
                name="Delete"
                color="#ff504d"
                style="cursor: pointer"
                v-if="siteDelete"
                @click="handleDelete(item)"
              ></pl-icon>
            </div>
          </pl-scrollbar>
        </div>
      </template>

      <!-- 确定按钮 -->
      <pl-button
        type="primary"
        class="confirm-btn"
        v-if="siteConfirm"
        @click="handleConfirm"
      >
        确定
      </pl-button>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  getCurrentInstance,
  reactive,
  watch,
  h,
  resolveComponent,
  computed,
} from "vue";
import { useMap } from "pls-common";
import { useFence } from "./useFence";
import { useSites, chooseSite, clearMarkers } from "./useSites";
import { on } from "@/utils/eventBus";
import { ElTag } from "element-plus";
import _ from "lodash";

const props = defineProps({
  formFields: {
    // 表单配置
    type: Array,
    default: () => [],
  },
  formSpan: {
    // 表单区域宽度
    type: Number,
    default: 6,
  },
  areaFence: {
    // 区域电子围栏数组
    type: Array,
    default: () => [],
  },
  renderSiteToMap: {
    // 渲染到地图上的站点
    type: Array,
    default: () => [],
  },
  lists: {
    // 左侧站点列表，如果有就显示，没有就不显示
    type: Array,
    default: () => [],
  },
  synchronous: {
    // 地图上的站点和左侧站点列表是否同步(数据一样),不同步时，左侧列表展示选中的站点
    type: Boolean,
    default: false,
  },
  siteSort: {
    // 是否显示选中站点的序号
    type: Boolean,
    default: false,
  },
  sitesLibrary: {
    // 是否显示站点库
    type: Boolean,
    default: false,
  },
  siteTag: {
    // 站点标签
    type: Boolean,
    default: false,
  },
  siteDelete: {
    // 站点删除按钮
    type: Boolean,
    default: false,
  },
  // 是否显示复选框
  siteCheckbox: {
    type: Boolean,
    default: false,
  },
  // 是否显示确定按钮
  siteConfirm: {
    type: Boolean,
    default: false,
  },
  // 地图缩放按钮显示
  mapToolBar: {
    type: Boolean,
    default: false,
  },
  // 地图尺
  mapScale: {
    type: Boolean,
    default: false,
  },
});
const selectArray = ref([]);
const fields = ref(props.formFields); // 表单配置
let renderSiteToMap = reactive(props.renderSiteToMap); // 渲染到地图上的站点
console.log("renderSiteToMap", renderSiteToMap);
let lists = reactive(props.lists);
let leftSitesArray = ref(props.synchronous ? renderSiteToMap : lists); // 左侧站点列表

// 监听点击事件
on("markerClick", (data) => {
  if (!props.synchronous) {
    // 如果不同步，则将选中的站点添加到左侧站点列表
    leftSitesArray.value = data.map((item) => {
      item.checked = true;
      return item;
    });
  }
  console.log('点击=====点',props.synchronous);
  

  selectArray.value = data;
});

watch(
  () => props.renderSiteToMap,
  (newVal) => {
    renderSiteToMap = newVal;
    if (props.synchronous) {
      leftSitesArray.value = newVal;
    }

    setUseSites();
  },
  {
    deep: true,
  }
);
watch(
  () => renderSiteToMap,
  (newVal) => {
    console.log("map-pro的地图点", newVal);
    if (props.synchronous) {
      console.log("===", props.synchronous);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => props.areaFence,
  () => {
    useFence(mapBase, props);
  },
  {
    deep: true,
  }
);

const emit = defineEmits([
  "formSearch",
  "formCancel",
  "confirm",
  "siteListChange",
]);

let mapBase = reactive(null);

const mapContainer = ref(null);

// 地图初始化
const mapInit = async () => {
  console.log("地图初始化");
  mapBase = await useMap(mapContainer, {
    scale: props.mapScale,
    toolBar: props.mapToolBar,
  });
  // 地图站点主函数
  if (props.renderSiteToMap) {
    setUseSites();
  }

  // 区域电子围栏
  if (props.areaFence) {
    useFence(mapBase, props);
  }
};

const setUseSites = async () => {
  await useSites({
    mapDom: mapBase,
    defineProps: props,
    renderSiteToMap,
    leftSitesArray,
  });
};

const getMapBase = () => {
  return mapBase;
};

// 初始化时，如果是同步模式，确保 leftSitesArray 包含所有点且为选中状态
const initSites = () => {
  if (props.synchronous && props.renderSiteToMap) {
    leftSitesArray.value = props.renderSiteToMap.map(site => ({
      ...site,
      checked: true
    }));
    setUseSites();
  }
};

// 在组件挂载时初始化
onMounted(() => {
  mapInit();
  initSites();
});

// 选择站点
const handleSiteClick = async (item) => {
  mapBase.setCenter(item.longitude, item.latitude);
  mapBase.map.value.setZoom(18);
};

const handleSiteChange = async (event, item) => {
  item.checked = event;
  chooseSite(item);
};
// 删除站点
const handleDelete = (item) => {
  item.checked = false;
  chooseSite(item);
};

// 确定按钮
const handleConfirm = () => {
  emit("confirm", selectArray.value);
};

// 表单点击事件
const handleSearch = (data) => {
  clearMarkers();
  emit("formSearch", data);
};

// 表单重置事件
const handleCancel = (data) => {
  emit("formCancel", data);
};

// 监听 formFields 变化
watch(
  () => props.formFields,
  (newVal) => {
    fields.value = newVal;
  },
  { deep: true, immediate: true }
);
watch(
  () => leftSitesArray.value,
  (newVal) => {
    emit("siteListChange", newVal);
  }
);

// 检查是否有对应的模板
const hasTemplate = (templateName) => {
  return props.formFields.some((field) => field.template === templateName);
};

const renderForm = computed(() => {
  return () => {
    const PlForm = resolveComponent("pl-form");

    // 创建插槽对象
    const slots = {};
    props.formFields.forEach((field) => {
      if (field.template) {
        slots[field.template] = () => {
          const instance = getCurrentInstance();
          if (instance?.slots[field.template]) {
            return h(
              "div",
              {
                style: {
                  position: "relative",
                  zIndex: 9,
                  width: "100%",
                },
              },
              [instance.slots[field.template]()]
            );
          }
          return null;
        };
      }
    });

    return h(
      PlForm,
      {
        fields: fields.value,
        inline: true,
        span: props.formSpan,
        confirmButtonText: "搜索",
        cancelButtonText: "重置",
        clear: true,
        onConfirm: handleSearch,
        onCancel: handleCancel,
      },
      slots
    );
  };
});

defineExpose({
  getMapBase,
  handleSiteClick,
});
</script>

<style lang="scss" scoped>
.confirm-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10;
}
.site-list-left {
  position: absolute;
  left: 10px;
  top: 60px;
  width: 20%;
  height: 80%;
  background-color: #fff;
  z-index: 99;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 10px;
  .site-list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e6e6e6;
    cursor: pointer;
    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  .site-info {
    min-height: 40px;
    flex: 1;
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .site-name {
      font-weight: 600;
    }
    .site-address {
      margin-top: 5px;
      color: var(--el-text-color-secondary);
    }
  }
}
:deep(.sort) {
  background-color: #ff504d;
  color: #fff;
  border-radius: 5px;
  padding: 2px 3px;
  line-height: 1;
  font-size: 12px;
  margin-right: 5px;
}
.form-container {
  position: relative;
  z-index: 9;
  padding: 10px;
}
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}
:deep(.amap-marker-label) {
  border: none;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  text-shadow: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}
:deep(.marker-label) {
  display: inline-block;
  text-align: center;
  width: 100%;
  font-size: 13px;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  border: none;
}
.form-item-wrapper {
  position: relative;
  z-index: 9;
  width: 100%;
}
</style>
