<template>
  <div>
    <iframe
      class="iframe"
      src="/Cgo8/realtime/TripTraceReplay/Index?vehicleid=0&module=2"
      frameborder="0"
      ref="iframeRef"
      sandbox="allow-scripts allow-same-origin"
    ></iframe>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
const iframeRef = ref(null);
onMounted(() => {
  iframeRef.value.addEventListener("load", () => {
    const contentWindow = iframeRef.value.contentWindow;
    window.parent.Global = contentWindow.Global;
    window.parent.$ = contentWindow.$;
    const doc = contentWindow.document;

    const inputDome = doc.getElementsByClassName(
      "textbox-text validatebox-text textbox-prompt"
    )[0];
    setTimeout(() => {
      doc.getElementById("PlateNum").value = "渝A18G66";
      inputDome.value = "渝A18G66";
      inputDome.classList.remove("textbox-prompt");
      doc.getElementById("VehicleId").value = "21322";
      doc.getElementById("SimNum").value = "51770231415";
      doc.getElementById("btnQuerySubmit").click();
    }, 5000);
  });
});
</script>

<style lang="scss" scoped>
.iframe {
  width: 100vw;
  height: 100vh;
}
</style>
