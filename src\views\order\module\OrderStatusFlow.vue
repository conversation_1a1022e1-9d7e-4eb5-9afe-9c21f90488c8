<template>
  <div class="order-flow-container">
    <div class="flow-line">
      <div v-if="currentStatus == 6" class="close-box">
        <img class="close-icon" src="@/assets/img/order-close.png" alt="" />
        <div class="status-text">订单已关闭</div>
      </div>

      <template v-else>
        <div
          v-for="(status, index) in statusList"
          :key="status.value"
          class="flow-item"
          :class="{
            active: isActive(status.value),
            completed: isCompleted(status.value),
          }"
        >
          <div class="status-node">
            <pl-icon
              v-if="isCompleted(status.value)"
              name="Check"
              class="check-icon"
            />
          </div>
          <div class="status-text">
            <div class="label">{{ status.label }}</div>
            <div class="time" v-if="status.value == 0">
              <pl-button
                type="primary"
                size="small"
                v-if="currentStatus == 0"
                @click="handleClickBtn('lx')"
                >确定联系</pl-button
              >
              <div v-else>联系时间:2024-12-25 10:00:00</div>
            </div>

            <!-- <div class="time" v-if="status.value == 0">
            <pl-button type="primary" size="small" v-if="isActive(status.value)"
              >确定联系</pl-button
            >
            <div v-else>联系时间:2024-12-25 10:00:00</div>
          </div> -->

            <div class="time" v-if="status.value == 1">
              <pl-button
                type="primary"
                size="small"
                @click="handleClickBtn('fk')"
                v-if="isActive(status.value)"
                >确定付款</pl-button
              >
              <div v-else>
                <!-- <div class="">线路名称：{{ data.travelLineName }}</div> -->
                <div>班次：{{ data.trainNo }}-{{ data.orderTime }}</div>
              </div>
            </div>

            <div class="time" v-if="status.value == 2">
              <pl-button
                type="primary"
                size="small"
                v-if="isActive(status.value)"
                @click="handleClickBtn('pc')"
                >确定派车</pl-button
              >
              <div v-else>
                <!-- <div class="">线路名称：{{ data.travelLineName }}</div> -->
                <div>班次：{{ data.trainNo }}-{{ data.orderTime }}</div>
              </div>
            </div>

            <div class="time" v-if="status.value == 3">
              <div>
                <!-- <div class="">线路名称：{{ data.travelLineName }}</div> -->
                <div>班次：{{ data.trainNo }}-{{ data.orderTime }}</div>
                <div>车牌号：{{ data.licensePlateNo }}</div>
                <div>司机：{{ data.driverName }}</div>
                <div>联系电话：{{ data.driverPhone }}</div>
              </div>
            </div>

            <!-- 已完成 start -->
            <div class="time" v-if="status.value == 5">
              <pl-button
                type="primary"
                size="small"
                v-if="currentStatus == 4"
                @click="handleClickBtn('dd')"
                >到达</pl-button
              >
            </div>
            <!-- 已完成 end -->
          </div>
          <div v-if="index < statusList.length - 1" class="connect-line"></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentStatus: {
    type: Number,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
});

const statusList = [
  { label: "待确定", value: 0 },
  { label: "待付款", value: 1 },
  { label: "待派车", value: 2 },
  { label: "待出行", value: 3 },
  { label: "出行中", value: 4 },
  { label: "已完成", value: 5 },
];

const isActive = (status) => status === props.currentStatus;
const isCompleted = (status) => {
  if (props.currentStatus == 0) {
    return false;
  } else if (props.currentStatus == 4 && status == 4) {
    return true;
  } else if (props.currentStatus == 5 && status == 5) {
    return true;
  }
  return status < props.currentStatus;
};
const emit = defineEmits(["clickBtn"]);
const handleClickBtn = (type) => {
  emit("clickBtn", type, props.data);
};
</script>

<style scoped lang="scss">
.close-box {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex: 1;
  .status-text {
    margin-top: 10px;
  }
}
.close-icon {
  width: 50px;
}
.order-flow-container {
  padding: 20px;

  .flow-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flow-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 100px;
    margin-bottom: 40px;

    .status-node {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 2px solid #dcdfe6;
      background-color: #fff;
      margin-bottom: 8px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      .check-icon {
        font-size: 14px;
        color: #fff;
      }
    }

    .status-text {
      font-size: 14px;
      color: #909399;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      width: 100%;
      position: relative;

      .label {
        margin-bottom: 4px;
      }

      .time {
        font-size: 12px;
        color: #909399;
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 8px;
        width: max-content;
        max-width: 250px;
        text-align: left;
        :deep(.pl-button) {
          margin: 4px 0;
        }
      }
    }

    .connect-line {
      position: absolute;
      top: 12px;
      right: -50%;
      width: 100%;
      height: 2px;
      background-color: #dcdfe6;
      z-index: -1;
    }

    // &.active {
    //   .status-node {
    //     border-color: #2b85e4;
    //     background-color: #2b85e4;
    //   }
    //   .status-text {
    //     color: #2b85e4;
    //   }
    // }

    &.completed {
      .status-node {
        border-color: #67c23a;
        background-color: #67c23a;
      }
      .status-text {
        color: #67c23a;
      }
      .connect-line {
        background-color: #67c23a;
      }
    }
  }
}
</style>
