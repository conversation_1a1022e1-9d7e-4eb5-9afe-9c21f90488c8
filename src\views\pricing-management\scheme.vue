<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        :fields="columns"
        :form="queryForm"
        inline
        clear
        :span="12"
        confirmButtonText="搜索"
        cancelButtonText="重置"
        @confirm="handleSearch"
        @cancel="handleCancel"
      ></pl-form>
      <!-- 新增按钮区域 -->
      <div>
        <pl-button
          type="primary"
          @click="handleAdd"
          v-has="'menu_calculate_list:btn_plan_add'"
        >
          新增方案
        </pl-button>
      </div>
      <!-- 表格区域 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @row-click="handleRowClick"
        >
          <!-- 操作列插槽 -->
          <template #operate="{ scope }">
            <pl-button
              type="primary"
              link
              @click="handleEdit(scope)"
              v-has="'menu_calculate_list:btn_plan_edit'"
            >
              编辑
            </pl-button>
            <pl-button
              type="danger"
              link
              @click="handleDelete(scope, 'pricingPlanId')"
              v-has="'menu_calculate_list:btn_plan_delete'"
            >
              删除
            </pl-button>
          </template>
        </pl-table>
      </div>

      <!-- 分页组件 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 新增方案 start -->
    <drawerFrom
      ref="drawerFromRef"
      v-model="drawerVisible"
      :disabled="formDisabled"
      :title="drawerTitle"
      :fields="drawerFields"
      :form="fromData"
      @submit="DrawerSubmit($event)"
    />
    <!-- 新增方案 end -->
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { getCookie } from "@/utils/cookie";
const columns = ref([
  {
    label: "方案名称",
    type: "input",
    prop: "pricingPlanName",
  },
]);
const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 80,
  },
  {
    label: "方案名称",
    prop: "pricingPlanName",
    sortable: "custom",
  },
  {
    label: "方案简述",
    prop: "remark",
    sortable: "custom",
  },
  {
    label: "创建时间",
    prop: "createTime",
    sortable: "custom",
  },
  {
    label: "操作",
    template: "operate",
    setting: true,
  },
]);
const queryForm = ref({});

const emit = defineEmits(["click"]);
const handleRowClick = (row) => {
  emit("click", row);
};

const drawerFields = ref([
  {
    label: "方案名称",
    prop: "pricingPlanName",
    type: "input",
    rules: [{ required: true, message: "请输入方案名称", trigger: "blur" }],
  },
  {
    label: "方案简述",
    prop: "remark",
    type: "textarea",
  },
]);

const {
  tableData,
  dataTotal,
  tabLoading,
  fromData,
  formDisabled,
  drawerVisible,
  drawerTitle,
  sizeChange,
  currentChange,
  handleAdd,
  handleDelete,
  handleEdit,
  handleDrawerSubmit,
  handleSearch,
  handleCancel,
} = useTable({
  queryForm,
  list: "/ticket/pricingPlan/pagePricingPlan",
  add: "/ticket/pricingPlan/savaPricingPlan",
  delete: "/ticket/pricingPlan/deletePricingPlan/",
  edit: "/ticket/pricingPlan/updatePricingPlan",
  del: {
    message: "确定删除该方案吗？",
  },
});

const DrawerSubmit = (data) => {
  handleDrawerSubmit({
    ...data,
    orgId: JSON.parse(getCookie("userInfo")).orgId,
  }).then(() => {
    handleCancel();
  });
};
</script>

<style lang="scss" scoped>
.baseFee-box {
  display: flex;
  width: 100%;
  position: relative;
  .unit {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
  .select {
    width: 200px;
    margin-right: 10px;
  }
  .fee-input {
    flex: 1;
  }
}
</style>
