<template>
  <div>
    <!-- 乘车人信息循环 start -->
    <div
      v-for="(user, index) in form.orderUserVOList"
      :key="index"
      class="passenger-info"
    >
      <pl-form
        :fields="passengerFields"
        :isButtonShow="false"
        class="chengcheren"
      >
        <template #Passenger>
          <pl-input
            v-model="user.userName"
            placeholder="请输入乘车人"
            disabled
          ></pl-input>
        </template>
        <template #IdCard>
          <pl-input
            v-model="user.idCard"
            placeholder="请输入身份证号"
            disabled
          ></pl-input>
        </template>
      </pl-form>
    </div>
    <!-- 乘车人信息循环 end -->

    <!-- 订单基本信息 -->
    <pl-form :fields="orderFields" :form="form" :isButtonShow="false">
    </pl-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getTravelLineSchedule } from "@/api/travel";
const form = ref({
  orderUserVOList: [],
});

//班次
const bcList = ref([]);
onMounted(() => {
  getTravelLineSchedule({
    travelLineId: props.detailData.travelLineId,
    orderId: props.detailData.orderId,
    schedulingDate: props.detailData.orderDate,
  }).then((res) => {
    bcList.value = res.data;
  });
});

const props = defineProps({
  detailData: {
    type: Object,
    default: () => {},
  },
  popTitle: {
    type: String,
    default: "",
  },
});

form.value = props.detailData;
const disabledForm = ref(false);

if (props.popTitle === "编辑") {
  disabledForm.value = false;
} else {
  disabledForm.value = true;
}

const passengerFields = ref([
  {
    label: "乘车人",
    prop: "userName",
    type: "input",
    template: "Passenger",
    disabled: disabledForm.value,
  },
  {
    label: "身份证号",
    prop: "idCard",
    type: "input",
    template: "IdCard",
    disabled: disabledForm.value,
  },
]);

const orderFields = ref([
  {
    label: "联系电话",
    prop: "phone",
    type: "input",
    disabled: true,
  },
  {
    label: "乘车日期",
    prop: "orderDate",
    format: "YYYY-MM-DD",
    type: "date",
    disabled: disabledForm.value,
  },
  {
    label: "乘车时间",
    prop: "orderTime",
    type: "date",
    format: "HH:mm",
    disabled: disabledForm.value,
  },
  {
    label: "起点",
    prop: "startStopName",
    type: "input",
    disabled: disabledForm.value,
  },
  {
    label: "终点",
    prop: "endStopName",
    type: "input",
    disabled: disabledForm.value,
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
    disabled: disabledForm.value,
  },
  {
    label: "线路名称",
    prop: "travelLineName",
    type: "input",
    disabled: disabledForm.value,
  },
  {
    label: "班次",
    prop: "schedulingItemId",
    type: "select",
    options: bcList,
    labelKey: "schedulingNo",
    valueKey: "schedulingItemId",
  },
  {
    label: "派车备注",
    prop: "orderRemark",
    type: "textarea",
  },
]);

const getFormData = () => {
  return form.value;
};

defineExpose({
  getFormData,
});
</script>

<style lang="scss" scoped>
.chengcheren {
  margin-bottom: 0 !important;
}
.passenger-info {
  .passenger-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #303133;
  }
}
.tenant-Scrollbar {
  padding-bottom: 60px;
}
</style>
