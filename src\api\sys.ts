import request from "./request";

export const SYS = {
  // 查询订单信息
  getBusinessTypeList() {
    return request.get(`/businessType/getBusinessTypeList`);
  },
  /**
   * 定制-预览追加驳载班次
   * @param params
   * @returns
   */
  appendBargeSchedulingItem(params: any) {
    return request.post("/scheduling/appendBargeSchedulingItem", params);
  },
  /**
   * 根据业务编码获取业务详情
   */
  getBusinessTypeDetail(businessType: any) {
    return request.get(`/businessType/getBusinessTypeDetail/${businessType}`);
  },
  /**
   * 根据排班id与驳载线路id获取驳载记录(只需要传schedulingId和schedulingItemId)
   */
  getSchedulingItemBarge(params: any) {
    return request.post(`/scheduling/template/getSchedulingItemBarge`, params);
  },
  /**
   * 定制-查询对应班次的车辆实时定位数据
   */
  getVehiclesBySchedulingItem(schedulingItemId: any) {
    return request.get(
      `/scheduling/getVehiclesBySchedulingItem/${schedulingItemId}`
    );
  },
};
