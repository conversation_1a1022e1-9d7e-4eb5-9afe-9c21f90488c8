<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 新增租户按钮 -->
      <div>
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'menu_help_center_answers_list:btn_add'"
        >
          新增
        </pl-button>
      </div>
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, tableColumns)"
        >
          <template #summary="{ scope }">
            <span v-if="scope.row.summary.length > 25">
              {{ scope.row.summary.slice(0, 25) }}...
            </span>
            <span v-else>{{ scope.row.summary }}</span>
          </template>
          <template #status="{ scope }">
            <span v-if="scope.row.status == 1" class="success-color">启用</span>
            <span v-else class="error-color">停用</span>
          </template>
          <template #operation="{ scope }">
            <pl-button
              link
              type="primary"
              @click="clickEdit(scope)"
              v-has="'menu_help_center_answers_list:btn_edit'"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope, 'guideId')"
              v-has="'menu_help_center_answers_list:btn_del'"
              >删除</pl-button
            >
          </template>
        </pl-table>
        <!-- 分页 -->
      </div>
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 新建问答 start -->
    <pl-drawer v-model="drawerVisible" :title="drawerTitle">
      <add-answer-popup
        v-if="drawerVisible"
        @close="drawerVisible = false"
        :categoryList="categoryList"
        :appList="appList"
        :labelList="labelList"
        :from="fromData"
        @submit="handleDrawerSubmit"
      />
    </pl-drawer>
    <!-- 新建问答 end -->
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import AddAnswerPopup from "./add-answer-popup.vue";
import { getQaCategoryList } from "@/api/helpCenter";
import { getAppList, getQaDetail, getLabelList } from "@/api";

const categoryList = ref([]);
onMounted(() => {
  getQaCategoryList().then((res) => {
    console.log("分类列表", res);
    categoryList.value = res.data;
  });
});
const appList = ref([]);
getAppList().then((res) => {
  appList.value = res.data;
});
const labelList = ref([]);
getLabelList().then((res) => {
  labelList.value = res.data;
});

import { useTable } from "@/hooks/usetTable";
const queryForm = ref({});
const {
  fromData,
  tableData,
  drawerTitle,
  drawerVisible,
  dataTotal,
  tabLoading,
  sizeChange,
  currentChange,
  handleCancel,
  handleSearch,
  handleDelete,
  handleEdit,
  handleAdd,
  handleSort,
  handleDrawerSubmit,
  current,
} = useTable({
  queryForm,
  list: "/qa/pageQa",
  delete: "/qa/deleteQa/",
  add: "/qa/saveQa",
  edit: "/qa/updateQa",
  del: {
    message: "删除后无法恢复，是否确定删除?",
    type: "message",
  },
});

// 查看问题详情
const clickEdit = (scope) => {
  getQaDetail({
    guideId: scope.row.guideId,
  }).then((res) => {
    handleEdit(res.data);
  });
};

const formColumns = ref([
  {
    label: "所属应用",
    type: "select",
    prop: "appCodeList",
    options: appList,
    valueKey: "appCode",
    labelKey: "appName",
    multiple: true,
  },
  {
    label: "所属类别",
    type: "tree-select-pro",
    options: categoryList,
    prop: "qaCategoryId",
    cascaderProps: {
      label: "categoryName",
      value: "qaCategoryId",
      children: "children",
    },
    nodeKey: "qaCategoryId",
    // multiple: false,
  },
  {
    label: "状态",
    type: "select",
    prop: "status",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
  {
    label: "关键字",
    type: "input",
    prop: "keyword",
  },
]);

const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: "60",
  },
  {
    label: "标题",
    prop: "name",
    sortable: "custom",
  },
  {
    label: "摘要",
    prop: "summary",
    sortable: "custom",
    width: 500,
    template: "summary",
  },
  {
    label: "所属应用",
    prop: "appName",
    sortable: "custom",
    sortProp: "appId",
  },
  {
    label: "所属类别",
    prop: "qaCategoryName",
    sortProp: "qaCategoryId",
    sortable: "custom",
  },
  // {
  //   label: "创建人",
  //   prop: "createByName",
  //   sortable: "custom",
  //   sortProp: "createBy",
  // },
  {
    label: "状态",
    prop: "status",
    sortable: "custom",
    template: "status",
  },
  {
    label: "创建时间",
    prop: "createTime",
    sortable: "custom",
  },
  {
    label: "操作",
    template: "operation",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped></style>
