import { ref } from "vue";

/**
 * 实收记录配置列表
 */
export const actualColumns = ref([
  { label: "实收单号", prop: "receiptNo", minWidth: 130 },
  { label: "订单号", prop: "orderNo", minWidth: 150 },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    minWidth: 100,
  },
  {
    label: "实收金额(元)",
    prop: "receiptAmount",
    minWidth: 130,
    price: true,
    align: "center",
  },
  {
    label: "未核销金额(元)",
    prop: "unverifiedAmount",
    minWidth: 130,
    price: true,
    align: "center",
  },

  { label: "付款方名称", prop: "payerName", minWidth: 100 },
  {
    label: "操作",
    fixed: "right",
    template: "operate",
    align: "center",
    width: 80,
  },
]);

// 应收记录
export const arColumns = ref([
  {
    label: "应收单号",
    prop: "receivableNo",
    template: "receivableNo",
    minWidth: 120,
  },
  {
    label: "订单号",
    prop: "orderNo",
    template: "orderNo",
    minWidth: 130,
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
    sortProp: "orderType",
    minWidth: 120,
  },
  { label: "付款方名称", prop: "payerName", minWidth: 120 },
  {
    label: "应收金额",
    prop: "receivableAmount",
    minWidth: 100,
    price: true,
    align: "center",
  },
  {
    label: "已核销金额",
    prop: "verifiedAmount",
    minWidth: 110,
    price: true,
    align: "center",
  },
  {
    label: "未核销金额",
    prop: "unverifiedAmount",
    minWidth: 110,
    price: true,
    align: "center",
  },
  {
    label: "核销金额",
    template: "amount",
    minWidth: 150,
  },
  {
    label: "到期日期",
    prop: "expirationTime",
    sortable: "custom",
    minWidth: 160,
  },
  {
    label: "核销状态",
    prop: "verificationStatusName",
    sortProp: "verificationStatus",
    minWidth: 110,
  },
  {
    label: "核销时间",
    prop: "verificationTime",
    minWidth: 110,
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    sortProp: "businessType",
    minWidth: 110,
  },
  { label: "关联合同", prop: "covenantNo", minWidth: 160 },
  { label: "负责销售", prop: "covenantNo", minWidth: 110 },
  { label: "账单备注", prop: "covenantNo", minWidth: 120 },
  { label: "创建时间", prop: "covenantNo", minWidth: 110 },
  { label: "更新时间", prop: "covenantNo", minWidth: 110 },
  {
    label: "操作",
    fixed: "right",
    template: "operate",
    align: "center",
    width: 80,
  },
]);
