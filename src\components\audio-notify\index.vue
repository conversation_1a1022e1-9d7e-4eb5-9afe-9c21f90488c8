<template>
  <div class="audio-control">
    <pl-switch
      v-model="enabled"
      active-text="关闭新订单监听"
      inactive-text="开启新订单监听"
      @change="handleChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { getOrderList } from "@/api/travel";
import mp3 from "@/assets/order-tips.mp3";
const props = defineProps({
  title: {
    type: String,
    default: "今日订单",
  },
  pollingInterval: {
    type: Number,
    default: 30000, // 默认30秒
  },
});

const emit = defineEmits(["update:total", "order-change"]);

// 状态变量
const enabled = ref(false);
const audio = ref(new Audio(mp3));
const isPlaying = ref(false);
const lastTotal = ref(0);
const newOrderCount = ref(0);
const pollingTimer = ref(null);

// 音频播放函数
const playAudio = async () => {
  if (!enabled.value || isPlaying.value) return;
  console.log("播放音频");
  try {
    isPlaying.value = true;
    await audio.value.play();

    audio.value.onended = () => {
      isPlaying.value = false;
    };
  } catch (err) {
    console.error("播放提示音失败:", err);
    isPlaying.value = false;
  }
};

// 获取订单数据
const fetchOrderData = async () => {
  try {
    const today = new Date().toISOString().split("T")[0];
    const res = await getOrderList({
      createTime: today,
      current: 1,
      limit: 10,
    });
    const { total } = res.data;

    // 检查是否有新订单
    if (lastTotal.value !== 0 && total > lastTotal.value) {
      newOrderCount.value += total - lastTotal.value;
      playAudio();
      emit("order-change", {
        newCount: total - lastTotal.value,
        total,
      });
    }

    // 更新最后一次订单总数
    lastTotal.value = total;
    emit("update:total", total);
  } catch (error) {
    console.error("获取订单数据失败:", error);
  }
};

// 开始轮询
const startPolling = () => {
  stopPolling(); // 确保之前的轮询已停止
  pollingTimer.value = setInterval(fetchOrderData, props.pollingInterval);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 开关状态改变处理
const handleChange = (value) => {
  if (value) {
    startPolling();
  } else {
    stopPolling();
  }
  clearNewOrderCount();
};

// 清除新订单计数
const clearNewOrderCount = () => {
  newOrderCount.value = 0;
};

// 组件挂载时初始化
onMounted(() => {
  fetchOrderData();
});

// 组件卸载时清理
onUnmounted(() => {
  stopPolling();
  audio.value = null;
});

// 对外暴露方法
defineExpose({
  playAudio,
  clearNewOrderCount,
  fetchOrderData,
});
</script>

<style scoped lang="scss">
.audio-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;

  .title-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;

    .new-order-badge {
      background-color: #f56c6c;
      color: white;
      border-radius: 10px;
      padding: 0 6px;
      height: 20px;
      min-width: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
    }
  }
}
</style>
