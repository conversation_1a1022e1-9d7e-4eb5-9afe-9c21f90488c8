<!-- 订单列表右侧内容 -->
<template>
  <!-- 卡片组件开始 -->
  <div class="card-flex">
    <div
      class="placehorder-card"
      @click="handlePlacehorder"
      v-if="isPlacehorder"
    ></div>

    <!-- 查询表单开始 -->
    <pl-form
      confirmButtonText="搜索"
      cancelButtonText="重置"
      :fields="formColumns"
      :form="queryForm"
      inline
      :span="8"
      formType="1"
      clear
      @confirm="handleSearch"
      @cancel="handleCancel"
    >
    </pl-form>
    <!-- 查询表单结束 -->

    <!-- 表格展示区域开始 -->
    <div class="card-table">
      <pl-table
        :columns="tableColumns"
        :data="tableData"
        class="table"
        v-loading="tabLoading"
        @sort-change="handleSort"
      >
        <template #ticketNo="{ scope }">
          <div class="order-tmp">
            <div class="order-no" :title="scope.row.ticketNo">
              {{ scope.row.ticketNo }}
            </div>
            <div class="copy" @click="copy(scope.row.ticketNo)">
              <pl-icon name="pl-icon-baobiao4" class="icon"></pl-icon>
            </div>
          </div>
        </template>
        <template #operation="{ scope }">
          <pl-button type="primary" link @click="handleEdit(scope.row)"
            >编辑</pl-button
          >
          <pl-button
            link
            type="danger"
            @click="handleDelete(scope.row, '设置成需要删除的id名字')"
            >删除</pl-button
          >
        </template>
      </pl-table>
    </div>
    <!-- 表格展示区域结束 -->

    <!-- 分页组件开始 -->
    <pl-pagination
      :currentPage="current"
      :total="dataTotal"
      @size-change="sizeChange"
      @current-change="currentChange"
    ></pl-pagination>
    <!-- 分页组件结束 -->

    <!-- 新增/编辑弹窗 -->
    <drawerFrom
      :fields="drawerFields"
      v-model="drawerVisible"
      :title="drawerTitle"
      :form="fromData"
      :disabled="formDisabled"
      @submit="handleDrawerSubmit(event)"
    >
    </drawerFrom>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { getDepotList } from "@/api";
import { plMessage } from "pls-common";
const props = defineProps({
  // 接收父组件传递的参数
  orderData: {
    type: Object,
    default: () => {},
  },
});

const stopList = ref([]);
const isPlacehorder = ref(true);
watch(
  () => props.orderData,
  (e) => {
    fixedField.value.orderNo = e.orderNo;
    isPlacehorder.value = false;
    loadData();
  },
  { deep: true }
);
onMounted(() => {
  //组件加载完成后执行事件
  getDepotList({
    stopMode: "DZ",
  }).then((res) => {
    if (res.code == 200) {
      stopList.value = res.data;
    }
  });
});

/**
 * 占位卡片点击事件
 */
const handlePlacehorder = () => {
  plMessage("请先选择左侧订单", "warning");
};

// 查询表单的数据
const queryForm = ref({});
const fixedField = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  loadData,
  handleEdit,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/ticket/pageTicket",
  noLoad: true,
  fixedField: fixedField.value,
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "起点",
    prop: "startStopId",
    type: "select",
    options: stopList,
    placeholder: "请选择起点",
    valueKey: "stopId",
    labelKey: "stopName",
  },
  {
    label: "终点",
    prop: "endStopId",
    type: "select",
    options: stopList,
    placeholder: "请选择终点",
    valueKey: "stopId",
    labelKey: "stopName",
  },
  {
    label: "用户",
    prop: "passName",
    type: "input",
    placeholder: "请输入用户名称",
  },
  {
    label: "联系方式",
    prop: "passPhone",
    type: "input",
    placeholder: "请输入联系方式",
  },
]);

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "名称",
    prop: "name",
    type: "input",
    placeholder: "请输入名称",
  },
]);

/**
 * 处理抽屉提交事件的方法
 * @param {Event} e - 提交事件对象
 */
const handleDrawerSubmit = (e) => {
  // 输出提交事件对象到控制台，用于调试目的
  console.log(e);
};

// 表格列配置
const tableColumns = ref([
  {
    label: "票据号",
    prop: "ticketNo",
    sortable: "custom",
    minWidth: 180,
    template: "ticketNo",
  },
  { label: "座位号", prop: "seatNum", sortable: "custom", minWidth: 90 },
  { label: "起点", prop: "startStopName", minWidth: 240 },
  { label: "终点", prop: "endStopName", minWidth: 240 },
  {
    label: "用户",
    prop: "passName",
    sortable: "custom",
    fixed: "right",
    minWidth: 90,
  },
  {
    label: "联系方式",
    prop: "passPhone",
    sortable: "custom",
    minWidth: 120,
    fixed: "right",
  },
  {
    label: "票务状态",
    prop: "ticketStatusName",
    minWidth: 110,
    fixed: "right",
  },
]);

// 复制票据号
const copy = (ticketNo) => {
  navigator.clipboard.writeText(ticketNo);
  plMessage("复制成功", "success");
};
</script>

<style lang="scss" scoped>
.card-flex {
  position: relative;
}
.placehorder-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9;
}
.order-tmp {
  display: flex;
  .order-no {
    flex: 1;
  }
  .icon {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
.order-no {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-flex {
  padding: 15px 15px 15px 0px;
}
</style>
