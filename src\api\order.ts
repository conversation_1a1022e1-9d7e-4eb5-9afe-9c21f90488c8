import request from "./request";

export const OD = {
  // 查询订单信息
  getOrderList(orderId: any) {
    return request.get(`/order/orderDetail/${orderId}`);
  },
  closeOrder(orderNo: any) {
    return request.post(`/order/closeLogicOrder/${orderNo}`);
  },
  closeOrderNew(orderNo: any) {
    return request.get(`/order/cancelOrder/${orderNo}`);
  },
  // 退款改签审批审批详情
  orderReviewDetail(param: any) {
    return request.get(`/order/review/orderReviewDetail/${param}`);
  },
  // 审批
  orderReview(param: any) {
    return request.post(`/order/review/review`, param);
  },
  // 获取订单申请人与审核人用户列表
  orderApplyUserList() {
    return request.get("/order/review/orderApplyUserList");
  },
  /**
   * 逻辑下单
   */
  logicOrder(params: any) {
    return request.post(`/order/logicOrder`, params);
  },
  // 确认支付订单
  confirmPayTicketOrder(params: any) {
    return request.post(`/order/confirmPayOrder`, params);
  },
};
