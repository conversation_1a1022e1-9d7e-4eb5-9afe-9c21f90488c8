<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        formType="1"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div>
        <pl-button
          type="primary"
          @click="unifiedTicketVisible = true"
          v-has="'menu_billmanagement_list:btn_sell'"
        >
          售票
        </pl-button>
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <!-- 订单状态 -->
          <template #orderStatus="{ scope }">
            <div
              :class="
                orderStatusList.find(
                  (item) => item.value === scope.row.orderStatus
                )?.value
              "
              v-if="scope.row.orderStatus"
            >
              {{
                orderStatusList.find(
                  (item) => item.value === scope.row.orderStatus
                )?.label
              }}
            </div>
            <div class="empty" v-else>暂无</div>
          </template>
          <!-- 票务状态 -->
          <template #ticketStatus="{ scope }">
            <div
              v-if="scope.row.ticketStatus"
              :class="
                ticketStatusList.find(
                  (item) => item.value === scope.row.ticketStatus
                )?.value
              "
            >
              {{
                ticketStatusList.find(
                  (item) => item.value === scope.row.ticketStatus
                )?.label
              }}
            </div>
            <div class="empty" v-else>暂无</div>
          </template>
          <!-- 操作 -->
          <template #operation="{ scope }">
            <!-- <pl-button
              type="primary"
              link
              @click="singleTicketVisible = true"
              v-if="scope.row.ticketCheckStatus == 0"
              >售票</pl-button
            > -->
            <pl-button
              link
              @click="handleBillDetail(scope.row)"
              v-has="'menu_billmanagement_list:btn_billmanagement_view'"
              >详情</pl-button
            >
            <pl-button
              type="warning"
              link
              @click="handleCheckTicket(scope.row)"
              v-has="'menu_billmanagement_list:btn_billmanagement_check'"
              v-if="
                scope.row.ticketCheckStatus == 'DJP' &&
                (scope.row.ticketStatus == 'YCP' ||
                  scope.row.ticketStatus == 'YZF')
              "
            >
              检票
            </pl-button>
            <pl-button
              type="danger"
              link
              @click="changeTicket(scope.row)"
              v-has="'menu_billmanagement_list:btn_billmanagement_rebook'"
              v-if="
                (scope.row.ticketCheckStatus == 'DJP' ||
                  scope.row.ticketCheckStatus == 'JPYC') &&
                (scope.row.ticketStatus == 'YCP' ||
                  scope.row.ticketStatus == 'YZF')
              "
              >改签</pl-button
            >
            <pl-button
              type="info"
              link
              @click="handleCheckTicket(scope.row, true)"
              v-has="'menu_billmanagement_list:btn_billmanagement_back'"
              v-if="
                (scope.row.ticketCheckStatus == 'DJP' ||
                  scope.row.ticketCheckStatus == 'JPYC') &&
                (scope.row.ticketStatus == 'YCP' ||
                  scope.row.ticketStatus == 'YZF')
              "
              >退票</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 检票弹窗 -->
      <pl-dialog
        v-model="checkDialogVisible"
        :title="isBack ? '退票' : '检票'"
        width="400px"
        align-center
        append-to-body
        :footerShow="false"
      >
        <template #content>
          <div class="tips">请仔细核对乘客信息，确定该乘客信息无误！</div>
          <pl-form :fields="refundFormColumns" :form="checkForm"> </pl-form>
          <div class="check-btn">
            <pl-button
              :type="isBack ? 'danger' : ''"
              @click="checkConfirm(false)"
              :loading="checkUnLoad"
            >
              {{ isBack ? "确认退票" : "信息不符" }}
            </pl-button>
            <pl-button
              v-if="!isBack"
              type="primary"
              @click="checkConfirm(true)"
              :loading="checkLoad"
            >
              确认检票
            </pl-button>
          </div>
        </template>
      </pl-dialog>

      <!-- 票据详情 -->
      <pl-drawer v-model="billDetailVisible" title="票据详情">
        <div class="bill-detail-container">
          <pl-form
            class="bill-form"
            :fields="billDetailFormColumns"
            :form="billDetailForm"
            inline
            :isButtonShow="false"
          >
          </pl-form>
          <div class="bill-circulation">
            <div class="title">票据流转</div>
            <div class="timeline">
              <div
                class="timeline-item"
                v-for="i in ticketFlow"
                :key="i.ticketStatus"
              >
                <div class="node">
                  <div class="circle" :class="{ active: i.isActive }"></div>
                </div>
                <div class="content" :class="{ active: i.isActive }">
                  <span class="status">
                    {{
                      ticketStatusList.find(
                        (item) => item.value == i.ticketStatus
                      )?.label
                    }}
                  </span>
                  <span class="time" v-if="i.optTime">{{ i.optTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </pl-drawer>

      <!-- 统一售票 -->
      <pl-drawer v-model="unifiedTicketVisible" title="售票">
        <unifiedTicketSales
          @close="unifiedClose"
          :visible="unifiedTicketVisible"
        ></unifiedTicketSales>
      </pl-drawer>

      <!-- 改签 -->
      <pl-drawer v-model="changeTicketVisible" title="改签">
        <rebook
          :ticketNo="ticketNo"
          :visible="changeTicketVisible"
          @close="changeTicketVisible = false"
        ></rebook>
      </pl-drawer>

      <!-- 单独操作售票 -->
      <pl-drawer v-model="singleTicketVisible" title="售票">
        <ticketSales @close="singleTicketVisible = false"></ticketSales>
      </pl-drawer>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useTable } from "@/hooks/usetTable";
import unifiedTicketSales from "./module/unified-ticket-sales.vue";
import ticketSales from "./module/ticket-sales.vue";
import { tableColumns } from "./config";
import rebook from "./module/rebook.vue";
import { getDictionaryData } from "@/api/dict";
import {
  getSiteList,
  checkTicket,
  checkTicketInfo,
  refundTicket,
  getTicketInfo,
} from "@/api";
import { plMessage } from "pls-common";
const unifiedTicketVisible = ref(false); //统一售票弹窗控制
const changeTicketVisible = ref(false); //改签弹窗控制
const singleTicketVisible = ref(false); //单独操作售票

onMounted(() => {
  getDict();
  getSite();
});

// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  tableData,
  tabLoading,
  handleSort,
  handleCancel,
  loadData,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  // noLoad: true,
  list: "/ticket/pageTicket",
  queryForm,
});

// 获取字典数据
const orderStatusList = ref([]); // 订单状态
const ticketStatusList = ref([]); // 票务状态
const refundType = ref([]); // 退票方式
const getDict = async () => {
  const res = await getDictionaryData({
    dictTypeCode: "order_status",
  });
  orderStatusList.value = res;
  const res2 = await getDictionaryData({
    dictTypeCode: "ticket_status",
  });
  ticketStatusList.value = res2;
  const res3 = await getDictionaryData({
    dictTypeCode: "refund model",
  });
  refundType.value = res3;
};
// 获取站点列表
const siteList = ref([]);
const getSite = async () => {
  const { data: res } = await getSiteList({ stopMode: "DZ" });
  siteList.value = res;
};

// 检票form
const refundFormColumns = ref([
  {
    label: "乘车人",
    prop: "passName",
    type: "input",
    disabled: true,
  },
  {
    label: "联系电话",
    prop: "passPhone",
    type: "input",
    disabled: true,
  },
  {
    label: "身份证号",
    prop: "passId",
    type: "input",
    disabled: true,
  },
]);

// 退票
const affirmRefundTicket = async () => {
  checkUnLoad.value = true;
  try {
    const params = {
      orderPlatform: "SPCK",
      businessType: curBusinessType.value,
      orgId: JSON.parse(localStorage.getItem("userInfo")).orgId,
      refundDescribe: "",
      refundModel: "YLTH",
      ticketNos: [checkForm.value.ticketNo],
    };
    const res = await refundTicket(params);
    if (res.code == 200) {
      plMessage("退票成功", "success");
    }
    checkDialogVisible.value = false;
    loadData();
    console.log("退票确认", params);
  } catch (error) {
    console.log("err", error);
  } finally {
    checkUnLoad.value = false;
  }
};

// 检票
const checkDialogVisible = ref(false);
const checkForm = ref({});
const isBack = ref(false); // 是否为退票
const curBusinessType = ref(); // 已选的业务类型
const handleCheckTicket = async (row, ident = false) => {
  curBusinessType.value = row.businessType;
  isBack.value = ident;
  // 确认检票信息
  const { data: res } = await checkTicketInfo({ ticketNo: row.ticketNo });
  checkDialogVisible.value = true;
  checkForm.value = res;
};
// 检票操作
const checkLoad = ref(false);
const checkUnLoad = ref(false);
const checkConfirm = async (isPass) => {
  if (isPass) {
    checkLoad.value = true;
  } else {
    if (isBack.value) {
      affirmRefundTicket();
      return;
    } else {
      checkUnLoad.value = true;
    }
  }
  try {
    const params = {
      isPass,
      ticketNo: checkForm.value.ticketNo,
    };
    const res = await checkTicket(params);
    plMessage(res.message, "success");
    checkDialogVisible.value = false;
    loadData();
  } catch (error) {
    console.log("error", error);
  } finally {
    checkLoad.value = false;
    checkUnLoad.value = false;
  }
};

// 票据详情
const billDetailVisible = ref(false);
const billDetailForm = ref({});
const billDetailFormColumns = ref([
  {
    label: "票号",
    prop: "ticketNo",
    type: "input",
    disabled: true,
  },
  {
    label: "乘车人",
    prop: "passName",
    type: "input",
    disabled: true,
  },
  {
    label: "联系电话",
    prop: "passPhone",
    type: "input",
    disabled: true,
  },
  {
    label: "发车日期",
    prop: "departureTime",
    type: "date",
    format: "YYYY-MM-DD",
    disabled: true,
    placeholder: "暂无",
  },
  {
    label: "发车时间",
    prop: "time",
    type: "input",
    disabled: true,
  },
  {
    label: "座位号",
    prop: "seatNum",
    type: "input",
    disabled: true,
  },
  {
    label: "线路名称",
    prop: "lineName",
    type: "input",
    disabled: true,
  },
  {
    label: "起点",
    prop: "startStopName",
    type: "input",
    disabled: true,
  },
  {
    label: "终点",
    prop: "endStopName",
    type: "input",
    disabled: true,
  },
  {
    label: "票价（元）",
    prop: "price",
    type: "select",
    placeholder: "暂无",
    disabled: true,
  },
  {
    label: "购票时间",
    prop: "createTime",
    type: "date",
    format: "YYYY-MM-DD HH:mm:ss",
    disabled: true,
  },
  {
    label: "状态",
    prop: "orderStatusName",
    type: "input",
    disabled: true,
  },
  {
    label: "身份证号",
    prop: "passId",
    type: "input",
    disabled: true,
  },
]);
const ticketFlow = ref([]); // 票据流转
const handleBillDetail = async (row) => {
  try {
    const { data: res } = await getTicketInfo(row.ticketNo);
    billDetailForm.value = res;
    // 发车时间格式调整
    billDetailForm.value.time = billDetailForm.value.departureTime
      ? billDetailForm.value.departureTime.split(" ")[1]
      : "暂无";

    // 票据流转流程赋值
    if (
      billDetailForm.value.ticketStatusFlowList &&
      billDetailForm.value.ticketStatusFlowList.length > 0
    ) {
      // 如果有流转记录，取最后一条记录的状态
      const lastFlow =
        billDetailForm.value.ticketStatusFlowList[
          billDetailForm.value.ticketStatusFlowList.length - 1
        ];
      const currentStatus = lastFlow.ticketStatus;

      // 找到当前状态在orderStatusList中的索引
      const currentIndex = ticketStatusList.value.findIndex(
        (item) => item.value === currentStatus
      );

      // 构建流转数组，显示所有状态，并标记激活状态
      ticketFlow.value = ticketStatusList.value.map((item, index) => {
        // 查找该状态在流转记录中的时间
        const flowRecord = billDetailForm.value.ticketStatusFlowList.find(
          (flow) => flow.ticketStatus === item.value
        );

        return {
          ticketStatus: item.value,
          optTime: flowRecord ? flowRecord.optTime || "" : "",
          isActive: index <= currentIndex, // 从第一步到当前步骤都标记为激活
        };
      });
      console.log('===',ticketFlow.value);
      
    } else {
      // 如果没有流转记录，所有状态都显示为默认
      ticketFlow.value = ticketStatusList.value.map((item) => ({
        ticketStatus: item.value,
        optTime: "",
        isActive: false,
      }));
    }
  } catch (error) {
    console.log("err", error);
  }
  billDetailVisible.value = true;
};

// 改签
const ticketNo = ref(""); // 改签票号
const changeTicket = (row) => {
  ticketNo.value = row.ticketNo;
  changeTicketVisible.value = true;
};

// 统一售票窗口关闭
const unifiedClose = () => {
  unifiedTicketVisible.value = false;
  loadData();
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "票号",
    prop: "ticketNo",
    type: "input",
  },
  {
    label: "乘车人",
    prop: "passName",
    type: "input",
  },
  {
    label: "联系方式",
    prop: "passPhone",
    type: "input",
  },
  {
    label: "发车时间",
    prop: "departureTime",
    type: "datetime",
    format: "YYYY-MM-DD HH:mm:ss",
  },
  {
    label: "起点",
    prop: "startStopId",
    labelKey: "stopName",
    valueKey: "stopId",
    type: "select",
    options: siteList,
    filterable: true,
    clearable: true,
  },
  {
    label: "终点",
    prop: "endStopId",
    labelKey: "stopName",
    valueKey: "stopId",
    type: "select",
    options: siteList,
    filterable: true,
    clearable: true,
  },
  {
    label: "身份证号",
    prop: "passId",
    type: "input",
    placeholder: "请输入身份证号后六位",
  },
]);
</script>

<style lang="scss" scoped>
.bill-detail-container {
  display: flex;
}

.bill-circulation {
  min-width: 200px;
  background-color: #f9f9f9;
  padding: 15px;
  margin-left: 15px;
  border-radius: 8px;
  .bill-form {
    flex: 1;
  }
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
  }

  .timeline {
    position: relative;
    padding-left: 30px;

    &::before {
      content: "";
      position: absolute;
      left: 15px;
      top: 0;
      bottom: 0;
      width: 1px;
      background-color: #dcdfe6;
    }

    .timeline-item {
      position: relative;
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      .node {
        position: absolute;
        left: -30px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9f9f9;

        .circle {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #dcdfe6;

          &.active {
            background-color: #00ad77;
            box-shadow: 0 0 0 2px rgba(0, 173, 119, 0.2);
          }
        }
      }

      .content {
        min-height: 30px;
        display: flex;
        flex-direction: column;
        .status {
          font-weight: 500;
          margin-right: 20px;
          width: 80px;
          color: #909399;
        }

        .time {
          color: #909399;
          font-size: 14px;
        }
        &.active {
          .status {
            color: #00ad77;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.tips {
  margin-bottom: 10px;
  color: #ff7070;
}
.check-btn {
  display: flex;
  justify-content: flex-end;
}

.empty {
  font-size: 13px;
  color: #999;
}

// 票务状态
.WZF {
  color: #999;
}
// 订单状态
.WZF {
  color: #999;
}
.YZF {
  color: #00ad77;
}
.TKZ {
  color: #1777ff;
}
.YTK {
  color: #ff7070;
}
.YWC {
  color: #999;
}
// 检票状态
.check-wait {
  color: #999;
}
.check-success {
  color: #00ad77;
}
.check-fail {
  color: #ff7070;
}
</style>
