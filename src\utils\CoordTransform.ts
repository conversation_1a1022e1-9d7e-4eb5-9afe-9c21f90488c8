/**
 * 提供了百度坐标（BD-09）、国测局坐标（火星坐标，GCJ-02）、和 WGS-84 坐标系之间的转换
 */

// 定义常量
// eslint-disable-next-line
const x_PI: number = (3.14159265358979324 * 3000.0) / 180.0;
// eslint-disable-next-line
const PI: number = 3.1415926535897932384626;
const a: number = 6378245.0;
// eslint-disable-next-line
const ee: number = 0.00669342162296594323;

// 定义坐标点类型
type Coordinate = [number, number];

/**
 * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02) 的转换
 * 即 百度 转 谷歌、高德
 */
export const bd09togcj02 = (bd_lng: number, bd_lat: number): Coordinate => {
  const x = bd_lng - 0.0065;
  const y = bd_lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
  const gg_lng = z * Math.cos(theta);
  const gg_lat = z * Math.sin(theta);
  return [gg_lng, gg_lat];
};

/**
 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换
 * 即 谷歌、高德 转 百度
 */
export const gcj02tobd09 = (lng: number, lat: number): Coordinate => {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
  const bd_lng = z * Math.cos(theta) + 0.0065;
  const bd_lat = z * Math.sin(theta) + 0.006;
  return [bd_lng, bd_lat];
};

/**
 * WGS-84 转 GCJ-02
 */
export const wgs84togcj02 = (lng: number, lat: number): Coordinate => {
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  }
  const dlat = transformlat(lng - 105.0, lat - 35.0);
  const dlng = transformlng(lng - 105.0, lat - 35.0);
  const radlat = (lat / 180.0) * PI;
  const magic = Math.sin(radlat);
  const sqrtmagic = Math.sqrt(1 - ee * magic * magic);
  const dlat2 = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
  const dlng2 = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);

  // 经度和纬度使用不同的偏移系数
  const lngFactor = 1; // 经度保持原样
  const latFactor = 2.95; // 纬度偏移减半，可以根据需要调整这个值

  return [lng + dlng2 * lngFactor, lat + dlat2 * latFactor];
};

/**
 * GCJ-02 转换为 WGS-84
 */
export const gcj02towgs84 = (lng: number, lat: number): Coordinate => {
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  }

  let wgsLng = lng - 0.01;
  let wgsLat = lat - 0.01;
  let tempLng, tempLat;

  for (let i = 0; i < 30; i++) {
    tempLng = wgsLng;
    tempLat = wgsLat;
    const result = wgs84togcj02(wgsLng, wgsLat);
    wgsLng += lng - result[0];
    wgsLat += lat - result[1];
    if (
      Math.abs(tempLng - wgsLng) < 0.000001 &&
      Math.abs(tempLat - wgsLat) < 0.000001
    ) {
      break;
    }
  }

  return [wgsLng, wgsLat];
};

const transformlat = (lng: number, lat: number): number => {
  let ret =
    -0.0 +
    2.0 * lng +
    3.0 * lat +
    0.2 * lat * lat +
    0.1 * lng * lat +
    0.2 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) /
    3.0;
  ret +=
    ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) *
      2.0) /
    3.0;
  return ret;
};

const transformlng = (lng: number, lat: number): number => {
  let ret =
    300.0 +
    lng +
    2.0 * lat +
    0.1 * lng * lng +
    0.1 * lng * lat +
    0.1 * Math.sqrt(Math.abs(lng));
  ret +=
    ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) /
    3.0;
  ret +=
    ((150.0 * Math.sin((lng / 12.0) * PI) +
      300.0 * Math.sin((lng / 30.0) * PI)) *
      2.0) /
    3.0;
  return ret;
};

/**
 * 判断是否在国内，不在国内则不做偏移
 */
const out_of_china = (lng: number, lat: number): boolean => {
  // 纬度 3.86~53.55, 经度 73.66~135.05
  return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
};
