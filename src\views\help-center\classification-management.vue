<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        formType="1"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 新增租户按钮 -->
      <div>
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'menu_help_center_category_list:btn_add'"
        >
          新增
        </pl-button>
      </div>
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          row-key="id"
          @sort-change="handleSort"
        >
          <template #status="{ scope }">
            <span v-if="scope.row.status == 1" class="success-color">启用</span>
            <span v-else class="error-color">停用</span>
          </template>

          <template #operation="{ scope }">
            <pl-button
              link
              type="primary"
              @click="handleEdit(scope.row)"
              v-has="'menu_help_center_category_list:btn_edit'"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope, 'qaCategoryId')"
              v-has="'menu_help_center_category_list:btn_del'"
              >删除</pl-button
            >
          </template>
        </pl-table>
        <!-- 分页 -->
      </div>
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 新建问答 start -->
    <pl-drawer v-model="drawerVisible" :title="drawerTitle">
      <classification-popup
        v-if="drawerVisible"
        @close="drawerVisible = false"
        @submit="handleSubmit"
        :treeOptions="treeData"
        :fromData="fromData"
      />
    </pl-drawer>
    <!-- 新建问答 end -->
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import ClassificationPopup from "./classification-popup.vue";
import { useTable } from "@/hooks/usetTable";
const queryForm = ref({});
const handleSubmit = (data) => {
  handleDrawerSubmit(data);
};
const {
  tableData,
  drawerTitle,
  drawerVisible,
  dataTotal,
  tabLoading,
  fromData,
  treeData,
  handleSort,
  sizeChange,
  currentChange,
  handleCancel,
  handleSearch,
  handleDelete,
  handleAdd,
  handleEdit,
  handleDrawerSubmit,
  current,
} = useTable({
  queryForm,
  list: "/qaCategory/pageQaCategory",
  treeList: "/qaCategory/getQaCategoryTreeList",
  delete: "/qaCategory/deleteQaCategory/",
  add: "/qaCategory/saveQaCategory",
  edit: "/qaCategory/updateQaCategory",
  del: {
    message: "删除后无法恢复，是否确定删除?",
    type: "message",
  },
});

const formColumns = ref([
  {
    label: "类别名称",
    type: "tree-select-pro",
    prop: "categoryName",
    options: treeData,
    cascaderProps: {
      label: "categoryName",
      value: "categoryName",
      children: "children",
    },
    multiple: false,
  },
  {
    label: "类别编码",
    type: "input",
    prop: "categoryCode",
  },
  {
    label: "创建时间",
    prop: "startTimeAndEndTime",
    type: "datetimerange",
    format: "YYYY-MM-DD HH:mm:ss",
    onChange: (value) => {
      queryForm.value.startTime = value[0];
      queryForm.value.endTime = value[1];
    },
  },
  {
    label: "状态",
    type: "select",
    prop: "status",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
]);

const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: "60",
  },
  {
    label: "类别名称",
    prop: "categoryName",
    sortable: "custom",
  },
  {
    label: "类别编码",
    prop: "categoryCode",
    sortable: "custom",
  },

  // {
  //   label: "创建人",
  //   prop: "createByName",
  //   sortable: "custom",
  // },
  {
    label: "状态",
    prop: "status",
    template: "status",
    sortable: "custom",
  },
  {
    label: "创建日期",
    prop: "createTime",
    sortable: "custom",
  },
  {
    label: "操作",
    template: "operation",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped></style>
