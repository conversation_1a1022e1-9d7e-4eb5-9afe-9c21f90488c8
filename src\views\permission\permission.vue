<template>
  <div class="h100">
    <pl-row class="h100">
      <pl-col :span="4" class="h100">
        <pl-card>
          <institution
            @change="institutionChange"
            @error="institutionError"
            @parentChange="institutionParentChange"
          ></institution>
        </pl-card>
      </pl-col>

      <pl-col :span="20" class="h100 pl20">
        <pl-card>
          <pl-noData v-if="nodata"></pl-noData>
          <perSetting :params="params" v-else-if="isBlock"></perSetting>
        </pl-card>
      </pl-col>
    </pl-row>
  </div>
</template>

<script setup>
import institution from "@/components/institution/tree.vue";
import perSetting from "./per-setting.vue";
import { plMessage } from "pls-common";
import { ref } from "vue";
const nodata = ref(false);
const params = ref({});
const isBlock = ref(false);
const institutionChange = (val, item) => {
  nodata.value = false;
  params.value = item;
  isBlock.value = true;
};
const institutionError = () => {
  console.log(111);
  nodata.value = true;
};
const institutionParentChange = (item) => {
  if (item.sysRoleVOList) {
    params.value = item.sysRoleVOList[0];
    isBlock.value = true;
  } else {
    plMessage("该机构下没有角色", "warning");
    nodata.value = true;
    isBlock.value = false;
  }
};
</script>

<style lang="scss" scoped></style>
