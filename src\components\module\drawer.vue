<template>
  <div class="drawer-box-big" :class="modelValue ? 'drwaer-show' : ''">
    <pl-icon
      class="drawer-close"
      name="Close"
      :size="24"
      @click="drawerClose"
    ></pl-icon>
    <div class="drawer-header" v-if="title">
      <div class="title">{{ title }}</div>
    </div>
    <div class="content" :class="isPadding ? 'isPadding' : ''">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { plIcon } from "pls-common";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  isPadding: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:modelValue", "close", "openState"]);

const drawerClose = () => {
  emit("update:modelValue", false);
  emit("close");
};

const isOpen = ref(false);
watch(
  () => props.modelValue,
  (newVal) => {
    isOpen.value = newVal ? true : false;
    emit("openState", isOpen.value);
  }
);
</script>

<style scoped lang="scss">
.c-box {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
.content-scrollbar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.button-box {
  display: flex;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 10%;
  z-index: 2;
}
.drawer-header {
  border-bottom: 1px solid var(--el-border-color);
  padding: 15px;
  .title {
    font-weight: bold;
  }
}

.drawer-close {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
}

.drawer-box-big {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--el-color-white);
  border-radius: 8px;
  z-index: 9;
  transition: all 0.3s ease;
  transform: translateX(120%);
  display: flex;
  flex-direction: column;
  &.drwaer-show {
    transform: translateX(0);
  }
  .content {
    flex: 1;
    position: relative;
    overflow: hidden;
    &.isPadding {
      padding-top: 24px;
      padding-left: 24px;
      padding-right: 24px;
    }
  }
}
</style>
