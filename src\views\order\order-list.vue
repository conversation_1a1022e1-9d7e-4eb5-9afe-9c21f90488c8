<template>
  <!-- 卡片组件开始 -->
  <div class="card-flex">
    <!-- 查询表单开始 -->
    <pl-form
      confirmButtonText="搜索"
      cancelButtonText="重置"
      :fields="formColumns"
      :form="queryForm"
      inline
      :span="8"
      formType="1"
      clear
      @confirm="handleSearch"
      @cancel="handleCancel"
    >
    </pl-form>
    <!-- 查询表单结束 -->

    <!-- 表格展示区域开始 -->
    <div class="card-table">
      <pl-table
        :columns="tableColumns"
        :data="tableData"
        class="table"
        v-loading="tabLoading"
        @sort-change="handleSort"
        @row-click="handleRowClick"
      >
        <template #orderNo="{ scope }">
          <div class="order-tmp">
            <div class="order-no" :title="scope.row.orderNo">
              {{ scope.row.orderNo }}
            </div>
            <div class="copy" @click="copy(scope.row.orderNo)">
              <pl-icon name="pl-icon-baobiao4" class="icon"></pl-icon>
            </div>
          </div>
        </template>
        <template #operation="{ scope }">
          <pl-button
            type="primary"
            link
            @click.stop="getDetails(scope.row)"
            v-has="'menu_mocs_order_list:btn_detai'"
            >详情</pl-button
          >
          <pl-button
            link
            type="danger"
            v-if="
              scope.row.orderStatus == 'DZF' || scope.row.orderStatus == 'YZF'
            "
            v-has="'menu_mocs_order_list:btn_cancel'"
            @click.stop="handleCancelOrder(scope.row)"
            >取消订单</pl-button
          >
        </template>
      </pl-table>
    </div>
    <!-- 表格展示区域结束 -->

    <!-- 分页组件开始 -->
    <pl-pagination
      :currentPage="current"
      :total="dataTotal"
      @size-change="sizeChange"
      @current-change="currentChange"
    ></pl-pagination>
    <!-- 分页组件结束 -->

    <pl-drawer
      v-model="drawerVisible"
      title="详情"
      style="z-index: 99"
      class="order-pl-details-drawer"
    >
      <pl-scrollbar>
        <div class="order-details">
          <div class="order-info">
            <div class="drawer-w">
              <div class="title">订单信息</div>
              <pl-form
                :fields="orderColumns"
                :form="fromData.orderBaseVO"
                inline
                :isButtonShow="false"
                :disabled="true"
              ></pl-form>
            </div>
            <div class="drawer-w">
              <div class="title">乘车人信息</div>
              <template
                v-for="item in fromData.orderPassengerDTOList"
                :key="item.passId"
              >
                <pl-form
                  :fields="PassengerColumns"
                  :form="item"
                  inline
                  :isButtonShow="false"
                  :disabled="true"
                ></pl-form>
              </template>
            </div>
            <div class="drawer-w">
              <div class="title">线路信息</div>
              <pl-form
                :fields="lineColumns"
                :form="fromData.ticketLineInfoVO"
                inline
                :isButtonShow="false"
                :disabled="true"
              ></pl-form>
            </div>
            <div
              class="drawer-w"
              v-if="fromData?.orderConnectOrderVOList?.length"
            >
              <div class="title">关联订单</div>
              <pl-table
                :columns="orderTableColumns"
                :data="fromData.orderConnectOrderVOList"
              ></pl-table>
            </div>
            <div class="drawer-w mt20" v-if="fromData.orderBaseVO">
              <div class="title">支付信息</div>
              <pl-table
                :columns="payTableColumns"
                :data="[
                  {
                    ...fromData.orderPaymentInfoVO,
                    orderTypeName: fromData.orderBaseVO.orderTypeName,
                    orderNo: fromData.orderBaseVO.orderNo,
                  },
                ]"
              ></pl-table>
            </div>
          </div>
          <!-- 流程状态 -->
          <div class="drawer-w status-flow" v-if="flowData.length">
            <div class="title">状态流转</div>
            <pl-status-flow :data="flowData">
              <template #content="{ item }">
                <div class="status-flow-content">
                  <div
                    v-if="
                      item.orderStatus == 'DZF' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item" v-if="item.userName">
                      <span class="label">下单人：</span>{{ item.userName }}
                    </div>
                    <div class="child-item" v-if="item.userPhone">
                      <span class="label">手机号：</span>{{ item.userPhone }}
                    </div>
                    <div class="child-item">
                      <span class="label">下单时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'YZF' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">订单金额：</span
                      >{{ item.orderAmount }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'TKZ' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">退款金额：</span
                      >{{ item.orderAmount }}
                    </div>
                    <div class="child-item">
                      <span class="label">申请时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'YTK' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">退款金额：</span
                      >{{ item.orderAmount }}
                    </div>
                    <div class="child-item">
                      <span class="label">退款时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'YWC' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">完成时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'YQX' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">取消时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                  <div
                    v-else-if="
                      item.orderStatus == 'YGQ' && item.statusColor == 'green'
                    "
                  >
                    <div class="child-item">
                      <span class="label">补价/退款金额：</span
                      >{{ item.orderAmount }}
                    </div>
                    <div class="child-item">
                      <span class="label">改签时间：</span>{{ item.optTime }}
                    </div>
                  </div>
                </div>
              </template>
            </pl-status-flow>
          </div>
        </div>
      </pl-scrollbar>
    </pl-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useTable } from "@/hooks/usetTable";
import { plMessage, plMessageBox } from "pls-common";
import { OD } from "@/api/order";
import { SYS } from "@/api/sys";
import { getDictionaryData } from "@/api/dict";
const drawerVisible = ref(false);
const businessTypeList = ref([]);
const orderStatusList = ref([]); // 订单状态
const orderTypeList = ref([]); // 订单类型
const paymentWayList = ref([]); // 支付方式
const paymentStatusList = ref([]); // 支付状态
const orderPlatformList = ref([]); // 订单来源
onMounted(() => {
  SYS.getBusinessTypeList().then((res) => {
    if (res.code == 200) {
      businessTypeList.value = res.data;
    }
  });

  getDictionaryData({
    dictTypeCode: "order_status",
  }).then((res) => {
    orderStatusList.value = res;
  });

  getDictionaryData({
    dictTypeCode: "payment_way",
  }).then((res) => {
    paymentWayList.value = res;
  });
  getDictionaryData({
    dictTypeCode: "order_payment_status",
  }).then((res) => {
    paymentStatusList.value = res;
  });
  getDictionaryData({
    dictTypeCode: "order_platform",
  }).then((res) => {
    orderPlatformList.value = res;
  });
  getDictionaryData({
    dictTypeCode: "order_type",
  }).then((res) => {
    orderTypeList.value = res;
  });
});

/**
 * 取消订单
 */
const handleCancelOrder = (row) => {
  plMessageBox
    .confirm("确定要取消该订单吗？", "温馨提示", {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(() => {
      OD.closeOrderNew(row.orderNo).then((res) => {
        console.log(res);
        if (res.code == 200) {
          plMessage("取消成功", "success");
          loadData();
        }
      });
    })
    .catch(() => {});
};

const emit = defineEmits(["rowClick"]);

/**
 * 查询订单详情
 */
const flowData = ref([]); // 状态流转
const getDetails = (row) => {
  OD.getOrderList(row.orderNo).then((res) => {
    if (res.code == 200) {
      fromData.value = res.data;
      drawerVisible.value = true;
      // 获取后端返回的流转记录
      const flowList = res.data.orderStatusFlowVOList || [];
      // 获取所有主流程状态
      const allStatus = orderStatusList.value || [];
      // 找到当前最新状态在主流程中的索引
      let currentStatus = "";
      if (flowList.length > 0) {
        currentStatus = flowList[flowList.length - 1].orderStatus;
      }
      const currentIndex = allStatus.findIndex(
        (item) => item.value === currentStatus
      );
      // 构建完整的流程数据
      flowData.value = allStatus.map((item, idx) => {
        // 查找该状态在流转记录中的详细信息
        const flowRecord = flowList.find((f) => f.orderStatus === item.value);
        return {
          orderStatus: item.value,
          label: item.label || item.orderStatusName || "",
          iconStatus: idx <= currentIndex ? "success" : "wait",
          statusColor: idx <= currentIndex ? "green" : "#ccc",
          lineColor: idx < currentIndex ? "green" : "#ccc",
          userName: flowRecord ? flowRecord.userName : "",
          userPhone: flowRecord ? flowRecord.userPhone : "",
          orderAmount: flowRecord ? flowRecord.orderAmount : "",
          optTime: flowRecord ? flowRecord.optTime : "",
        };
      });
      console.log("flowData", flowData.value);
    }
  });
};

const orderColumns = ref([
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    type: "input",
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
    type: "input",
    placeholder: "",
  },
  {
    label: "订单来源",
    prop: "orderPlatformName",
    type: "input",
    placeholder: "",
  },
  {
    label: "订单状态",
    prop: "orderStatusName",
    type: "input",
    placeholder: "",
  },
  {
    label: "下单时间",
    prop: "orderTime",
    type: "input",
    placeholder: "",
  },
  {
    label: "下单人",
    prop: "userName",
    type: "input",
    placeholder: "",
  },
  {
    label: "手机号",
    prop: "userPhone",
    type: "input",
    placeholder: "",
  },
]);
const PassengerColumns = ref([
  {
    label: "乘车人",
    prop: "passName",
    type: "input",
    placeholder: "",
  },
  {
    label: "联系电话",
    prop: "passPhone",
    type: "input",
    placeholder: "",
  },
  {
    label: "身份号码",
    prop: "passId",
    type: "input",
    placeholder: "",
  },
]);
const lineColumns = ref([
  {
    label: "线路名称",
    prop: "lineName",
    type: "input",
    placeholder: "",
  },
  {
    label: "线路起点",
    prop: "lineStartStopName",
    type: "input",
    placeholder: "",
  },
  {
    label: "线路终点",
    prop: "lineEndStopName",
    type: "input",
    placeholder: "",
  },

  {
    label: "乘车时间",
    prop: "departureTime",
    type: "input",
    placeholder: "",
  },
  {
    label: "起点",
    prop: "startStopName",
    type: "input",
    placeholder: "",
  },
  {
    label: "终点",
    prop: "endStopName",
    type: "input",
  },
]);
const orderTableColumns = ref([
  {
    label: "订单号",
    prop: "orderNo",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
  },
  {
    label: "支付方式",
    prop: "paymentWayName",
  },
  {
    label: "支付时间",
    prop: "paymentTime",
  },
  {
    label: "支付状态",
    prop: "paymentStatusName",
  },
  {
    label: "订单金额",
    prop: "paymentAmount",
    price: true,
    align: "center",
  },
  {
    label: "订单来源",
    prop: "orderPlatformName",
  },
  {
    label: "订单状态",
    prop: "orderStatusName",
  },
  {
    label: "下单时间",
    prop: "orderTime",
  },
]);
const payTableColumns = ref([
  {
    label: "订单号",
    prop: "orderNo",
  },
  {
    label: "原价（元）",
    prop: "orderAmount",
    price: true,
    align: "center",
  },
  {
    label: "优惠金额（元）",
    prop: "floatAmount",
    price: true,
    align: "center",
  },
  {
    label: "支付金额（元）",
    prop: "receivableAmount",
    price: true,
    align: "center",
  },
  {
    label: "订单类型",
    prop: "orderTypeName",
    align: "center",
  },
  {
    label: "到账时间",
    prop: "paymentTime",
  },
]);

onMounted(() => {
  //组件加载完成后执行事件
});

// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  tabLoading,
  dataTotal,
  tableData,
  current,
  fromData,
  loadData,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/order/pageParentOrder",
  queryForm,
});

/**
 * 行点击事件
 */
const handleRowClick = (e) => {
  emit("rowClick", e);
};

const copy = (e) => {
  // 复制到剪切板
  navigator.clipboard.writeText(e);
  plMessage("复制成功", "success");
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "业务类型",
    prop: "businessTypeList",
    type: "select",
    options: businessTypeList,
    valueKey: "businessType",
    labelKey: "businessTypeName",
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "订单状态",
    prop: "orderStatusList",
    type: "select",
    options: orderStatusList,
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  // {
  //   label: "订单类型",
  //   prop: "orderStatus",
  //   type: "select",
  //   options: orderTypeList,
  //   filterable: true,
  //   multiple: true,
  //   collapseTags: true,
  //   collapseTagsTooltip: true,
  //   maxCollapseTags: 1,
  // },

  {
    label: "支付方式",
    prop: "paymentWayList",
    type: "select",
    options: paymentWayList,
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "支付时间",
    prop: "ZFSJ",
    type: "datetimerange",
    format: "YYYY-MM-DD HH:mm:ss",
    onChange: (e) => {
      queryForm.value.startPaymentTime = e[0];
      queryForm.value.endPaymentTime = e[1];
    },
  },
  {
    label: "支付状态",
    prop: "paymentStatusList",
    type: "select",
    options: paymentStatusList,
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "订单来源",
    prop: "orderPlatformList",
    type: "select",
    options: orderPlatformList,
    filterable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },

  {
    label: "下单时间",
    prop: "xdsj",
    type: "datetimerange",
    format: "YYYY-MM-DD HH:mm:ss",
    onChange: (e) => {
      queryForm.value.startOrderTime = e[0];
      queryForm.value.endOrderTime = e[1];
    },
  },
]);

// 表格列配置
const tableColumns = ref([
  // { type: "selection", width: 55 },
  { label: "序号", type: "index", width: 80 },
  {
    label: "订单号",
    sortProp: "orderNo",
    sortable: "custom",
    minWidth: 180,
    template: "orderNo",
  },
  {
    label: "业务类型",
    prop: "businessTypeName",
    sortProp: "businessType",
    sortable: "custom",
    minWidth: 110,
  },
  { label: "支付方式", prop: "paymentWayName", minWidth: 110 },
  { label: "支付时间", prop: "paymentTime", minWidth: 180 },
  {
    label: "支付状态",
    prop: "orderPaymentStatusName",

    minWidth: 110,
  },
  {
    label: "订单金额",
    prop: "paymentAmount",
    minWidth: 110,
    price: true,
    align: "center",
  },
  {
    label: "订单来源",
    prop: "orderPlatformName",
    minWidth: 110,
  },
  {
    label: "订单状态",
    prop: "orderStatusName",
    sortable: "custom",
    sortProp: "orderStatus",
    minWidth: 110,
  },
  { label: "下单时间", prop: "orderTime", sortable: "custom", minWidth: 180 },
  { label: "备注", prop: "remark", sortable: "custom" },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped>
.order-tmp {
  display: flex;
  .order-no {
    flex: 1;
  }
  .icon {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
.order-no {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.order-details {
  display: flex;
  padding-bottom: 20px;
  .order-info {
    flex: 1;
  }
  .status-flow {
    margin-left: 20px;
    width: 300px;
  }
}
.status-flow-content {
  .label {
    display: inline-block;
    width: 70px;
    text-align: left;
  }
  .child-item {
    margin-bottom: 3px;
    font-size: 12px;
  }
}

.card-flex {
  padding: 15px 0 15px 15px;
}
</style>
<style lang="scss">
.order-pl-details-drawer {
  .content.isPadding {
    padding-bottom: 20px;
  }
}
</style>
