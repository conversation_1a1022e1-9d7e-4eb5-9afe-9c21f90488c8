<template>
  <!-- 卡片组件开始 -->
  <pl-card class="unified-ticket-sales">
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        formType="1"
        clear
        @confirm="search"
        @cancel="reset"
      >
      </pl-form>
      <!-- 查询表单结束 -->
      <pl-drag-range :initialLeftWidth="1000">
        <template #left>
          <div class="left-content-ticket">
            <!-- 表格展示区域开始 -->
            <!-- 原表格 -->
            <div class="card-table old-table">
              <pl-table
                :columns="oldTableColumns"
                :data="oldTableData"
                class="table"
                border
              >
                <!-- 标签 -->
                <template #header="{ column }">
                  <span v-if="column.label == '起点'" class="tb-hd-old"
                    >原</span
                  >
                  {{ column.label }}
                </template>
                <!-- 操作 -->
                <template #operation="{ scope }">
                  <pl-button type="primary" @click="handleEdit(scope.row)"
                    >编辑</pl-button
                  >
                  <pl-button
                    type="danger"
                    @click="handleDelete(scope.row, '设置成需要删除的id名字')"
                    >删除</pl-button
                  >
                </template>
              </pl-table>
            </div>

            <!-- 新表格 -->
            <div class="card-table mt20">
              <pl-table
                :columns="newTableColumns"
                :data="newTableData"
                class="table"
                v-loading="tabLoading"
                border
                @sort-change="handleSort"
                highlight-current-row
                @current-change="handleRowSelect"
              >
                <!-- 选择 -->
                <template #checked="{ scope }">
                  <pl-radio
                    :options="radioOptions"
                    v-model="radioValue"
                    :disabled="!scope.row.isSupportSell"
                    :value="scope.row.ticketPriceId"
                    @change="handleRowSelect(scope.row, 'change')"
                  ></pl-radio>
                </template>
                <!-- 标签 -->
                <template #header="{ column }">
                  <span v-if="column.label == '起点'" class="tb-hd-new"
                    >新</span
                  >
                  {{ column.label }}
                </template>
                <!-- 日期/时间 -->
                <!-- 操作 -->
                <template #operation="{ scope }">
                  <pl-button type="primary" @click="handleEdit(scope.row)"
                    >编辑</pl-button
                  >
                  <pl-button
                    type="danger"
                    @click="handleDelete(scope.row, '设置成需要删除的id名字')"
                    >删除</pl-button
                  >
                </template>
              </pl-table>
            </div>
            <!-- 表格展示区域结束 -->

            <!-- 分页组件开始 -->
            <pl-pagination
              v-show="newTableData.length > 0"
              :total="dataTotal"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></pl-pagination>
            <!-- 分页组件结束 -->
          </div>
        </template>
        <template #right>
          <div class="seat">
            <div class="auto-scheduling-tip" v-if="curIsAuto">
              <div class="tip-box">
                <div class="tip-icon">🚌</div>
                <div class="tip-content">
                  <div class="tip-title">滚动发车</div>
                  <div class="tip-desc">座位将随机分配</div>
                </div>
              </div>
            </div>
            <seatSelect
              v-if="showSeat"
              isSale
              :dropValueData="[]"
              :seatInfoSetList="seatData"
              @clickSeat="seatSelectChange"
            ></seatSelect>
            <div v-else>
              <div class="seat-empty">
                <div class="seat-empty-text">🎫请选择左侧班次</div>
              </div>
            </div>
          </div>
        </template>
      </pl-drag-range>
    </div>
    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <pl-button class="btn" type="primary" @click="handleSaveData"
        >确定</pl-button
      >
    </div>

    <!-- 确认改签弹窗 -->
    <pl-dialog
      v-model="dialogVisible"
      title="乘车人信息"
      append-to-body
      align-center
      showCancel
      :loading="affirmLoad"
      @confirm="handleConfirm"
      @cancel="dialogVisible = false"
    >
      <template #content>
        <!-- 票种类型 -->
        <div class="info-item">
          <div class="info-item-label">票种类型:</div>
          <pl-select
            v-model="affirmTicket.passengers[0].ticketType"
            :options="ticketTypeList"
            @change="ticketTypeChange"
          ></pl-select>
        </div>
        <!-- 乘车人 -->
        <div class="info-item">
          <div class="info-item-label">乘车人:</div>
          <pl-input v-model="affirmTicket.passengers[0].passName"></pl-input>
        </div>
        <!-- 联系电话 -->
        <div class="info-item">
          <div class="info-item-label">联系电话:</div>
          <pl-input
            type="number"
            v-model="affirmTicket.passengers[0].passPhone"
            placeholder="请输入手机号码"
          ></pl-input>
        </div>
        <!-- 身份证号 -->
        <div class="info-item">
          <div class="info-item-label">身份证号:</div>
          <pl-input
            v-model="affirmTicket.passengers[0].passId"
            placeholder="请输入身份证号码"
          ></pl-input>
        </div>
        <!-- 付款方式 -->
        <div class="info-item">
          <div class="info-item-label">付款方式:</div>
          <pl-select
            v-model="affirmTicket.paymentWay"
            :options="payTypeList"
            @change="priceTypeChange"
          ></pl-select>
        </div>
        <!-- 付款金额 -->
        <div class="info-item">
          <div class="info-item-label">
            优惠{{ ticketRule.discountAmount >= 0 ? "/上涨" : "/下调" }}金额:
          </div>
          <pl-input disabled v-model="ticketRule.discountAmount"></pl-input>
        </div>
        <!-- 计价类型 -->
        <div class="info-item">
          <div class="info-item-label">计价类型:</div>
          <pl-input
            disabled
            v-model="
              affirmTicket.passengers[0].ticketPricingRuleInfoList
                .pricingTypeFloatPriceMapVO
            "
          ></pl-input>
        </div>
        <!-- 附加费 -->
        <div class="info-item">
          <div class="info-item-label">附加费:</div>
          <pl-input disabled v-model="ticketRule.surchargeAmount">
            <template #suffix>
              <span>#</span>
            </template>
          </pl-input>
        </div>
        <!-- 金额 -->
        <div class="info-item">
          <div class="info-item-label">支付金额:</div>
          <pl-input disabled v-model="ticketRule.paymentAmount"></pl-input>
        </div>
      </template>
    </pl-dialog>
  </pl-card>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { useTable } from "@/hooks/usetTable";
import { oldTableColumns, newTableColumns } from "./config";
import {
  getTicketByNo,
  getSiteList,
  getTicketSeat,
  getVehicleModelList,
  getNewTicketList,
  rebookTicket,
  tickPriceCal,
  getBusinessDetail,
  confirmPayTicketOrder,
} from "@/api/index";
import { getDictionaryData } from "@/api/dict";
import seatSelect from "@/components/seat-select/seat-select.vue";
import { plMessage } from "pls-common";

onMounted(() => {
  //组件加载完成后执行事件
  console.log("加载完成");
});

// 加载状态控制
const tabLoading = ref(false);

// 获取字典数据
const ticketTypeList = ref([]); // 票据类型
const ticketFromList = ref([]); // 票据来源
const payTypeList = ref([]); // 支付方式
const priceTypeList = ref([]); // 计价类型
const getDict = async () => {
  const res = await getDictionaryData({
    dictTypeCode: "ticket_type",
  });
  ticketTypeList.value = res;
  const res2 = await getDictionaryData({
    dictTypeCode: "order_platform",
  });
  ticketFromList.value = res2;
  const res3 = await getDictionaryData({
    dictTypeCode: "pricing_type",
  });
  priceTypeList.value = res3;
  const res4 = await getDictionaryData({
    dictTypeCode: "payment_way",
  });
  payTypeList.value = res4;
  payTypeList.value.forEach((item) => {
    if (item.value !== "XJ") {
      item.disabled = true;
    }
  });
};

// 使用 useTable 钩子管理表格相关逻辑
const queryForm = ref({
  stepTime: ["00:00:00", "23:59:59"],
  stepDate: getCurrentDate(),
}); // 查询表单的数据
const { handleEdit, handleDelete, handleSort } = useTable({
  noLoad: true,
  list: "",
  queryForm,
});
// 获取站点列表
const siteList = ref([]);
const getSite = async () => {
  const { data: res } = await getSiteList({ stopMode: "DZ" });
  siteList.value = res;
};
// 获取车型列表
const vehicleModelList = ref([]);
const getVehicleModel = async () => {
  const { data: res } = await getVehicleModelList({});
  vehicleModelList.value = res;
};

const emit = defineEmits(["close"]);
const handleClose = () => {
  emit("close");
  // 重置搜索/表单/选座状态
  reset();
};
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  ticketNo: {
    type: String,
    required: true,
  },
});

// 座次选中
const curSiteInfo = ref({});
const seatSelectChange = (data) => {
  curSiteInfo.value = data;
};

// ^ 新表格票据数据
const dataTotal = ref(0); // 新表格数据总条数
const newCurrent = ref(1); // 新表格当前页
const newLimit = ref(10); // 新表格每页条数
const newTableData = ref([]); // 新表格数据
// 获取新票据列表
const getNewTickList = async () => {
  tabLoading.value = true;
  try {
    const params = {
      ...queryForm.value,
      current: newCurrent.value,
      limit: newLimit.value,
    };
    const { data: res } = await getNewTicketList(params);
    newTableData.value = res.records;
    dataTotal.value = res.total;
  } catch (error) {
    console.log("error", error);
  } finally {
    tabLoading.value = false;
  }
};
// 票据选择
const radioValue = ref("");
const radioOptions = ref([
  {
    label: "",
    value: "1",
  },
]);
// 选择行座
const showSeat = ref(false);
const seatData = ref([]);
const curIsAuto = ref(false); // 是否滚动发车
const handleRowSelect = async (row, ident) => {
  if (!row) return;
  curIsAuto.value = row.isAutoScheduling == 1 ? true : false;

  if (curIsAuto.value == 1) {
    affirmTicket.value.passengers[0].seatNum = "xx";
    curTicketPriceId.value = row.ticketPriceId;

    radioValue.value = row.ticketPriceId;
    // 只在点击 radio 时发起请求
    if (ident === "change") {
      const { data: res } = await getTicketSeat({
        ticketPriceId: row.ticketPriceId,
        schedulingItemId: row.schedulingItemId,
      });
      seatData.value = res.seatInfo ? JSON.parse(res.seatInfo) : [];
    } else {
      showSeat.value = false;
    }
  } else {
    // 更新选中状态
    curTicketPriceId.value = row.ticketPriceId;
    radioValue.value = row.ticketPriceId;
    // 只在点击 radio 时发起请求
    if (ident === "change") {
      showSeat.value = true;
      const { data: res } = await getTicketSeat({
        ticketPriceId: row.ticketPriceId,
        schedulingItemId: row.schedulingItemId,
      });
      seatData.value = res.seatInfo ? JSON.parse(res.seatInfo) : [];
    } else {
      showSeat.value = false;
    }
  }
};
// 付款方式change
const priceTypeChange = (e, item) => {
  console.log(e, item);
};
// 新表格分页
const handleSizeChange = (e) => {
  newLimit.value = e;
  getNewTickList();
};
const handleCurrentChange = (e) => {
  newCurrent.value = e;
  getNewTickList();
};

// ^原票据数据
// 获取原票据数据
const oldTableData = ref([]);
const oldTickInfo = ref();
const getTickInfo = async () => {
  const { data: res } = await getTicketByNo(props.ticketNo);
  oldTickInfo.value = res;
  oldTableData.value = [res];
  // 乘车起点/终点赋值
  queryForm.value.startStop = res.startStopId;
  queryForm.value.endStop = res.endStopId;
  console.log("oldTableData", oldTableData.value);
};

// ^ 点击搜索
const search = () => {
  if (!queryForm.value.stepDate) {
    plMessage("请选择乘车日期", "warning");
    return;
  }
  if (!queryForm.value.stepTime || !queryForm.value.stepTime.length) {
    plMessage("请选择乘车时间", "warning");
    return;
  }
  if (!queryForm.value.startStop) {
    plMessage("请选择乘车起点", "warning");
    return;
  }
  if (!queryForm.value.endStop) {
    plMessage("请选择乘车终点", "warning");
    return;
  }

  // 拼接搜索参数
  queryForm.value.startDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[0];
  queryForm.value.endDepartureTime =
    queryForm.value.stepDate + " " + queryForm.value.stepTime[1];
  queryForm.value.startStopId = queryForm.value.startStop;
  queryForm.value.endStopId = queryForm.value.endStop;
  queryForm.value["orderPlatform"] = "SPCK";
  getNewTickList();
};

// ^ 确认改签
const dialogVisible = ref(false); // 乘车人信息弹窗
// 确定乘车人表单
const affirmTicket = ref({
  businessType: "", // 业务类型
  discountAmount: "", // 总优惠金额
  orderAmount: "", // 总订单金额
  orderPlatform: "", // 订单来源
  orgId: "", // 组织id
  passengers: [
    {
      passId: "", // 身份证号
      passName: "", // 乘车人
      passPhone: "", // 联系电话
      seatNum: "", // 座位号
      ticketType: "", // 票种类型
      ticketPricingRuleInfoList: {
        originalPrice: "", // 原价
        price: "", // 票价
        pricingTypeFloatPriceMap: "", //  计价类型浮动金额列表
        pricingTypeFloatPriceMapVO: "", //  计价类型浮动金额列表
        surcharge: "", // 附加费
      },
    },
  ],
  paymentAmount: "", // 	总支付金额
  paymentWay: "", // 付款方式
  surchargeAmount: "", // 总附加费
  ticketPriceId: "", // 	票价id
});
// 计价规则数据
const ticketRule = ref({
  discountAmount: "",
  orderAmount: "",
  paymentAmount: "",
  surchargeAmount: "",
  ticketPricingRuleInfoList: [],
});
// 票种类型选择
const curTickType = ref({});
const curTicketPriceId = ref("");
const ticketTypeChange = async (e, item) => {
  curTickType.value = item;
  const { data: res } = await tickPriceCal({
    ticketPriceId: curTicketPriceId.value,
    ticketTypeList: [item.value],
  });

  // ? 赋值
  ticketRule.value = res;

  // 计价类型转换
  const pricingMap = res.ticketPricingRuleInfoList[0].pricingTypeFloatPriceMap;
  const convertedPricing = Object.entries(pricingMap)
    .map(([key]) => {
      const matchedType = priceTypeList.value.find(
        (type) => type.value === key
      );
      return matchedType ? `${matchedType.label}` : `${key}`;
    })
    .join(",");
  // 赋值转换后的计价类型字符串
  affirmTicket.value.passengers[0].ticketPricingRuleInfoList.pricingTypeFloatPriceMapVO =
    convertedPricing;
};
const affirmLoad = ref(false); // 确认按钮load
// 点击确认
const handleConfirm = async () => {
  // 基础信息校验
  if (!curTickType.value.label) {
    return plMessage("请选择票种类型", "warning");
  }
  if (!affirmTicket.value.passengers[0].passName) {
    return plMessage("请输入乘车人", "warning");
  }

  // 手机号校验
  const phoneReg = /^1[3-9]\d{9}$/;
  const phone = affirmTicket.value.passengers[0].passPhone;
  if (!phone) {
    return plMessage("请输入联系电话", "warning");
  }
  if (!phoneReg.test(phone)) {
    return plMessage("请输入正确的手机号码", "warning");
  }
  // 身份证号校验
  const idCardReg =
    /(^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)?$)/;
  const idCard = affirmTicket.value.passengers[0].passId;
  if (!idCard) {
    return plMessage("请输入身份证号", "warning");
  }
  if (!idCardReg.test(idCard)) {
    return plMessage("请输入正确的身份证号码", "warning");
  }
  if (!affirmTicket.value.paymentWay) {
    return plMessage("请选择付款方式", "warning");
  }

  affirmLoad.value = true;
  try {
    const orderPlaceOrderDTO = {
      ...affirmTicket.value,
    };
    // 票种类型
    orderPlaceOrderDTO.orgId = JSON.parse(
      localStorage.getItem("userInfo")
    ).orgId;
    orderPlaceOrderDTO.paymentAmount = ticketRule.value.paymentAmount;
    orderPlaceOrderDTO.paymentWay = affirmTicket.value.paymentWay;
    orderPlaceOrderDTO.orderPlatform = "SPCK";
    orderPlaceOrderDTO.discountAmount = ticketRule.value.discountAmount;
    orderPlaceOrderDTO.orderAmount = ticketRule.value.orderAmount;
    orderPlaceOrderDTO.passengers[0].seatNum =
      curIsAuto.value == 1 ? "xx" : curSiteInfo.value.num;

    orderPlaceOrderDTO.surchargeAmount = ticketRule.value.surchargeAmount;
    orderPlaceOrderDTO.passengers[0].passId =
      affirmTicket.value.passengers[0].passId;
    orderPlaceOrderDTO.passengers[0]["ticketPricingRuleInfoVO"] =
      ticketRule.value.ticketPricingRuleInfoList[0];
    orderPlaceOrderDTO.ticketPriceId = curTicketPriceId.value;
    orderPlaceOrderDTO.isRebook = true; // 是否改签
    orderPlaceOrderDTO.isOversold = false; // 是否超卖

    // 获取业务类型编码
    const { data: typeRes } = await getBusinessDetail({
      businessCode: "DZZX",
    });
    orderPlaceOrderDTO.businessType = typeRes.businessType;
    const rebookParams = {
      orderPlaceOrderDTO,
      ticketNo: props.ticketNo,
      describe: "改签",
      refundModel: "YLTH",
    };
    console.log("orderPlaceOrderDTO params", orderPlaceOrderDTO);
    const orderRes = await rebookTicket(rebookParams);
    if (orderRes.code == 200) {
      if (orderRes.data.premiumNo) {
        const res = await confirmPayTicketOrder({
          orderNo: orderRes.data.orderNo,
          premiumNo: orderRes.data.premiumNo,
          paymentWay: affirmTicket.value.paymentWay,
          isOversold: false, // 是否超卖
          businessType: typeRes.businessType,
          orderPlatform: "SPCK",
          orgId: JSON.parse(localStorage.getItem("userInfo")).orgId,
        });
        if (res.code !== 200) {
          plMessage(res.message, "warning");
          return;
        }
      }
      affirmLoad.value = false;
      plMessage(orderRes.message, "success");
      dialogVisible.value = false;
      handleClose();
    }
  } catch (error) {
    console.log("err", error);
  } finally {
    affirmLoad.value = false;
  }
};

// ^ 点击保存改签
const handleSaveData = () => {
  if (curIsAuto.value == 1) {
    plMessage("请选择班次", "warning");
    return;
  }

  if (!curSiteInfo.value.num && curIsAuto.value == 0) {
    plMessage("请选择座位", "warning");
    return;
  }
  dialogVisible.value = true;
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "乘车日期",
    prop: "stepDate",
    type: "date",
    format: "YYYY-MM-DD",
    placeholder: "请输入乘车日期",
  },
  {
    label: "乘车时间",
    prop: "stepTime",
    type: "time-picker",
    format: "HH:mm:ss",
  },
  {
    label: "乘车起点",
    prop: "startStop",
    type: "select",
    filterable: true,
    labelKey: "stopName",
    valueKey: "stopId",
    options: siteList,
    clearable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "乘车终点",
    prop: "endStop",
    type: "select",
    filterable: true,
    labelKey: "stopName",
    valueKey: "stopId",
    options: siteList,
  },
  {
    label: "车型",
    prop: "vehicleModelId",
    type: "select",
    options: vehicleModelList,
    labelKey: "vehicleModelName",
    valueKey: "vehicleModelId",
  },
]);

// 默认乘车日期修改
function getCurrentDate() {
  const currentDate = new Date().toISOString().split("T")[0];
  return currentDate;
}

// 重置状态
const reset = () => {
  queryForm.value = {
    stepTime: ["00:00:00", "23:59:59"],
    stepDate: getCurrentDate(),
  };
  getTickInfo();
  newTableData.value = [];
  radioValue.value = "";
  radioOptions.value = [
    {
      label: "",
      value: "1",
    },
  ];
  showSeat.value = false;
  // 重置 affirmTicket 为初始状态
  affirmTicket.value = {
    businessType: "",
    discountAmount: "",
    orderAmount: "",
    orderPlatform: "",
    orgId: "",
    passengers: [
      {
        passId: "",
        passName: "",
        passPhone: "",
        seatNum: "",
        ticketType: "",
        ticketPricingRuleInfoList: {
          originalPrice: "",
          price: "",
          pricingTypeFloatPriceMap: "",
          pricingTypeFloatPriceMapVO: "",
          surcharge: "",
        },
      },
    ],
    paymentAmount: "",
    paymentWay: "",
    surchargeAmount: "",
    ticketPriceId: "",
  };
  // 重置 curTickType
  curTickType.value = {};
  // 重置 curSiteInfo
  curSiteInfo.value = {};
  ticketRule.value = {};
  // 重置 seatData
  seatData.value = [];
};

watch(
  () => props.visible,
  (newVal) => {
    console.log("watch", newVal);
    if (newVal) {
      getSite();
      getDict();
      getTickInfo();
      getVehicleModel();
    }
  }
);
</script>

<style lang="scss" scoped>
.tb-hd-new {
  background-color: var(--el-color-primary);
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-weight: normal;
  font-size: 12px;
  color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
}
.tb-hd-old {
  background-color: #ccc;
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-weight: normal;
  font-size: 12px;
  color: #797979;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
}
.button-box {
  z-index: 99;
}
.left-content-ticket {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.old-table {
  max-height: 80px;
}
.unified-ticket-sales {
  padding-bottom: 60px !important;
}

.seat-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  font-size: 14px;
  color: #999;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .info-item-label {
    white-space: nowrap;
    min-width: 100px;
  }
}

.seat {
  width: 100%;
  height: 100%;
  position: relative;
}
.auto-scheduling-tip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  .tip-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 16px;
    max-width: 320px;
    text-align: center;
  }
  .tip-icon {
    font-size: 32px;
    flex-shrink: 0;
  }

  .tip-content {
    flex: 1;

    .tip-title {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .tip-desc {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
    }
  }
}
</style>
