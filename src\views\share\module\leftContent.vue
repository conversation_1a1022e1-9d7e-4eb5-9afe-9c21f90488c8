<template>
  <div class="content-details">
    <pl-form
      :fields="columns"
      inline
      :form="queryForm"
      confirmButtonText="搜索"
      cancelButtonText="重置"
      @confirm="init"
      @cancel="handleCancel"
    ></pl-form>
    <div class="content-table">
      <pl-table
        :columns="tableColumns"
        :data="tableData"
        class="table"
        row-key="sharerClientUserId"
        @row-click="handleRowClick"
      ></pl-table>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { postUserShareList } from "@/api/travel";
const queryForm = ref({});
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const { userId, appId } = props.data;
const emit = defineEmits(["rowClick", "shareData"]);

const init = () => {
  postUserShareList({
    sharerClientUserId: userId,
    appId,
    ...queryForm.value,
  }).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data?.sharedItemVOList || [];
      emit("shareData", {
        commissionAmount: res.data?.commissionAmount,
        shareAmount: res.data?.shareAmount,
        shareOrderCount: res.data?.shareOrderCount,
      });
    }
  });
};

init();
const handleCancel = () => {
  queryForm.value = {};
  init();
};

const handleRowClick = (row) => {
  emit("rowClick", row);
};

const columns = ref([
  {
    label: "用户名",
    prop: "userName",
    type: "input",
  },
  {
    label: "手机号",
    prop: "phone",
    type: "input",
  },
]);
const tableColumns = ref([
  {
    label: "用户名",
    prop: "userName",
  },
  {
    label: "手机号",
    prop: "phone",
  },
  {
    label: "分享时间",
    prop: "shareTime",
  },
]);
const tableData = ref([]);
</script>

<style lang="scss" scoped>
.content-details {
  display: flex;
  flex-direction: column;
  height: 100%;
  .content-table {
    flex: 1;
    position: relative;
    .table {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
