<template>
  <div style="width: 100%">
    <plTreeSelectPro
      :options="options"
      :cascaderProps="cascaderProps"
      @change="change"
      v-model="selectVal"
      :valueType="valueType"
      :disabled="disabled"
      :multiple="multiple"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getAllLineTypeTree } from "@/api/travel";
import { plMessage } from "pls-common";

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    deault: "",
  },
  valueType: {
    type: String,
    default: "string",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true,
  },
  checkStrictly: {
    type: Boolean,
    default: true,
  },
  // 是否能选父级
  canSelectParent: {
    type: Boolean,
    default: true,
  },
});

watch(
  () => props.modelValue,
  (val) => {
    console.log(val, "线路类型", "line-type");
    if (!val) {
      selectVal.value = [];
    } else {
      selectVal.value = val;
    }
  }
);

const selectVal = ref(props.modelValue || []);
const options = ref([]);
const cascaderProps = {
  label: "lineTypeName",
  value: "lineTypeCode",
  children: "children",
  checkStrictly: false,
};

onMounted(() => {
  getAllLineTypeTree().then((res) => {
    options.value = res.data;
  });
});

const emit = defineEmits(["change", "update:modelValue"]);
const change = (val, val2) => {
  if (
    val2 &&
    val2.length > 0 &&
    !props.canSelectParent &&
    val2[0].children &&
    val2[0].children.length > 0
  ) {
    plMessage("不能选择父级", "warning");
    selectVal.value = [];
    return;
  }
  emit("update:modelValue", val);
  emit("change", val, val2);
};

defineExpose({
  getOptions: () => {
    return options.value;
  },
});
</script>

<style lang="scss" scoped></style>
