<template>
  <div class="not-found">
    <div class="error-container">
      <div class="space-scene">
        <div class="astronaut">
          <div class="astronaut-body">👨‍🚀</div>
        </div>
        <div class="planet">🌍</div>
        <div class="stars"></div>
      </div>

      <h1 class="error-title">
        <span class="digit">4</span>
        <span class="planet-o">🪐</span>
        <span class="digit">4</span>
      </h1>

      <p class="error-subtitle">糟糕！看来您迷失在太空了</p>
      <p class="error-description">别担心，让我们一起返回地球吧</p>

      <div class="error-action">
        <pl-button
          type="primary"
          class="back-button"
          @click="$router.push('/')"
        >
          <span class="rocket">🚀</span> 返回首页
        </pl-button>
        <pl-button class="history-button" @click="$router.go(-1)">
          返回上页
        </pl-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 可以添加一些交互逻辑
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  overflow: hidden;
}

.error-container {
  text-align: center;
  position: relative;
  padding: 20px;
}

.space-scene {
  position: relative;
  height: 200px;
  margin-bottom: 30px;
}

.astronaut {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  animation: float 6s ease-in-out infinite;
}

.astronaut-body {
  font-size: 60px;
  transform: rotate(45deg);
}

.planet {
  position: absolute;
  font-size: 40px;
  right: 20%;
  top: 30%;
  animation: rotate 20s linear infinite;
}

.stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(white 1px, transparent 1px);
  background-size: 50px 50px;
  animation: twinkle 1s ease-in-out infinite;
}

.error-title {
  font-size: 72px;
  color: #fff;
  margin: 0 0 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.digit {
  animation: pulse 2s ease-in-out infinite;
}

.planet-o {
  font-size: 60px;
  animation: rotate 8s linear infinite;
}

.error-subtitle {
  font-size: 24px;
  color: #fff;
  margin: 0 0 15px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.error-description {
  font-size: 18px;
  color: #a0a0a0;
  margin: 0 0 30px;
}

.error-action {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.back-button {
  padding: 12px 24px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s;
}

.back-button:hover {
  transform: translateY(-3px);
}

.rocket {
  animation: shake 1s ease-in-out infinite;
}

.history-button {
  padding: 12px 24px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0%,
  100% {
    transform: translate(-50%, -50%) translateY(0);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
}
</style>
