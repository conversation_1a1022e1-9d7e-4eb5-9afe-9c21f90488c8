/**
 * 用户类型
 */
export interface User {
  avatar: string;
  createBy: number;
  createTime: string;
  deleted: boolean;
  employeeNo: string | null;
  loginAccount: string;
  openId: string | null;
  orgId: number | null;
  password: string;
  phone: string;
  postId: number | null;
  remark: string | null;
  sex: boolean | null;
  status: number;
  tenantId: number;
  token: string;
  updateBy: number | null;
  updateTime: string;
  userId: number;
  userType: number;
  username: string;
}
