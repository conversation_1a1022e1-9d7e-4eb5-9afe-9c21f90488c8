<!-- 用户管理 -->
<template>
  <pl-card>
    <div class="card-flex">
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>

      <!-- 表格 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
        >
          <template #avatar="{ scope }">
            <pl-image
              class="avatar"
              :src="scope.row.avatar"
              alt=""
              v-if="scope.row.avatar"
            />
            <span v-else style="color: #999; font-size: 13px">暂无</span>
          </template>

          <template #gender="{ scope }">
            <span v-if="scope.row.gender === 0">男</span>
            <span v-else-if="scope.row.gender === 1">女</span>
            <span v-else>未知</span>
          </template>

          <template #status="{ scope }">
            <pl-switch
              v-model="scope.row.status"
              activeText="启用"
              inactiveText="禁用"
              @change="handleStatusChange($event, scope.row)"
            ></pl-switch>
          </template>

          <!-- 操作列 start -->
          <template #operate="{ scope }">
            <pl-button
              type="primary"
              link
              @click="handleDetail(scope)"
              v-has="'menu_user_list:btn_user_view'"
              >查看</pl-button
            >
          </template>
          <!-- 操作列 end -->
        </pl-table>
      </div>

      <!-- 分页 -->
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        :currentPage="current"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 查看详情 start -->
    <pl-drawer :title="drawerTitle" v-model="drawerVisible">
      <pl-scrollbar class="h100 tenant-Scrollbar" v-if="drawerVisible">
        <div class="drawer-w">
          <div class="title">用户信息</div>
          <pl-form
            :fields="userInfoFields"
            inline
            :isButtonShow="false"
            ref="formRefQT"
            :form="formData"
            :disabled="formDisabled"
          >
            <template #gender>
              {{
                formData["gender"] == 0
                  ? "男"
                  : formData["gender"] == 1
                  ? "女"
                  : ""
              }}
            </template>
          </pl-form>
        </div>
        <div class="drawer-w">
          <div class="title">授权应用</div>
          <table class="route-table">
            <tbody>
              <tr class="head">
                <th>应用名称</th>
                <th>icon</th>
              </tr>
              <tr
                class="item"
                v-for="item in formData.appList"
                :key="item.conAppId"
              >
                <td>{{ item.appName }}</td>
                <td>{{ item.appIcon }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </pl-scrollbar>
    </pl-drawer>
    <!-- 查看详情 end -->
  </pl-card>
</template>

<script setup>
import { ref } from "vue";
import { useTable } from "@/hooks/usetTable";
import { getClientUserDetail, upDateClientUserStatus } from "@/api/index";
import { plMessage } from "pls-common";
const formData = ref({});

// 预览弹窗显示
// const previewVisible = ref(false);
// /**
//  * 预览线路
//  */
// const handlePreview = () => {
//   previewVisible.value = true;
// };
const {
  tableData,
  formDisabled,
  drawerTitle,
  drawerVisible,
  dataTotal,
  tabLoading,
  loadData,
  sizeChange,
  currentChange,
  handleCancel,
  current,
} = useTable({
  list: "/ClientUser/pageClientUser",
  del: {
    message: "确定要删除该排班的数据吗?",
    type: "message",
  },
});

const handleStatusChange = (e, row) => {
  upDateClientUserStatus({
    clientUserId: row.clientUserId,
    status: e ? 1 : 0,
  }).then((res) => {
    if (res.code === 200) {
      plMessage(res.message, "success");
    } else {
      plMessage(res.message, "error");
    }
  });
};
const clientUserId = ref("");
// 查看详情
const handleDetail = ({ row }) => {
  clientUserId.value = row.clientUserId;
  getClientUserDetail({
    clientUserId: row.clientUserId,
  }).then((res) => {
    if (res.code === 200) {
      formData.value = res.data;
      drawerTitle.value = "详情";
      drawerVisible.value = true;
    }
  });
};

// 条件搜索
const handleSearch = (e) => {
  loadData({
    nickname: e.nickname || "",
    phone: e.phone || "",
    startTime: (e.startTimeAndEndTime && e.startTimeAndEndTime[0]) || "",
    endTime: (e.startTimeAndEndTime && e.startTimeAndEndTime[1]) || "",
  });
};

// 弹窗-用户信息
const userInfoFields = ref([
  {
    label: "昵称",
    prop: "nickname",
    type: "input",
    disabled: true,
  },
  {
    label: "联系电话",
    prop: "phone",
    type: "number",
    disabled: true,
  },
  {
    label: "性别",
    prop: "gender",
    type: "input",
    disabled: true,
    template: "gender",
  },
  {
    label: "注册来源",
    prop: "appName",
    type: "input",
    disabled: true,
  },
  {
    label: "状态",
    prop: "status",
    type: "select",
    options: [
      { label: "启用", value: 1 },
      { label: "禁用", value: 0 },
    ],
    onChange: (e) => {
      upDateClientUserStatus({
        clientUserId: clientUserId.value,
        status: e,
      }).then((res) => {
        if (res.code === 200) {
          plMessage(res.message, "success");
        } else {
          plMessage(res.message, "error");
        }
      });
    },
  },
  {
    label: "头像",
    type: "image",
    prop: "avatar",
    style: {
      width: "50px",
      height: "50px",
      borderRadius: "50%",
      overflow: "hidden",
    },
  },
  {
    label: "注册时间",
    prop: "createTime",
    type: "datetime",
    disabled: true,
  },
]);

// 列表搜索条件
const formColumns = ref([
  {
    label: "昵称",
    type: "input",
    prop: "nickname",
  },
  {
    label: "手机号",
    prop: "phone",
    type: "number",
  },
  {
    label: "注册时间",
    prop: "startTimeAndEndTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      console.log(e);
    },
  },
]);

// table表格列
const tableColumns = ref([
  {
    label: "序号",
    type: "index",
    width: 60,
  },
  {
    label: "昵称",
    prop: "nickname",
    width: 120,
  },
  {
    label: "联系电话",
    prop: "phone",
    width: 120,
  },
  {
    label: "性别",
    prop: "gender",
    template: "gender",
  },
  {
    label: "注册来源",
    prop: "appName",
  },
  {
    label: "头像",
    prop: "avatar",
    template: "avatar",
  },
  {
    label: "状态",
    prop: "status",
    template: "status",
  },
  {
    label: "注册时间",
    prop: "createTime",
  },
  {
    label: "操作",
    template: "operate",
    fixed: "right",
    width: 240,
  },
]);
</script>

<style lang="scss" scoped>
.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.tenant-Scrollbar {
  padding-bottom: 20px;
}
.route-table {
  text-align: center;
  border-spacing: 0;
  border-color: var(--el-border-color);
  border-width: 1px;
  border-style: solid;
  .head {
    background-color: #f5f7fa;
    th {
      font-weight: normal;
      padding: 10px 0;
      font-size: 14px;
      width: 200px;
      border-bottom: 1px solid var(--el-border-color);
      border-right: 1px solid var(--el-border-color);
      &:last-child {
        border-right: none;
      }
    }
  }
  .item {
    &:last-child {
      td {
        border-bottom: none;
      }
    }
    td {
      padding: 10px 0;
      font-size: 14px;
      border-bottom: 1px solid var(--el-border-color);
      border-right: 1px solid var(--el-border-color);
      &:last-child {
        border-right: none;
      }
    }
  }
}
</style>
