import request from "./request";

/**
 * 登录
 */
export const apiLogin = (params: any) => {
  return request.post("/index/login", params);
};

/**
 * 获取授权
 */
export const apiGetAuth = async (params: any) => {
  const res = await request.post("/index/authorize", params);
  const returnAry: any[] = [];
  if (res.code == 200) {
    res.data.forEach((item: any) => {
      if (item.treeList && item.treeList.length > 0) {
        returnAry.push(item);
      }
    });
  }
  res.data = returnAry;
  return res;
};
