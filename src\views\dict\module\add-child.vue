<template>
  <drawer :title="title" v-model="drawerOpen">
    <pl-form
      :form="form"
      ref="formRef"
      :fields="fields"
      :isButtonShow="false"
      inline
      v-if="drawerOpen"
    ></pl-form>

    <!-- 按钮 -->
    <div class="button-box">
      <pl-button @click="handleClose" size="large">取消</pl-button>
      <pl-button
        plain
        type="primary"
        @click="handleSubmit(false)"
        v-if="type == 'add'"
        size="large"
        >确定并新增</pl-button
      >
      <pl-button type="primary" size="large" @click="handleSubmit(true)"
        >确定</pl-button
      >
    </div>
  </drawer>
</template>

<script setup>
import { ref, watch } from "vue";
import drawer from "@/components/module/drawer.vue";
import apiDict from "@/api/dict";
import { plMessage } from "pls-common";
let formRef = ref(null);

const prop = defineProps({
  modelValue: {
    type: <PERSON>olean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  dictTypeCode: {
    type: String,
    default: "",
  },
  form: {
    type: Object,
    default: () => ({}),
  },
  tenantId: {
    default: 0,
  },
});
/**
 * 类型
 */
let propType = ref(prop.type);
watch(
  () => prop.type,
  (val) => {
    propType.value = val;
    fields.value[1].disabled = val == "edit" ? true : false;
  }
);

let drawerOpen = ref(prop.modelValue);
watch(
  () => prop.modelValue,
  (val) => {
    drawerOpen.value = val;
  }
);
const emit = defineEmits(["update:modelValue", "change"]);
watch(
  () => drawerOpen.value,
  () => {
    emit("update:modelValue", drawerOpen.value);
  }
);

/**
 * 关闭抽屉
 */
const handleClose = () => {
  drawerOpen.value = false;
};

const fields = ref([
  {
    label: "编码名称",
    prop: "dictName",
    type: "text",
    placeholder: "请输入编码名称",
    rules: [{ required: true, message: "请输入编码名称", trigger: "blur" }],
  },
  {
    label: "编码值",
    prop: "dictCode",
    type: "text",
    placeholder: "请输入编码值",
    rules: [{ required: true, message: "请输入编码值", trigger: "blur" }],
  },
  {
    label: "序号",
    prop: "dictSort",
    type: "input-number",
  },
  {
    label: "状态",
    prop: "status",
    type: "radio",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
    rules: [{ required: true, message: "请选择状态", trigger: "blur" }],
  },
  {
    label: "描述",
    prop: "remark",
    type: "textarea",
  },
]);
/**
 * 提交表单
 */
const handleSubmit = (isClosePop) => {
  formRef.value.confirm((data) => {
    if (propType.value == "edit") {
      apiDict
        .updateDict({
          tenantId: prop.tenantId,
          dictId: prop.form.dictId,
          dictTypeCode: prop.dictTypeCode,
          ...data,
        })
        .then((res) => {
          if (res.code == 200) {
            plMessage(res.message, "success");
            emit("change");
            emit("update:modelValue", false);
          } else {
            plMessage(res.message, "error");
          }
        });
    } else {
      apiDict
        .addDict({
          tenantId: prop.tenantId,
          dictTypeCode: prop.dictTypeCode,
          ...data,
        })
        .then((res) => {
          if (res.code == 200) {
            plMessage(res.message, "success");
            emit("change");
            if (isClosePop) {
              emit("update:modelValue", false);
            }
          } else {
            plMessage(res.message, "error");
          }
        });
    }
  });
};
</script>

<style scoped lang="scss"></style>
