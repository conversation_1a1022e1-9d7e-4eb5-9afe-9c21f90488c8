import fs from "fs";
import path from "path";
// import { execSync } from "child_process";
// import readline from "readline";
import { fileURLToPath } from "url";
// import process from "process";

// const rl = readline.createInterface({
//   input: process.stdin,
//   output: process.stdout,
// });

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取version.json
const versionPath = path.join(__dirname, "public/version.json");
const versionData = JSON.parse(fs.readFileSync(versionPath, "utf8"));

// 更新版本号
const now = new Date();
versionData.version = now.getTime().toString();
versionData.updateTime = now.toLocaleString();

// 写入文件
fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2));

// // 定义commit类型
// const commitTypes = [
//   "feat", // 新功能
//   "fix", // 修复
//   "docs", // 文档更改
//   "style", // 代码格式修改
//   "refactor", // 代码重构
//   "perf", // 优化相关
//   "test", // 测试用例修改
//   "chore", // 其他修改
//   "revert", // 回滚到上一个版本
// ];

// // 提示用户输入commit信息
// console.log("请选择提交类型：");
// commitTypes.forEach((type, index) => {
//   console.log(`${index + 1}. ${type}`);
// });

// rl.question("请输入提交类型的序号(1-9): ", (typeIndex) => {
//   const selectedType = commitTypes[parseInt(typeIndex) - 1];

//   if (!selectedType) {
//     console.error("无效的类型序号");
//     rl.close();
//     process.exit(1);
//   }

//   rl.question("请输入提交描述: ", async (description) => {
//     if (!description) {
//       console.error("提交描述不能为空");
//       rl.close();
//       process.exit(1);
//     }

//     try {
//       // git add
//       execSync("git add public/version.json");

//       // git commit
//       const commitMessage = `${selectedType}: ${description}`;
//       execSync(`git commit -m "${commitMessage}"`, { stdio: "inherit" });

//       // git push
//       console.log("正在推送到远程仓库...");
//       execSync("git push", { stdio: "inherit" });

//       console.log("版本更新、提交并推送成功！");
//     } catch (error) {
//       console.error("Git操作失败：", error.message);
//       process.exit(1);
//     }

//     rl.close();
//   });
// });
