<template>
  <plTreeSelectSingle
    :cascaderProps="casProps"
    :options="props.showCustom ? customOptions : options"
    v-model="selectVal"
    @change="change"
    :nodeKey="props.nodeKey"
    :multiple="props.multiple"
    :placeholder="placeholder"
    :disabled="disabled"
    :checkAll="props.checkAll"
  >
    <template #head>
      <slot name="head"></slot>
    </template>
  </plTreeSelectSingle>
</template>

<script setup>
import { ref, watch } from "vue";
import { getParentOrgTreeList } from "@/api/index";

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showCustom: {
    type: Boolean,
    default: false,
  },
  customOptions: {
    type: Array,
    default: () => [],
  },
  casProps: {
    type: Object,
    default: () => {
      // children值需设置为'children',pl-common中已固定
      return {
        label: "orgName",
        value: "id",
        children: "children",
        checkStrictly: true,
      };
    },
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  nodeKey: {
    type: String,
    default: "id",
  },
  checkAll: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
});

const selectVal = ref("");
const options = ref([]);

const emit = defineEmits(["change", "update:modelValue"]);
const change = (val, val2) => {
  emit("update:modelValue", val);
  emit("change", val2);
};

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      selectVal.value = val;
    } else {
      selectVal.value = [];
    }
  },
  { immediate: true }
);

watch(
  () => props.showCustom,
  (val) => {
    if (!val) {
      console.log("===!!!==", val);
      getParentOrgTreeList().then((res) => {
        options.value = res.data;
        selectVal.value = props.modelValue;
      });
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
