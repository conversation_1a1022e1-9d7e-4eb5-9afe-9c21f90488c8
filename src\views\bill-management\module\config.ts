import { ref } from "vue";

// 表格列配置
export const tableColumns = ref([
  // { type: "selection", width: 55, align: "center" },
  { label: "起点", prop: "startStopName", align: "center", minWidth: 160 },
  { label: "终点", prop: "endStopName", align: "center", minWidth: 160 },
  {
    label: "乘车日期",
    prop: "departureDateStr",
    align: "center",
    minWidth: 100,
  },
  {
    label: "乘车时间",
    prop: "departureTimeStr",
    align: "center",
    minWidth: 100,
  },
  {
    label: "预计里程",
    prop: "predictionMileage",
    align: "center",
    minWidth: 100,
  },
  { label: "车型", prop: "vehicleModelName", align: "center", minWidth: 100 },
  { label: "票价", prop: "originalPrice", align: "center", minWidth: 100 },
  {
    label: "余票",
    prop: "remainingTicketCount",
    align: "center",
    minWidth: 100,
  },
]);

// 改签表格配置
// 原表格
export const oldTableColumns = ref([
  { label: "起点", prop: "startStopName", align: "center", minWidth: 140 },
  { label: "终点", prop: "endStopName", align: "center", minWidth: 140 },
  {
    label: "乘车日期",
    prop: "departureDateStr",
    align: "center",
    minWidth: 120,
  },
  {
    label: "乘车时间",
    prop: "departureTimeStr",
    align: "center",
    minWidth: 120,
  },
  {
    label: "预计里程",
    prop: "predictionMileage",
    align: "center",
    minWidth: 100,
  },
  { label: "车型", prop: "vehicleModelName", align: "center", minWidth: 100 },
  {
    label: "票价",
    prop: "originalPrice",
    align: "center",
    price: true,
    minWidth: 100,
  },
]);
// 新表格
export const newTableColumns = ref([
  {
    label: " ",
    template: "checked",
    align: "center",
    minWidth: 50,
  },
  { label: "起点", prop: "startStopName", align: "center", minWidth: 140 },
  { label: "终点", prop: "endStopName", align: "center", minWidth: 140 },
  {
    label: "乘车日期",
    prop: "departureDateStr",
    align: "center",
    minWidth: 120,
  },
  {
    label: "乘车时间",
    prop: "departureTimeStr",
    align: "center",
    minWidth: 120,
  },
  {
    label: "预计里程",
    prop: "predictionMileage",
    align: "center",
    minWidth: 100,
  },
  { label: "车型", prop: "vehicleModelName", align: "center", minWidth: 100 },
  {
    label: "票价",
    prop: "originalPrice",
    align: "center",
    price: true,
    minWidth: 100,
  },
]);
