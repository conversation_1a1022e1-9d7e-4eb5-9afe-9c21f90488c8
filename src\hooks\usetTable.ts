import { ref, onMounted, onUnmounted, nextTick } from "vue";
import http from "@/api/request";
import { plMessage, plMessageBox } from "pls-common";
export function useTable(options: any) {
  const drawerVisible = ref(false); // 控制抽屉显示隐藏
  const drawerTitle = ref(""); // 控制抽屉标题
  const tabLoading = ref(false); // 控制表格加载
  const current = ref(1); // 当前加载第几页
  const limit = ref(10); // 控制表格加载数量
  const tableData = ref([]); // 表格数据
  const dataTotal = ref(0); // 总条数
  const fromData = ref({}); // 搜索数据
  const isSubmitType = ref(""); // 提交类型
  const formDisabled = ref(false); // 表单是否禁用
  const noLoad = options?.noLoad || false; // 是否阻止自动加载
  const formRef = ref(null); // 表单实例
  const isSearch = ref(options?.treeList ? false : true); // 是否搜索
  const sortField = ref(""); // 排序字段
  const sortOrder = ref(); // 排序方式
  const treeData = ref([]); //树形结果单独存储
  let isMouseInside = false;
  nextTick(() => {
    const searchForm = document.querySelectorAll(".pls-form-box");
    searchForm.forEach((el) => {
      el.addEventListener("mouseenter", () => {
        isMouseInside = true;
        console.log("鼠标进入目标元素");
      });
      // 监听鼠标离开
      el.addEventListener("mouseleave", () => {
        isMouseInside = false;
        console.log("鼠标离开目标元素");
      });
    });
  });

  const keyUpHandler = (e: any) => {
    if (e.keyCode == 13 && isMouseInside) {
      //回车键
      loadData();
    }
  };

  onMounted(() => {
    console.log("加载");
    window.addEventListener("keyup", keyUpHandler);
    if (noLoad) return;
    loadData();
  });

  onUnmounted(() => {
    console.log("卸载");
    window.removeEventListener("keyup", keyUpHandler);
  });
  /**
   * 加载数据
   * @param params data
   * @param cb
   * @returns
   */

  const loadData = async (params?: any, cb?: any) => {
    tableData.value = [];
    const queryParams = options.queryForm?.value || {};
    const fixedField = options.fixedField ? { ...options.fixedField } : {};
    params = {
      ...params,
      ...queryParams,
      ...searchData.value,
      ...fixedField,
      sortField: params?.sortField || sortField.value,
      sortOrder: params?.sortOrder || sortOrder.value,
    };

    if (!options?.list) {
      plMessage("请先配置list接口", "error");
      return;
    }

    tabLoading.value = true;

    if (options.listType == "get") {
      await http
        .get(isSearch.value ? options.list : options.treeList, params)
        .then((res) => {
          tabLoading.value = false; // 加载完毕
          if (res.code == 200) {
            tableData.value = res.data
              ? res.data.records
                ? res.data.records
                : res.data
              : []; // 表格数据
            dataTotal.value = res.data.total || 0; // 总条数
            if (cb) {
              return cb(tableData.value);
            }
          }
        })
        .catch(() => {
          tabLoading.value = false; // 加载完毕
        });
      return;
    }

    await http
      .post(isSearch.value ? options.list : options.treeList, {
        limit: limit.value,
        current: current.value,
        ...params,
      })
      .then((res) => {
        tabLoading.value = false; // 加载完毕
        if (res.code == 200) {
          tableData.value = res.data
            ? res.data.records
              ? res.data.records
              : res.data
            : []; // 表格数据
          if (!isSearch.value) {
            treeData.value = tableData.value;
          }
          dataTotal.value = res.data.total || 0; // 总条数
          if (cb) {
            return cb(tableData.value);
          }
        }
      })
      .catch(() => {
        tabLoading.value = false; // 加载完毕
      });
  };

  const searchData = ref({});

  // 表单搜索
  const handleSearch = async (res: object) => {
    if (JSON.stringify(res) === "{}") {
      plMessage("请先输入搜索条件");
      return;
    }
    isSearch.value = true;
    searchData.value = res;
    if (options.search) {
      await http
        .post(options.search, {
          limit: limit.value,
          current: current.value,
          ...res,
          ...options.queryForm.value,
        })
        .then((res) => {
          tabLoading.value = false; // 加载完毕
          if (res.code == 200) {
            tableData.value = res.data
              ? res.data.records
                ? res.data.records
                : res.data
              : []; // 表格数据
            dataTotal.value = res.data.total || 0; // 总条数
          }
        })
        .catch(() => {
          tabLoading.value = false; // 加载完毕
        });

      return;
    }
    loadData();
  };

  // 表单重置
  const handleCancel = () => {
    if (options.queryForm && options.queryForm.value) {
      options.queryForm.value = {};
    }
    isSearch.value = options?.treeList ? false : true;
    searchData.value = {};
    current.value = 1;
    loadData();
  };

  // 表单排序
  const handleSort = (row: any, tableColumns: any) => {
    const item = tableColumns
      ? tableColumns.find((column: any) => column.prop === row.prop)
      : {};
    if (item?.sortProp) {
      sortField.value = item.sortProp;
    } else {
      sortField.value = row.prop;
    }
    const order = row.order;
    sortOrder.value = order == "ascending" ? 1 : order == "descending" ? 2 : "";
    if (!sortOrder.value) {
      sortField.value = "";
    }
    loadData();
  };

  //新增
  const handleAdd = (data: any) => {
    formDisabled.value = false;
    drawerVisible.value = true;
    drawerTitle.value = "新增";
    fromData.value = {
      status: 1,
      ...data,
    }; // 清空表单数据

    isSubmitType.value = "add"; // 提交类型
  };

  // 抽屉提交事件
  const handleDrawerSubmit = (data: any, other: any) => {
    return new Promise((resolve, reject) => {
      const url = isSubmitType.value == "add" ? options.add : options.edit;
      http
        .post(url, {
          ...data,
          ...other,
        })
        .then((res) => {
          if (res.code == 200) {
            plMessage(res.message, "success");
            drawerVisible.value = false;
            loadData(); // 刷新表格数据
            resolve(res);
          } else {
            reject(res);
          }
        })
        .catch(() => {
          reject();
        });
    });
  };

  // 查看详情
  const handleDetail = (data: any) => {
    formDisabled.value = true;
    fromData.value = data.row ? data.row : data;
    drawerVisible.value = true;
    drawerTitle.value = "详情";
  };

  // 编辑
  const handleEdit = (scope: any, data: any) => {
    const diyData = scope.row ? scope.row : scope;
    formDisabled.value = false;
    fromData.value = {
      ...diyData,
      ...data,
    };
    drawerVisible.value = true;
    drawerTitle.value = "编辑";
    isSubmitType.value = "edit";
  };

  // 删除
  const handleDelete = ({ row }: any, id: any) => {
    plMessageBox
      .confirm(options.del.message, "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
      .then(() => {
        console.log("删除");
        delData(row[id]);
      })
      .catch(() => {
        console.log("取消删除");
      });
  };

  /**
   * 删除函数
   */
  const delData = (id: any) => {
    // const queryParams = options.queryForm?.value || {};
    http.get(options.delete + id).then((res) => {
      if (res.code == 200) {
        plMessage(res.message, "success");
        loadData();
      }
    });
  };

  /**
   * 当前选择更改时的回调函数
   *
   * 此回调函数用于处理当前选择发生变化时的逻辑处理，主要功能是更新当前选择的值，
   * 并重新加载数据确保展示的信息是最新的。
   *
   * @param e 任何类型的选择事件，具体类型取决于触发该回调的元素
   */
  const currentChange = (e: any) => {
    // 更新当前选择的值，这里的e代表触发该回调的元素的值
    current.value = e;
    // 重新加载数据以反映新的当前选择
    loadData();
  };

  /**
   * 处理分页或加载更多时的尺寸变化
   *
   * @param e - 新的尺寸值，通常来自用户的选择或输入
   */
  const sizeChange = (e: any) => {
    limit.value = e; // 更新limit的值为新的尺寸
    loadData(); // 重新加载数据，应用新的尺寸限制
  };

  /**
   * 获取data数据
   */
  const getTableData = () => {
    return tableData.value;
  };
  return {
    treeData,
    isSubmitType,
    formDisabled,
    tabLoading,
    tableData,
    dataTotal,
    fromData,
    drawerTitle,
    drawerVisible,
    formRef,
    delData,
    current,
    sizeChange,
    currentChange,
    handleDrawerSubmit,
    handleCancel,
    handleSearch,
    handleDelete,
    handleAdd,
    loadData,
    handleDetail,
    handleEdit,
    getTableData,
    handleSort,
    sortField,
    sortOrder,
  };
}
