<template>
  <pl-card>
    <div class="card-flex">
      <!-- 禁止点击 start -->
      <div class="no-click" v-if="!pricingPlanId" @click="handleClick"></div>
      <!-- 禁止点击 end -->
      <pl-form
        :fields="rightColumns"
        :form="queryForm"
        inline
        confirmButtonText="搜索"
        cancelButtonText="重置"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
        <template #pricing_type>
          <pl-dict-select
            dict-code="pricing_type"
            v-model="queryForm.pricingType"
            multiple
          />
        </template>
      </pl-form>
      <!-- 新增按钮区域 -->
      <div>
        <pl-button
          type="primary"
          @click="handleAdd"
          v-has="'menu_calculate_list:btn_calculate_add'"
        >
          新增计价
        </pl-button>
      </div>
      <!-- 表格区域 -->
      <div class="card-table mt20">
        <pl-table
          :columns="rightTableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
        >
          <template #isDefault="{ scope }">
            <span>{{ scope.row.isDefault ? "是" : "否" }}</span>
          </template>
          <!-- 操作列插槽 -->
          <template #operate="{ scope }">
            <pl-button
              type="primary"
              link
              @click="rightHandleEdit(scope)"
              v-has="'menu_calculate_list:btn_calculate_edit'"
            >
              编辑
            </pl-button>
            <pl-button
              type="danger"
              link
              @click="handleDelete(scope, 'pricingId')"
              v-has="'menu_calculate_list:btn_calculate_del'"
            >
              删除
            </pl-button>
          </template>
        </pl-table>
      </div>
      <pl-pagination
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
    </div>

    <!-- 新增计价方案 start -->
    <drawerFrom
      ref="drawerFromRef2"
      v-model="drawerVisible"
      :title="drawerTitle"
      :fields="rightDrawerFields"
      :form="fromData"
      @submit="submitForm"
    >
      <template #people>
        <div class="people-box">
          <div class="people-input">
            <pl-input
              v-model="fromData.minPeople"
              :clearable="false"
            ></pl-input>
          </div>
          <div class="fh">-</div>
          <div class="people-input">
            <pl-input
              v-model="fromData.maxPeople"
              :clearable="false"
            ></pl-input>
          </div>
        </div>
      </template>
      <template #ticket_type>
        <pl-dict-select
          dict-code="ticket_type"
          @change="ticketTypeChange"
          v-model="fromData.ticketType"
        />
      </template>
      <template #baseFee>
        <div class="baseFee-box">
          <pl-select
            class="select"
            v-model="fromData.costModel"
            :options="[
              {
                label: '上涨',
                value: 1,
              },
              {
                label: '下降',
                value: 2,
              },
            ]"
          ></pl-select>
          <pl-input class="fee-input" v-model="fromData.costAdjustment" />
          <span class="unit">%</span>
        </div>
      </template>
    </drawerFrom>
  </pl-card>
</template>

<script setup>
import { ref, computed, nextTick, watch } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { useTable } from "@/hooks/usetTable";
import { getVehicleModelList, getPricingDetail } from "@/api";
import { getDictionaryData } from "@/api/dict";
import { plMessage } from "pls-common";
import _ from "lodash";
import { getCookie } from "@/utils/cookie";
/**
 * 定义一个引用类型变量，用于存储抽屉组件的引用。
 */
const drawerFromRef2 = ref(null);

/**
 * 处理点击事件的函数，当用户未选中左侧方案时，显示警告消息。
 */
const handleClick = () => {
  plMessage("请先选中左侧的方案", "warning");
};

/**
 * 定义组件的 props，接收 pricingPlanId 参数。
 * @param {Object} props - 组件的 props 对象。
 * @param {String|Number} props.pricingPlanId - 计价计划 ID，默认值为空字符串。
 */
const props = defineProps({
  pricingPlanId: {
    type: [String, Number],
    default: "",
  },
});

/**
 * 定义一个响应式对象，用于存储固定字段数据。
 */
const fixedField = ref({});

/**
 * 监听 pricingPlanId 的变化，更新 fixedField 并加载数据。
 */
watch(
  () => props.pricingPlanId,
  (newVal) => {
    fixedField.value.pricingPlanId = newVal;
    loadData();
  }
);

/**
 * 定义车辆型号列表，通过异步请求获取数据。
 */
let vehicleModelList = [];
getVehicleModelList({}).then((res) => {
  vehicleModelList = res.data;
});

/**
 * 定义右侧表格的列配置。
 */
const rightColumns = ref([
  {
    label: "计价类型",
    prop: "pricingType",
    template: "pricing_type",
  },
  {
    label: "计价名称",
    type: "input",
    prop: "pricingName",
  },
]);

/**
 * 定义右侧表格的列配置，包含更多详细信息。
 */
const rightTableColumns = ref([
  {
    label: "计价类型",
    prop: "pricingTypeName",
    width: 120,
  },
  {
    label: "是否默认计价",
    template: "isDefault",
    width: 120,
  },
  {
    label: "计价名称",
    prop: "pricingName",
    width: 120,
  },
  {
    label: "规则预览",
    prop: "rulePreview",
  },
  {
    label: "操作",
    template: "operate",
    setting: true,
    width: 120,
    fixed: "right",
  },
]);

/**
 * 定义时间相关的表单配置。
 */
const timeForm = [
  {
    label: "计价时间",
    prop: "JJSJ",
    type: "time-picker",
    format: "HH:mm:ss",
    rules: [{ required: true, message: "请选择计价时间", trigger: "blur" }],
    onChange: (v) => {
      fromData.value.startTime = v[0];
      fromData.value.endTime = v[1];
    },
  },
];

/**
 * 处理票种变化的函数，更新表单数据和标签。
 * @param {String} v - 票种值。
 * @param {Object} v2 - 票种对象，包含备注和标签。
 */
const ticketTypeChange = (v, v2) => {
  console.log(v, v2);
  fromData.value.ticketIntroduction = v2.remark;
  ticketLabel.value = v2.label;
};

/**
 * 定义票种相关的表单配置。
 */
const ticketForm = [
  {
    label: "票种种类",
    template: "ticket_type",
    rules: [{ required: true, message: "请选择票种种类", trigger: "blur" }],
  },
  {
    label: "票种介绍",
    prop: "ticketIntroduction",
    type: "input",
    rules: [{ required: true, message: "请输入票种介绍", trigger: "blur" }],
  },
];

/**
 * 定义人数相关的表单配置。
 */
const peopleForm = [
  {
    label: "人数设定",
    template: "people",
    prop: "people",
    rules: [
      {
        required: true,
        message: "请输入人数设定",
        trigger: "blur",
      },
      {
        validator: (rule, value, callback) => {
          if (fromData.value.minPeople || fromData.value.maxPeople) {
            fromData.value.people =
              fromData.value.minPeople || fromData.value.maxPeople;

            if (
              fromData.value.maxPeople &&
              fromData.value.minPeople > fromData.value.maxPeople
            ) {
              callback(new Error("最小人数不能大于最大人数"));
            }
            callback();
          } else {
            fromData.value.people = "";
          }
        },
      },
    ],
  },
];

/**
 * 定义车型相关的表单配置。
 */
const carForm = [
  {
    label: "车型",
    prop: "vehicleModelId",
    type: "select",
    options: vehicleModelList,
    filterable: true,
    valueKey: "vehicleModelId",
    labelKey: "vehicleModelName",
    rules: [{ required: true, message: "请选择车型", trigger: "blur" }],
    onChange: (v, v2) => {
      console.log(v, v2);
      vehicleModelLabel.value = v2.vehicleModelName;
    },
  },
];

/**
 * 定义日期相关的表单配置。
 */
const dateForm = [
  {
    label: "计价日期",
    prop: "JJRQ",
    type: "daterange",
    format: "YYYY-MM-DD",
    rules: [{ required: true, message: "请选择计价日期", trigger: "blur" }],
    onChange: (v) => {
      fromData.value.startDate = v[0];
      fromData.value.endDate = v[1];
    },
  },
];

/**
 * 定义查询表单的响应式对象。
 */
const queryForm = ref({});

/**
 * 提交表单的函数，处理数据并调用抽屉提交逻辑。
 * @param {Object} data - 表单数据。
 */
const submitForm = (data) => {
  const params = _.cloneDeep(data);

  params.costAdjustment = Number(params.costAdjustment) / 100;
  params.costAdjustmentType = 1;
  params.pricingPlanId = props.pricingPlanId;
  params.orgId = JSON.parse(getCookie("userInfo")).orgId;
  handleDrawerSubmit(params);
};

/**
 * 使用 useTable 钩子管理表格相关逻辑。
 */
const {
  dataTotal,
  tabLoading,
  tableData,
  fromData,
  drawerVisible,
  drawerTitle,
  sizeChange,
  currentChange,
  handleAdd,
  handleDelete,
  handleEdit,
  handleDrawerSubmit,
  handleSearch,
  handleCancel,
  loadData,
} = useTable({
  queryForm,
  fixedField: fixedField.value,
  noLoad: true,
  list: "/ticket/pricing/pagePricing",
  add: "/ticket/pricing/savaPricing",
  delete: "/ticket/pricing/deletePricing/",
  edit: "/ticket/pricing/updatePricing",
  del: {
    message: "确定要删除该计价规则的数据吗？",
  },
});

/**
 * 处理右侧表格编辑操作，获取计价详情并更新表单数据。
 * @param {Object} row - 当前行的数据。
 */
const rightHandleEdit = ({ row }) => {
  getPricingDetail({
    pricingId: row.pricingId,
  }).then((res) => {
    console.log(vehicleModelList);
    let data = _.cloneDeep(res.data);
    pricingType.value = data.pricingType;
    data.JJSJ =
      data.startTime && data.endTime ? [data.startTime, data.endTime] : [];
    data.JJRQ =
      data.startDate && data.endDate ? [data.startDate, data.endDate] : [];
    data.costAdjustment = data.costAdjustment * 100;
    data.people = data.minPeople || data.maxPeople;
    nextTick(() => {
      if (data.pricingType === "CX") {
        const item = rightDrawerFields.value.find(
          (item) => item.prop === "vehicleModelId"
        );
        item.options = vehicleModelList;
      }
      handleEdit(data);
    });
  });
};

/**
 * 定义计价类型的响应式变量。
 */
const pricingType = ref("");

/**
 * 定义票种标签的响应式变量。
 */
const ticketLabel = ref("");

/**
 * 定义车型标签的响应式变量。
 */
const vehicleModelLabel = ref("");

/**
 * 监听多个响应式变量的变化，动态生成规则预览。
 */
watch(
  [
    () => pricingType.value,
    () => fromData.value.startTime,
    () => fromData.value.endTime,
    () => fromData.value.minPeople,
    () => fromData.value.maxPeople,
    () => fromData.value.ticketType,
    () => fromData.value.vehicleModelId,
    () => fromData.value.startDate,
    () => fromData.value.endDate,
    () => fromData.value.costModel,
    () => fromData.value.costAdjustment,
  ],
  () => {
    let rulePreview = "";
    console.log(vehicleModelLabel.value);
    const startTime = fromData.value.startTime || "xxx";
    const endTime = fromData.value.endTime || "xxx";
    const minPeople = fromData.value.minPeople || "xxx";
    const maxPeople = fromData.value.maxPeople || "xxx";
    const ticketType = ticketLabel.value || "xxx";
    const vehicleModelId = vehicleModelLabel.value || "xxx";
    const startDate = fromData.value.startDate || "xxx";
    const endDate = fromData.value.endDate || "xxx";
    const costModel =
      fromData.value.costModel === 1
        ? "上涨"
        : fromData.value.costModel === 2
        ? "下降"
        : "xxx";
    const costAdjustment = fromData.value.costAdjustment || "xxx";

    switch (pricingType.value) {
      case "SJ": // 时间
        rulePreview = `在${startTime}到${endTime}范围内购票，在原票价基础上${costModel}${costAdjustment}%`;
        break;
      case "PZ": // 票种
        rulePreview = `${ticketType}购票，在原票价基础上${costModel}${costAdjustment}%`;
        break;
      case "RS": // 人数
        if (minPeople && maxPeople === "xxx") {
          rulePreview = `在大于${minPeople}人（包含${minPeople}人）范围内购票，在原票价基础上${costModel}${costAdjustment}%`;
        } else if (minPeople === "xxx" && maxPeople) {
          rulePreview = `在小于${maxPeople}人（包含${maxPeople}人）范围内购票，在原票价基础上${costModel}${costAdjustment}%`;
        } else {
          rulePreview = `在${minPeople}到${maxPeople}人范围内购票，在原票价基础上${costModel}${costAdjustment}%`;
        }
        break;
      case "CX": // 车型
        rulePreview = `选择${vehicleModelId}，在原票价基础上${costModel}${costAdjustment}%`;
        break;
      case "RQ": // 日期
        rulePreview = `在${startDate}到${endDate}范围内购票，在原票价基础上${costModel}${costAdjustment}%`;
        break;
      default:
        rulePreview = "";
    }

    fromData.value.rulePreview = rulePreview;
  }
);

/**
 * 动态计算表单配置，根据计价类型返回对应的表单字段。
 */
const DyForm = computed(() => {
  if (pricingType.value === "SJ") {
    return timeForm;
  } else if (pricingType.value === "PZ") {
    return ticketForm;
  } else if (pricingType.value === "RS") {
    return peopleForm;
  } else if (pricingType.value === "CX") {
    return carForm;
  } else if (pricingType.value === "RQ") {
    return dateForm;
  }
  return timeForm;
});

/**
 * 定义计价类型列表，通过异步请求获取字典数据。
 */
const pricingTypeList = ref([]);
getDictionaryData({ dictTypeCode: "pricing_type" }).then((res) => {
  const index = res.findIndex((item) => item.value === "FJF");
  res.splice(index, 1);
  pricingTypeList.value = res;
});

/**
 * 动态计算抽屉表单字段，根据计价类型动态调整字段配置。
 */
const rightDrawerFields = computed(() => [
  {
    label: "计价类型",
    prop: "pricingType",
    type: "select",
    options: pricingTypeList.value,
    rules: [{ required: true, message: "请选择计价类型", trigger: "blur" }],
    onChange: (v) => {
      pricingType.value = v;
      drawerFromRef2.value.clearValidateData();
      if (v === "CX") {
        const item = rightDrawerFields.value.find(
          (item) => item.prop === "vehicleModelId"
        );
        item.options = vehicleModelList;
      }
    },
  },
  {
    label: "计价名称",
    prop: "pricingName",
    type: "input",
    rules: [{ required: true, message: "请输入计价名称", trigger: "blur" }],
  },
  ...DyForm.value,
  {
    label: "基础费用",
    prop: "costModel",
    template: "baseFee",
    rules: [{ required: true, message: "请输入基础费用", trigger: "blur" }],
  },
  {
    label: "是否默认计价",
    type: "radio",
    prop: "isDefault",
    rules: [{ required: true, message: "请选择是否默认计价", trigger: "blur" }],
    options: [
      {
        label: "是",
        value: true,
      },
      {
        label: "否",
        value: false,
      },
    ],
  },
  {
    label: "规则预览",
    prop: "rulePreview",
    type: "textarea",
  },
]);
</script>

<style lang="scss" scoped>
.card-flex {
  position: relative;
}
.no-click {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}

.people-input {
  position: relative;
  &::after {
    position: absolute;
    content: "人";
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #ccc;
  }
}
.people-box {
  display: flex;
  .fh {
    margin: 0 5px;
  }
}
.baseFee-box {
  display: flex;
  width: 100%;
  position: relative;
  .unit {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
  .select {
    width: 200px;
    margin-right: 10px;
  }
  .fee-input {
    flex: 1;
  }
}
</style>
