<template>
  <div class="problem-handle">
    <div class="problem-left-box">
      <pl-scrollbar class="h100" v-loading="detailLoading">
        <div class="problem-box">
          <div class="title">问题详情</div>
          <div class="content">
            {{ content }}
          </div>
        </div>
        <div class="problem-box">
          <div class="title">问题回复</div>
          <div class="content">
            <textarea
              class="textarea"
              placeholder="请输入回复内容"
              v-model="formData.processPlan"
            ></textarea>

            <div class="upload-box">
              <pl-upload
                uploadType="image"
                v-model="formData.PicturePath"
                multi
              ></pl-upload>
            </div>
          </div>
        </div>
      </pl-scrollbar>

      <!-- 按钮 start -->
      <div class="btn-box">
        <pl-button type="primary" class="btn" @click="handleSubmit"
          >确认提交</pl-button
        >
      </div>
      <!-- 按钮 end -->
    </div>
    <div class="problem-right-log">
      <div class="log-title">
        <pl-icon name="ChatDotSquare" class="ChatDotSquare"></pl-icon>处理日志
      </div>
      <pl-scrollbar class="log-scroll-bar">
        <log :processList="processList" :disabled="true"></log>
      </pl-scrollbar>
    </div>
  </div>
</template>

<script setup>
import {
  getFeedBackDetail,
  getFeedBackProcessList,
  saveFeedBackProcess,
} from "@/api/index";
import log from "./log.vue";
import { onMounted, ref } from "vue";
import { plMessage, storage } from "pls-common";
import { getCookie } from "@/utils/cookie";
const emit = defineEmits(["refresh"]);
const props = defineProps({
  feedBackId: {
    type: String,
    default: "",
  },
  content: {
    type: String,
    default: "",
  },
});
const detailLoading = ref(true);
const processListLoading = ref(true);
const detail = ref({});
const processList = ref([]);
const formData = ref({
  processPlan: "",
  processSort: 1,
});
onMounted(() => {
  getFeedBackDetail({
    feedBackId: props.feedBackId,
  }).then((res) => {
    detail.value = res.data;
    detailLoading.value = false;
  });
  getLogList();
});

const getLogList = () => {
  getFeedBackProcessList({
    feedBackId: props.feedBackId,
    sortOrder: 1,
  }).then((res) => {
    processList.value = res.data;
    processListLoading.value = false;
  });
};
const handleSubmit = () => {
  if (!formData.value.processPlan) {
    plMessage("请输入回复内容", "error");
    return;
  }
  formData.value.processPicturePath = formData.value.PicturePath
    ? formData.value.PicturePath.split(",")
    : [];
  saveFeedBackProcess({
    feedBackId: props.feedBackId,
    ...formData.value,
    processBy: JSON.parse(getCookie("userInfo")).userId,
    processStatus: 1,
  }).then((res) => {
    if (res.code == 200) {
      plMessage("回复成功", "success");
      getLogList();
      emit("refresh");
    } else {
      plMessage(res.message, "error");
    }
  });
};
</script>

<style lang="scss" scoped>
.problem-handle {
  height: 100%;
  display: flex;
  padding-bottom: 20px;
  .problem-left-box {
    width: 70%;
    padding-right: 20px;
    position: relative;
    .btn-box {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 0 20px;
      display: flex;
      justify-content: center;
      .btn {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
  .problem-right-log {
    width: 30%;
    height: 100%;
    border-left: 1px dashed #ccc;
    position: relative;
    .log-title {
      font-size: 16px;
      font-weight: bold;
      line-height: 40px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      .ChatDotSquare {
        margin-right: 10px;
      }
    }
    .log-scroll-bar {
      position: absolute;
      left: 0;
      top: 40px;
      width: 100%;
      height: calc(100% - 40px);
    }
  }
}
.log-timeline {
  position: relative;
  padding-left: 20px;

  &::before {
    content: "";
    position: absolute;
    left: 40px;
    top: 0;
    bottom: 0;
    width: 0;
    border-left: 2px dashed #e8e8e8;
  }

  .log-item {
    position: relative;
    display: flex;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;

      &::after {
        content: "";
        position: absolute;
        left: 20px;
        top: 40px;
        bottom: 0;
        width: 2px;
        background-color: #fff;
        z-index: 1;
      }
    }

    .avatar {
      position: relative;
      z-index: 1;
      width: 40px;
      height: 40px;
      margin-right: 16px;

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        color: #fff;
        font-size: 14px;
      }

      .user-icon {
        background-color: #1890ff;
      }

      .merchant-icon {
        background-color: #52c41a;
      }

      .system-icon {
        background-color: #722ed1;
      }
    }

    .content {
      flex: 1;
      background: #fafafa;
      padding: 16px;
      border-radius: 4px;
      position: relative;
      .image-box {
        .image-item {
          width: 90px;
          height: 90px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .header {
        margin-bottom: 12px;

        .type {
          font-weight: bold;
          margin-right: 16px;
        }

        .time {
          color: #999;
          font-size: 14px;
        }
      }

      .info-row {
        margin-bottom: 8px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
        }

        .value {
          color: #333;
        }
      }
    }
  }
}
.problem-box {
  margin-bottom: 20px;
  .title {
    margin-bottom: 20px;
    font-weight: bold;
    position: relative;
    padding-left: 10px;
    font-size: 16px;
    &::after {
      position: absolute;
      content: "";
      width: 5px;
      height: 17px;
      background-color: var(--el-color-primary);
      left: 0;
      top: 3px;
    }
  }
  .content {
    font-size: 14px;
    color: #666;
    position: relative;
    .textarea {
      width: 100%;
      outline: none;
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 10px;
      height: 300px;
    }
    .upload-box {
      position: absolute;
      bottom: 15px;
      left: 10px;
    }
  }
}
</style>
