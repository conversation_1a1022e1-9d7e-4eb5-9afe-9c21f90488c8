<template>
  <div v-loading="tabLoading" class="h100">
    <pl-noData v-if="nodata" text="该角色没有配置菜单"></pl-noData>

    <template v-if="!nodata">
      <div class="tabs-box">
        <div
          class="tabs-item"
          v-for="(item, index) in tabs"
          :key="index"
          :class="item.label == tabCurrent ? 'active' : ''"
          @click="handleTabChange(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="permission-card">
        <div class="permission-tree">
          <pl-tree
            :data="treeData"
            :props="treeProps"
            @node-click="nodeclick"
          ></pl-tree>
        </div>
        <div class="permission-set">
          <div class="form-box">
            <div class="form-item">
              <label class="label">数据范围：</label>
              <div class="select-box">
                <pl-select
                  v-model="dataScope"
                  :options="dataScopeOptions"
                  @change="dataScopeChange"
                  :disabled="isClickDisabled"
                ></pl-select>
              </div>
            </div>
            <div class="form-item" v-if="dataScope == 4">
              <label class="label">指定机构：</label>
              <div class="select-box">
                <el-tree-select
                  v-model="orgId"
                  multiple
                  filterable
                  :data="orgList"
                  :props="{ label: 'orgName', value: 'id' }"
                  placeholder="请选择机构"
                ></el-tree-select>
              </div>
            </div>
            <div class="form-item" v-if="dataScope == 5">
              <label class="label">指定人员：</label>
              <div class="select-box">
                <pl-select
                  multiple
                  :options="userList"
                  label-key="label"
                  value-key="userId"
                  filterable
                  v-model="userIds"
                >
                  <template #option="{ item }">
                    {{ item.username }} - {{ item.postName }} -
                    {{ item.phone }} - {{ item.orgName }}
                  </template>
                </pl-select>
              </div>
            </div>
          </div>
          <div class="button-box" v-if="!isClickDisabled">
            <pl-button class="btn" type="primary" @click="handleSubmit"
              >确定</pl-button
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { ElTreeSelect } from "element-plus";
import {
  postRoleMenuTreeList,
  postQueryDataAuth,
  getParentOrgTreeList,
  getUserList,
  postSaveDataAuth,
} from "@/api/index";
import { plMessage } from "pls-common";
const props = defineProps({
  params: {
    type: Object,
    default: () => {},
  },
});
const orgId = ref([]);
const userIds = ref([]);
watch(
  () => props.params,
  (newVal) => {
    console.log(newVal);
    institutionChange(newVal);
  }
);
const tabCurrent = ref("");
const nodata = ref(false);
const treeProps = {
  label: "name",
  children: "children",
};
const tabLoading = ref(false);
const dataScopeOptions = ref([
  {
    label: "全机构",
    value: 1,
  },
  {
    label: "本机构",
    value: 2,
  },
  {
    label: "本机构及以下",
    value: 3,
  },
  {
    label: "指定机构",
    value: 4,
  },
  {
    label: "指定人员",
    value: 5,
  },
]);
const tabs = ref([]);
const treeData = ref([]);
const institutionChange = (item) => {
  tabLoading.value = true;
  tabs.value = [];
  postRoleMenuTreeList({
    roleId: item.roleId,
    dataPermissionStatus: 1,
  })
    .then((res) => {
      if (res.code === 200) {
        for (let k in res.data) {
          tabs.value.push({
            label: k,
            data: res.data[k],
          });
        }
        treeData.value = filterMenu(tabs.value[0].data);
        if (treeData.value.length > 0) {
          nodata.value = false;
        } else {
          nodata.value = true;
        }
        tabCurrent.value = tabs.value[0].label;
      } else {
        nodata.value = true;
      }
    })
    .finally(() => {
      tabLoading.value = false;
    });
};
institutionChange(props.params);

// 过滤掉没有权限的菜单
const filterMenu = (data) => {
  return data.filter((item) => {
    // 首先检查当前项是否有权限
    if (!item.authFlag) {
      return false;
    }

    // 如果有子项，递归过滤子项
    if (item.children && item.children.length) {
      item.children = filterMenu(item.children);
      // 如果过滤后没有子项，且当前项不是类型C的菜单，则不显示
      return item.children.length > 0 || item.menuType === "C";
    }

    // 如果是叶子节点，只返回类型为C的菜单
    return item.menuType === "C";
  });
};

const orgList = ref([]);
getParentOrgTreeList({}).then((res) => {
  orgList.value = res.data;
});

// 获取用户列表
const userList = ref([]);
getUserList({
  current: 1,
  limit: 1000,
}).then((res) => {
  if (res.code === 200) {
    res.data.records.map((item) => {
      item.label = item.username + " - " + item.phone;
      item.filterLabel =
        item.username + " - " + item.phone + " - " + item.orgName;
    });
    userList.value = res.data.records;
  }
});

const dataScope = ref("");
// 数据范围改变
const dataScopeChange = (v) => {
  dataScope.value = v;
  orgId.value = [];
  userIds.value = [];
};

// 菜单id
const menuId = ref("");
const rightBlock = ref(false);
const isClickDisabled = ref(true);
const nodeclick = (v1, v2) => {
  rightBlock.value = false;
  dataScope.value = "";
  orgId.value = [];
  userIds.value = [];
  isClickDisabled.value = true;
  if (v2.isLeaf) {
    isClickDisabled.value = false;
    menuId.value = v1.id;
    postQueryDataAuth({
      menuId: v1.id,
      roleId: props.params.roleId,
    }).then((res) => {
      if (res.code == 200) {
        rightBlock.value = true;
        if (res.data) {
          // 处理数据回显
          dataScope.value = res.data[0].dataScopeType;
          orgId.value = res.data[0].orgId
            ? res.data[0].orgId.split(",").map(Number)
            : [];
          userIds.value = res.data[0].userId
            ? res.data[0].userId.split(",").map(Number)
            : [];
        }
      }
    });
  }
};
// 保存数据权限
const handleSubmit = () => {
  if (dataScope.value == "") {
    plMessage("请选择数据范围", "warning");
    return;
  } else if (dataScope.value == 4 && orgId.value.length == 0) {
    plMessage("请选择机构", "warning");
    return;
  } else if (dataScope.value == 5 && userIds.value.length == 0) {
    plMessage("请选择人员", "warning");
    return;
  }
  postSaveDataAuth({
    dataScopeType: dataScope.value,
    menuId: menuId.value,
    roleId: props.params.roleId,
    orgId: orgId.value.join(","),
    userId: userIds.value.join(","),
    tenantId: props.params.tenantId,
  }).then((res) => {
    if (res.code == 200) {
      plMessage("保存成功", "success");
    } else {
      plMessage(res.message, "error");
    }
  });
};
</script>

<style lang="scss" scoped>
.h100 {
  display: flex;
  flex-direction: column;
}
.tabs-box {
  display: flex;
  height: 22px;
  .tabs-item {
    padding: 0 10px;
    cursor: pointer;
    position: relative;
    &.active {
      color: var(--el-color-primary);
      font-weight: bold;
      &::after {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        content: "";
        width: 100%;
        height: 2px;
        background-color: var(--el-color-primary);
      }
    }
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
.select-list {
  margin-top: 10px;
  .item {
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--el-color-primary);
    color: var(--el-color-primary);
    padding: 5px;
    border-radius: 5px;
    margin-right: 10px;
    font-size: 14px;
    .close-icon {
      margin-left: 10px;
      cursor: pointer;
      &:hover {
        color: red;
      }
    }
  }
}
.form-item {
  display: flex;
  margin-bottom: 10px;
  .select-box {
    flex: 1;
  }
  &.block {
    display: block;
    .form-item-box {
      display: flex;
      align-items: center;
    }
  }
  .label {
    width: 100px;
    font-size: 14px;
    color: var(--el-text-color-regular);
    padding-top: 5px;
    text-align: center;
  }
}
.permission-card {
  position: relative;
  display: flex;
  padding-top: 30px;
  flex: 1;
  .permission-tree {
    flex: 1;
    border-radius: 5px;
    padding: 15px;
    border: 1px solid var(--el-border-color);
  }
  .permission-set {
    flex: 1;
    border-radius: 5px;
    padding: 15px;
    margin-left: 10px;
    border: 1px solid var(--el-border-color);
    position: relative;
  }
}
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>
