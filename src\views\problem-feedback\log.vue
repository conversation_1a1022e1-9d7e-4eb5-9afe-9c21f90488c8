<template>
  <div class="log-timeline">
    <!-- 用户提交 -->
    <div class="log-item" v-for="(item, index) in list" :key="index">
      <div class="avatar">
        <span class="user-icon" v-if="item.processRoleName == '用户'">{{
          item.processRoleName
        }}</span>
        <span class="merchant-icon" v-if="item.processRoleName == '商家'">{{
          item.processRoleName
        }}</span>
        <span class="system-icon" v-if="item.processRoleName == '系统'">{{
          item.processRoleName
        }}</span>
      </div>
      <div class="content">
        <div class="header">
          <span class="type">{{
            item.processRoleName == "用户"
              ? "提出问题"
              : item.isEnd == 1
              ? "任务完结"
              : "处理问题"
          }}</span>
          <span class="time"
            >{{ item.processRoleName == "用户" ? "提出时间" : "处理时间" }}：{{
              item.createTime || item.processTime
            }}</span
          >
        </div>
        <div class="info-row" v-if="item.processRoleName !== '用户'">
          <span class="label">处理人：</span>
          <span class="value">{{
            item.processRoleName == "系统" ? "系统自动流程" : item.processByName
          }}</span>
        </div>
        <div class="info-row" v-if="item.processRoleName !== '系统'">
          <span class="label"
            >{{
              item.processRoleName == "用户" ? "提交问题" : "处理方案"
            }}：</span
          >
          <span class="value">{{
            item.isEnd == 1
              ? item.processRoleName == "系统"
                ? "系统自动流程"
                : item.processByName
              : item.content || item.processPlan
          }}</span>
        </div>

        <!-- 图片 start -->
        <pl-upload
          uploadType="image"
          v-if="item.picturePath && JSON.stringify(item.picturePath) != '[]'"
          v-model="item.picturePath"
          :showUpload="!disabled"
          :showDelete="!disabled"
        ></pl-upload>
        <!-- 图片 end -->
      </div>
    </div>

    <!-- 商家处理 -->
    <!-- <div class="log-item">
      <div class="avatar">
        <span class="merchant-icon">商家</span>
      </div>
      <div class="content">
        <div class="header">
          <span class="type">首次处理</span>
          <span class="time">处理时间：2025-01-01 01:00:00</span>
        </div>
        <div class="info-row">
          <span class="label">处理人：</span>
          <span class="value">XXX</span>
        </div>
        <div class="info-row">
          <span class="label">处理方案：</span>
          <span class="value"
            >尊敬的客户，您好！首先，我代表公司对您的不便深表歉意，我们非常理解您等待车辆时的焦急心情，对于车辆迟到且未提前通知给您带来的困扰，我们感到非常抱歉。我们已经对此次迟到的原因进行了调查，是因为具体原因，如交通拥堵、车辆突发故障等，这导致车辆无法按时到达。我深知这不能成为迟到的理由，但我们会从这次事件中吸取教训，避免类似情况再次发生。为了弥补您的不便，我们愿意先进提供以下补偿措施：[具体补偿措施，如下次服务折扣、赠送礼品等]。同时，我们会对相关责任人员进行严肃处理，并加强内部管理，确保今后能够按时为您提供服务。再次感谢您的理解与支持，如果您还有其他问题或建议，欢迎随时联系我们。祝您生活愉快！[您的全字/公司名称]
            [具体日期]</span
          >
        </div>
      </div>
    </div> -->

    <!-- 系统处理 -->
    <!-- <div class="log-item">
      <div class="avatar">
        <span class="system-icon">系统</span>
      </div>
      <div class="content">
        <div class="header">
          <span class="type">任务完结</span>
          <span class="time">处理时间：2025-01-03 01:00:00</span>
        </div>
        <div class="info-row">
          <span class="label">处理人：</span>
          <span class="value">系统自动流程</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { watch, ref } from "vue";
const props = defineProps({
  processList: {
    type: Array,
    default: () => [],
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const list = ref([]);

console.log(
  JSON.stringify([
    "https://bucket-30a8.xinan1.zos.ctyun.cn/2025-02-20/1740016004475_sshul_5b61d2bd228b22bcc435bef4576f3d7.png",
    "https://bucket-30a8.xinan1.zos.ctyun.cn/2025-02-20/1740016007403_1flyv_5b61d2bd228b22bcc435bef4576f3d7.png",
  ])
);

watch(
  () => props.processList,
  (newVal) => {
    newVal.forEach((item) => {
      if (item.picturePath) {
        console.log(item.picturePath);
        // item.picturePath = JSON.parse(item.picturePath).split(",");
      }
    });
    list.value = newVal;
  }
);
</script>

<style lang="scss" scoped>
.log-timeline {
  position: relative;
  padding-left: 20px;

  &::before {
    content: "";
    position: absolute;
    left: 40px;
    top: 0;
    bottom: 0;
    width: 0;
    border-left: 2px dashed #e8e8e8;
  }

  .log-item {
    position: relative;
    display: flex;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;

      &::after {
        content: "";
        position: absolute;
        left: 20px;
        top: 40px;
        bottom: 0;
        width: 2px;
        background-color: #fff;
        z-index: 1;
      }
    }

    .avatar {
      position: relative;
      z-index: 1;
      width: 40px;
      height: 40px;
      margin-right: 16px;

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        color: #fff;
        font-size: 14px;
      }

      .user-icon {
        background-color: #1890ff;
      }

      .merchant-icon {
        background-color: #52c41a;
      }

      .system-icon {
        background-color: #722ed1;
      }
    }

    .content {
      flex: 1;
      background: #fafafa;
      padding: 16px;
      border-radius: 4px;
      position: relative;
      .image-box {
        .image-item {
          width: 90px;
          height: 90px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .header {
        margin-bottom: 12px;

        .type {
          font-weight: bold;
          margin-right: 16px;
        }

        .time {
          color: #999;
          font-size: 14px;
        }
      }

      .info-row {
        margin-bottom: 8px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
        }

        .value {
          color: #333;
        }
      }
    }
  }
}
</style>
