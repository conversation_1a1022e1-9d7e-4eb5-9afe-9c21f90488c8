import router from "@/router";
import { storage } from "pls-common";
import { getDepotList } from "@/api/index";
import { pinyin } from "pinyin-pro"; // 修改导入方式
import { clearCookie } from "@/utils/cookie";
import { plMessage } from "pls-common";

export const autoCode = (str: string) => {
  if (!str) return "";
  // 使用 pinyin-pro 获取拼音首字母
  const result = pinyin(str, {
    pattern: "first", // 只获取首字母
    toneType: "none", // 不带声调
    type: "array", // 输出数组形式
  });
  return result.join("").toUpperCase();
};

/**
 * 日期格式化 转换成 yyyy-MM-dd HH:mm:ss
 */
export const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

/**
 * 应用类型
 */
export enum AppType {
  web = 1,
  app = 2,
  小程序 = 3,
}

/**
 * 根据给定的值在数组中查找对象，并返回该对象的中文名称。
 * @param items - 包含对象的数组
 * @param value - 用于查找的对象属性值
 * @returns 找到的对象的中文名称，如果未找到则返回 undefined
 */
export const findName = (v: any, v2: any) => {
  if (!v) return "暂无";

  const obj = v2.find((item: any) => item.value == v);
  if (obj) {
    return obj.label;
  }
};

/**
 * 根据给定的 ID 查找对应的对象，并将父级、父级的父级组合成一个数组
 *
 * @param {Array} arr - 树形数据
 * @param {string|number} id - 目标 ID
 * @returns {Array} 包含目标对象及其所有父级节点的数组
 */
export const findNodeAndParentsById = (
  arr: any[],
  id: string | number
): Array<any> => {
  if (!arr.length) return [];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].id === id) {
      return [arr[i].id];
    }
    if (arr[i].children) {
      const result = findNodeAndParentsById(arr[i].children, id);
      if (result.length) {
        return [arr[i].id].concat(result);
      }
    }
  }
  return [];
};

/**
 * 根据给定的 ID 查找对应的对象，并将父级、父级的父级组合成一个数组
 *
 * @param {Array} arr - 树形数据
 * @param {string|number} id - 目标 ID
 * @returns {Array} 包含目标对象及其所有父级节点的数组
 */
export const findNodeAndParentsByName = (
  arr: any[],
  id: string | number,
  key: string = "name"
): Array<any> => {
  if (!arr.length) return [];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].id === id) {
      return [arr[i][key]];
    }
    if (arr[i].children) {
      const result = findNodeAndParentsByName(arr[i].children, id);
      if (result.length) {
        return [arr[i][key]].concat(result);
      }
    }
  }
  return [];
};

/**
 * 递归提取按钮权限
 */
const extractButtonPerms = (menus: any[]): string[] => {
  let perms: string[] = [];
  if (!menus) return [];
  menus.forEach((menu) => {
    // 提取类型为 F (按钮) 的权限标识
    if (menu.menuType === "F" && menu.perms) {
      perms.push(menu.perms);
    }
    // 递归处理子菜单
    if (menu.children && menu.children.length > 0) {
      perms = perms.concat(extractButtonPerms(menu.children));
    }
  });

  return perms;
};

/**
 * 生成菜单
 */
export const generateRoutes = (menuList: any) => {
  const main = import.meta.glob("../components/layout/main.vue");
  const views = import.meta.glob("../views/**/*.vue");
  const menusList = [] as any;

  if (!menuList || menuList.length == 0) {
    plMessage("暂无此系统权限", "warning");
    return;
  }

  // 提取所有按钮权限
  const buttonPerms = extractButtonPerms(menuList);
  // 存储到 storage 中供后续使用
  storage.setItem("permissions", buttonPerms);

  // 对menuList进行排序
  const sortedMenuList = [...menuList].sort(
    (a, b) => (b.menuSort || 0) - (a.menuSort || 0)
  );

  sortedMenuList.forEach((menu: any, index: number) => {
    // 跳过状态为0的菜单和类型为F的按钮
    if (menu.status === 0 || menu.menuType === "F") return;
    const parentItem = {
      path: menu.path,
      name: menu.route_name ? menu.route_name : `parent${index}`,
      meta: {
        title: menu.menuName,
        icon: menu.icon,
        level: 1,
        parentRoute: 1,
        perms: menu.perms,
      },
      component: main[`../components/layout/main.vue`],
      children: [] as any,
      redirect: "",
    };

    if (menu.children && menu.children.length > 0) {
      const cdItem = [];
      // 对子菜单进行排序
      const sortedChildren = [...menu.children].sort(
        (a, b) => (b.menuSort || 0) - (a.menuSort || 0)
      );

      // 检查是否所有子项都是按钮类型
      const allChildrenAreButtons = sortedChildren.every(
        (child) => child.menuType === "F" || child.status === 0
      );

      if (allChildrenAreButtons) {
        // 如果所有子项都是按钮，将父级作为叶子节点处理
        const path_default = menu.path_default || "list";
        const childItem = {
          path: path_default,
          name: "child" + menu.route_name + index,
          component: views[`../views${menu.component}.vue`],
          meta: {
            title: menu.menuName,
            perms: menu.perms,
            level: 2,
            icon: menu.icon,
          },
        };
        parentItem.redirect = `${menu.path}/${path_default}`;
        parentItem.children.push(childItem);
      } else {
        // 正常处理非按钮类型的子菜单
        for (let i = 0; i < sortedChildren.length; i++) {
          const children = sortedChildren[i];
          // 跳过状态为0的菜单和类型为F的按钮
          if (children.status === 0 || children.menuType === "F") continue;
          const childItem = {
            path: children.path,
            name: children.route_name,
            component: views[`../views${children.component}.vue`],
            meta: {
              title: children.menuName,
              perms: children.perms,
              level: 2,
              icon: children.icon,
            },
          };
          cdItem.push(childItem);
        }
        parentItem.children = cdItem;
      }
    } else {
      // 没有子菜单，默认一个list路由
      const path_default = menu.path_default || "list";
      const childItem = {
        path: path_default,
        name: "child" + menu.route_name + index,
        component: views[`../views${menu.component}.vue`],
        meta: {
          title: menu.menuName,
          perms: menu.perms,
          level: 2,
          icon: menu.icon,
        },
      };
      parentItem.redirect = `${menu.path}/${path_default}`;
      parentItem.children.push(childItem);
    }
    menusList.push(parentItem);
  });

  removeOtherRoutes(router);
  menusList.forEach((item: any) => {
    router.addRoute(item);
  });
  console.log(menusList);
  return menusList;
};

/**
 * 移除除指定路由之外的所有路由
 */
export const removeOtherRoutes = (routes: any) => {
  const getRoutes = routes.getRoutes(); // 获取路由列表
  const parentRoute = getRoutes.filter(
    (item: any) => item.meta.parentRoute === 1
  );
  parentRoute.forEach((item: any) => {
    routes.removeRoute(item.name); // 移除路由
  });
};

/**
 * 退出登录
 */
export const logout = () => {
  storage.removeItem("menuList");
  storage.removeItem("userInfo");
  storage.removeItem("userAuthMenu");
  storage.removeItem("permissions");
  sessionStorage.clear();
  clearCookie("userInfo");
  setTimeout(() => {
    window.location.href = "/login";
  }, 1500);
};

/**
 * 根据站点坐标创建电子围栏
 * @param sites - 站点数组或单个站点对象，包含经纬度信息
 * @param radius - 围栏半径（单位：米），默认100米
 * @param shape - 围栏形状，'circle' 或 'square'，默认 'square'
 * @returns 添加了电子围栏的站点数组或站点对象
 */
export const createSquareFence = (
  sites: any[] | any,
  radius: number = 100,
  shape: "circle" | "square" = "square"
): any[] | any => {
  // 经度1度约等于111000米（地球赤道上）
  // 纬度1度约等于111000米
  const meterPerDegree = 111000;

  // 创建围栏的核心逻辑
  const createFence = (site: any) => {
    const lat = parseFloat(site.latitude); // 纬度
    const lng = parseFloat(site.longitude); // 经度

    let coordinates: number[][] = [];

    if (shape === "circle") {
      // 创建圆形围栏（用32个点近似表示圆形）
      const points = 32;
      for (let i = 0; i <= points; i++) {
        const angle = (i * 2 * Math.PI) / points;
        const latOffset = (radius * Math.sin(angle)) / meterPerDegree;
        const lngOffset =
          (radius * Math.cos(angle)) /
          (meterPerDegree * Math.cos((lat * Math.PI) / 180));
        coordinates.push([lng + lngOffset, lat + latOffset]);
      }
    } else {
      // 创建正方形围栏
      const latOffset = radius / meterPerDegree;
      const lngOffset =
        radius / (meterPerDegree * Math.cos((lat * Math.PI) / 180));

      coordinates = [
        [lng - lngOffset, lat + latOffset], // 左上
        [lng + lngOffset, lat + latOffset], // 右上
        [lng + lngOffset, lat - latOffset], // 右下
        [lng - lngOffset, lat - latOffset], // 左下
        [lng - lngOffset, lat + latOffset], // 闭合多边形
      ];
    }

    // 将坐标数组转换为指定格式的字符串
    const coordinateSet = JSON.stringify(
      coordinates.map(
        (coord) => coord.map((num) => Number(num.toFixed(6))) // 保留6位小数
      )
    );

    return {
      ...site,
      coordinateSet,
    };
  };

  // 判断输入是数组还是单个对象
  if (Array.isArray(sites)) {
    return sites.map((site) => createFence(site));
  } else {
    return createFence(sites);
  }
};

/**
 * 返乡专线 和 换线专线 的 起点和终点 的 选择
 * @param e
 * @returns
 */
export const utsTraveltypeOptions = async (e: string) => {
  let startStopId: any[] = [];
  let endStopId: any[] = [];

  if (e === "FXZX") {
    // 返乡专线：起点是学校，终点是区县
    const [startRes, endRes] = await Promise.all([
      getDepotList({
        stopMode: "DZ",
        label: "XX", // 学校
      }),
      getDepotList({
        stopMode: "DZ",
        label: "QX", // 区县
      }),
    ]);
    startStopId = startRes.data;
    endStopId = endRes.data;
  } else if (e === "HXZX") {
    // 换线专线：起点是区县，终点是学校
    const [startRes, endRes] = await Promise.all([
      getDepotList({
        stopMode: "DZ",
        label: "QX", // 区县
      }),
      getDepotList({
        stopMode: "DZ",
        label: "XX", // 学校
      }),
    ]);
    startStopId = startRes.data;
    endStopId = endRes.data;
  } else if (e === "BZZX" || e === "DZKY") {
    const res = await getDepotList({
      label: "QCZ",
      stopMode: "DZ",
    });
    startStopId = res.data;
    endStopId = res.data;
  }

  return {
    startStopId,
    endStopId,
  };
};

/**
 * 将米转换为公里，保留两位小数
 * @param meters 米数
 * @returns 公里数(保留两位小数)
 */
export const metersToKilometers = (meters: number): number => {
  return Number((meters / 1000).toFixed(2));
};

/**
 * 定制线路标签-中文
 * 支持单个标签或多个逗号分隔的标签
 */
export const getLabelName = (label: string) => {
  const labelMap = {
    QCZ: "汽车站",
    QY: "区域",
    XX: "学校",
    ZD: "站点",
    QX: "区县",
    CCD: "乘车点",
  } as any;

  // 检查是否有多个标签（用逗号分隔）
  if (label && label.includes(",")) {
    // 分割标签并转换每个标签
    return label
      .split(",")
      .map((item) => labelMap[item.trim()] || item.trim())
      .join("，"); // 使用中文逗号连接结果
  }

  // 单个标签的情况
  return labelMap[label] || label;
};
export const getLabelClass = (label: string) => {
  // 检查是否有多个标签（用逗号分隔）
  if (label && label.includes(",")) {
    return "merrage";
  }

  // 单个标签的情况
  return label;
};

/**
 * 获取颜色class
 */
export const getColor = (lineTypeCode: string) => {
  const colorMap = {
    SSGJ: "color-red",
    BXNK: "color-green",
    PQNK: "color-blue",
    DZKY: "color-yellow",
    BZZX: "color-purple",
    HXZX: "color-orange",
    FXZX: "color-pink",
  } as any;
  return colorMap[lineTypeCode] || "";
};
