<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <pl-button
          type="primary"
          @click="handleAdd()"
          v-has="'menu_notice_list:btn_add'"
          >新增</pl-button
        >
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <template #operation="{ scope }">
            <pl-button
              type="primary"
              link
              @click="clickEdit(scope.row)"
              v-has="'menu_notice_list:btn_edit'"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope, 'noticeId')"
              v-has="'menu_notice_list:btn_del'"
              >删除</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <pl-drawer v-model="drawerVisible" :title="drawerTitle">
        <addNotice
          :appList="appList"
          :noticeType="noticeType"
          v-if="drawerVisible"
          :from="fromData"
          @submit="drawerSubmit"
        ></addNotice>
      </pl-drawer>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useTable } from "@/hooks/usetTable";
import addNotice from "./add-notice.vue";
import { getAppList, noticeDetail } from "@/api";
import { getDictionaryData } from "@/api/dict";
import { getCookie } from "@/utils/cookie";

const noticeType = ref([]);
getDictionaryData({
  dictTypeCode: "notice_type",
}).then((res) => {
  noticeType.value = res;
});

const appList = ref([]); // 应用列表
getAppList().then((res) => {
  appList.value = res.data;
});
onMounted(() => {
  //组件加载完成后执行事件
});

// 加载状态控制
const tabLoading = ref(false);
// 查询表单的数据
const queryForm = ref({});

const clickEdit = (row) => {
  noticeDetail(row.noticeId).then((res) => {
    if (res.code == 200) {
      handleEdit(res.data);
    }
  });
};

// 使用 useTable 钩子管理表格相关逻辑
const {
  dataTotal,
  drawerVisible,
  drawerTitle,
  tableData,
  fromData,
  current,
  handleAdd,
  handleEdit,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
  handleDrawerSubmit,
} = useTable({
  list: "/traffic/notice/pageNotice",
  add: "/traffic/notice/saveNotice",
  edit: "/traffic/notice/updateNotice",
  delete: "/traffic/notice/deleteNotice/",
  del: {
    message: "确定删除该公告吗？",
  },
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "发布人",
    prop: "createByName",
  },
  {
    label: "所属应用",
    prop: "appCodeList",
    options: appList,
    type: "select",
    placeholder: "请输入名称",
    valueKey: "appCode",
    labelKey: "appName",
    multiple: true,
  },
  {
    label: "公告类型",
    prop: "noticeTypeLis",
    type: "select",
    placeholder: "请输入名称",
    options: [
      { label: "通知", value: 1 },
      { label: "公告", value: 2 },
    ],
  },
]);

/**
 * 处理抽屉提交事件的方法
 * @param {Event} e - 提交事件对象
 */
const drawerSubmit = (e) => {
  const params = {
    ...e,
  };
  // 输出提交事件对象到控制台，用于调试目的
  params.orgId = JSON.parse(getCookie("userInfo")).orgId;
  if (params.isAllRecipient) {
    delete params.recipientIds;
  }
  handleDrawerSubmit(params);
};

// 表格列配置
const tableColumns = ref([
  { label: "序号", type: "index", width: 80 },
  { label: "标题", prop: "title", sortable: "custom" },
  { label: "发布人", prop: "createByName", sortable: "custom" },
  { label: "所属应用", prop: "createByName", sortable: "custom" },
  { label: "公告类型", prop: "noticeTypeName", sortable: "custom" },
  { label: "接收人", prop: "createByName", sortable: "custom" },
  { label: "发布时间", prop: "createTime", sortable: "custom" },
  {
    label: "操作",
    template: "operation",
    width: 150,
    fixed: "right",
    setting: true,
  },
]);
</script>

<style lang="scss" scoped></style>
