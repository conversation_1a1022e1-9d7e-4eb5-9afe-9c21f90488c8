<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="balanceColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <template #status="{ scope }">
            <div>
              {{ scope.row.status === 0 ? "待结算" : "已结算" }}
            </div>
          </template>
          <template #operation="{ scope }">
            <pl-button type="primary" link @click="detail(scope.row)">
              明细
            </pl-button>
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        v-model="drawerVisible"
        :title="drawerTitle"
        @submit="handleDrawerSubmit()"
      >
        <div class="drawer">
          <!-- 筛选表单 -->
          <pl-form
            confirmButtonText="搜索"
            cancelButtonText="重置"
            :fields="drawerFormColumns"
            :form="drawerQueryForm"
            inline
            :span="6"
            clear
            @confirm="handleDrawerSearch"
            @cancel="handleDrawerCancel"
          >
          </pl-form>
          <!-- 操作按钮 -->
          <div class="draw-btns">
            <div>
              <pl-button type="primary" @click="batchConfirmSet">
                批量结算
              </pl-button>
            </div>
            <div>
              <pl-button
                type="primary"
                @click="exportDetail"
                :loading="btnLoading"
                >导出</pl-button
              >
            </div>
          </div>
          <!-- 金额统计 -->
          <div class="price">
            <div class="price-item">
              <div class="price-item-title">订单金额</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.receivableAmount) }}
              </div>
            </div>
            <div class="price-line"></div>
            <div class="price-item">
              <div class="price-item-title">实收金额</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.receiptAmount) }}
              </div>
            </div>
            <div class="price-line"></div>
            <div class="price-item">
              <div class="price-item-title">退款金额</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.refundAmount) }}
              </div>
            </div>
            <div class="price-line"></div>
            <div class="price-item">
              <div class="price-item-title">信息服务费</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.informationServiceAmount) }}
              </div>
            </div>
            <div class="price-line"></div>
            <div class="price-item">
              <div class="price-item-title">提成金额</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.commissionAmount) }}
              </div>
            </div>
            <div class="price-line"></div>
            <div class="price-item">
              <div class="price-item-title">结算金额</div>
              <div class="price-item-value">
                ￥{{ priceFormat(curRow?.settlementAmount) }}
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <pl-table
            ref="drawerTableRef"
            :columns="drawerColumns"
            :data="drawerTableData"
            class="table"
            v-loading="drawerLoading"
            @selection-change="handleSelectionChange"
          >
            <!-- 实收金额 -->
            <template #receiptAmount="{ scope }">
              <div class="table-detail" @click="detailTable(scope, 'receipt')">
                <div>￥{{ priceFormat(scope.row.receiptAmount) }}</div>
                <div class="table-detail-tag" v-if="scope.row.receiptAmount">
                  明细
                </div>
              </div>
            </template>
            <!-- 退款金额 -->
            <template #refundAmount="{ scope }">
              <div class="table-detail" @click="detailTable(scope, 'refund')">
                <div>￥{{ priceFormat(scope.row.refundAmount) }}</div>
                <div class="table-detail-tag" v-show="scope.row.refundAmount">
                  明细
                </div>
              </div>
            </template>
            <!-- 状态 -->
            <template #status="{ scope }">
              <div>
                {{ scope.row.status === 0 ? "待结算" : "已结算" }}
              </div>
            </template>
            <!-- 操作 -->
            <template #operation="{ scope }">
              <pl-button
                type="primary"
                link
                @click="drawConfirmSet(scope.row)"
                v-if="scope.row.status == 0"
              >
                确认结算
              </pl-button>
            </template>
          </pl-table>
          <!-- 分页组件开始 -->
          <pl-pagination
            :currentPage="drawerQueryForm.current"
            :total="drawerQueryForm.total"
            @size-change="drawerSizeChange"
            @current-change="drawerCurrentChange"
          ></pl-pagination>
          <!-- 分页组件结束 -->
        </div>
      </drawerFrom>

      <!-- 明细弹窗 -->
      <pl-dialog
        v-model="detailDialogVisible"
        :title="detailDialogTitle"
        width="1300px"
        @confirm="detailDialogVisible = false"
      >
        <template #content>
          <pl-table
            :columns="dialogColumns"
            :data="dialogData"
            class="table"
            v-loading="dialogLoading"
            v-if="detailDialogVisible"
          >
            <!-- 核销方式 -->
            <template #verificationWay="{ scope }">
              <div v-if="scope.row.verificationWay == 0">自动确认</div>
              <div v-if="scope.row.verificationWay == 1">手动确认</div>
            </template>
            <!-- 创建方式 -->
            <template #createWay="{ scope }">
              <div v-if="scope.row.verificationWay == 0">自动创建</div>
              <div v-if="scope.row.verificationWay == 1">手动创建</div>
            </template>
            <!-- 交易凭证 -->
            <template #voucherPath="{ scope }">
              <pl-image
                style="height: 60px; width: 60px"
                :src="scope.row.voucherPath"
              />
            </template>
          </pl-table>
        </template>
      </pl-dialog>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { balanceColumns } from "./config";
import { useTable } from "@/hooks/usetTable";
import {
  balanceDetailList,
  getSettlementCycle,
  confirmSettlement,
  exportSettlementItem,
  actualList,
  refundList,
} from "@/api";
import { plMessageBox, plMessage } from "pls-common";

onMounted(() => {
  //组件加载完成后执行事件
  getCycleList();
});
// 获取周期日期
const setCycleDate = ref([]);
const getCycleList = async () => {
  const res = await getSettlementCycle();
  setCycleDate.value = res.data.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
  console.log(setCycleDate.value);
};

// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  tableData,
  current,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/pay/settlement/pageSettlement",
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "结算周期",
    prop: "settlementCycleDate",
    type: "select",
    options: setCycleDate,
    placeholder: "请选择结算周期",
    clearable: true,
    multiple: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "结算状态",
    prop: "status",
    type: "select",
    options: [
      { label: "待结算", value: 0 },
      { label: "已结算", value: 1 },
    ],
  },
  {
    label: "创建时间",
    prop: "createTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (val) => {
      if (val && val.length === 2) {
        queryForm.value.startCreateTime = val[0] + " 00:00:00";
        queryForm.value.endCreateTime = val[1] + " 23:59:59";
      }
    },
  },
]);

// ^ 点击明细
const curRow = ref(); // 当前结算日期
const detail = (row) => {
  curRow.value = row;
  drawerVisible.value = true;
  console.log("curRow", curRow.value);

  getDetailList();
};
// 获取明细列表
// 明细表单筛选配置
const drawerQueryForm = ref({
  current: 1,
  limit: 10,
  total: 0,
});
const getDetailList = async () => {
  drawerLoading.value = true;
  try {
    const params = {
      ...drawerQueryForm.value,
      settlementCycleDate: curRow.value?.settlementCycleDate,
    };
    const res = await balanceDetailList(params);
    drawerTableData.value = res.data.records;
    drawerQueryForm.value.total = res.data.total;
    console.log(drawerQueryForm.value.total);
    // 清空选中数据
    selectedRows.value = [];
  } catch (error) {
    console.log(error);
  } finally {
    drawerLoading.value = false;
  }
};
const drawerSizeChange = (size) => {
  drawerQueryForm.value.limit = size;
  getDetailList();
};
const drawerCurrentChange = (current) => {
  drawerQueryForm.value.current = current;
  getDetailList();
};

const drawerFormColumns = ref([
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "结算单号",
    prop: "settlementNo",
    type: "input",
  },
  {
    label: "应收单号",
    prop: "receivableNo",
    type: "input",
  },
  {
    label: "结算状态",
    prop: "status",
    type: "select",
    options: [
      {
        label: "待结算",
        value: 0,
      },
      {
        label: "已结算",
        value: 1,
      },
    ],
  },
]);
// 明细表单表格配置
const drawerLoading = ref(false);
const drawerTableData = ref([]);
const drawerTableRef = ref(null);
const selectedRows = ref([]); // 选中的行数据
const drawerColumns = ref([
  { type: "selection", width: 55 },
  { label: "序号", type: "index", width: 60 },
  {
    label: "结算单号",
    prop: "settlementNo",
    minWidth: 160,
  },
  {
    label: "应收单号",
    prop: "receivableNo",
    minWidth: 160,
  },
  {
    label: "关联订单号",
    prop: "orderNo",
    minWidth: 160,
  },
  {
    label: "订单金额（元）",
    prop: "receivableAmount",
    price: true,
    align: "center",
    minWidth: 160,
  },
  {
    label: "实收金额(元）",
    prop: "receiptAmount",
    template: "receiptAmount",
    minWidth: 130,
  },
  {
    label: "退款金额(元）",
    prop: "refundAmount",
    template: "refundAmount",
    align: "center",
    minWidth: 130,
  },
  {
    label: "信息服务费(元)",
    prop: "informationServiceAmount",
    price: true,
    align: "center",
    minWidth: 160,
  },
  {
    label: "提成金额（元）",
    prop: "commissionAmount",
    price: true,
    align: "center",
    minWidth: 160,
  },
  {
    label: "结算金额（元）",
    prop: "settlementAmount",
    price: true,
    align: "center",
    minWidth: 160,
  },
  {
    label: "收款方名称",
    prop: "payeeName",
    minWidth: 160,
  },
  {
    label: "结算周期",
    prop: "settlementCycleDate",
    minWidth: 160,
  },
  {
    label: "结算状态",
    prop: "status",
    template: "status",
    minWidth: 120,
  },
  {
    label: "备注",
    prop: "remark",
    minWidth: 160,
  },
  { label: "操作", template: "operation", minWidth: 120, fixed: "right" },
]);

// 表格选择变化处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 表单搜索/重置
const handleDrawerSearch = () => {
  getDetailList();
};
const handleDrawerCancel = () => {
  drawerQueryForm.value = {
    current: 1,
    limit: 10,
  };
  getDetailList();
};

// 单个确认结算
const drawConfirmSet = (row) => {
  plMessageBox
    .confirm("是否确认结算？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(async () => {
      try {
        const params = {
          settlementId: curRow.value?.settlementId,
          settlementItemIdList: [row.settlementItemId],
        };
        interfaceSet(params);
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};
// 批量确认结算
const batchConfirmSet = () => {
  let settlementItemIdList = [];
  if (selectedRows.value.length === 0) {
    settlementItemIdList = drawerTableData.value
      .filter((i) => i.status == 0)
      .map((item) => item.settlementItemId);
  } else {
    settlementItemIdList = selectedRows.value
      .filter((i) => i.status == 0)
      .map((item) => item.settlementItemId);
  }
  if (settlementItemIdList.length === 0) {
    plMessage("暂无待结算的数据", "warning");
    return;
  }

  plMessageBox
    .confirm("是否确认结算？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(async () => {
      try {
        const params = {
          settlementId: curRow.value?.settlementId,
          settlementItemIdList,
        };
        interfaceSet(params);
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};

const interfaceSet = async (params) => {
  try {
    const res = await confirmSettlement(params);
    if (res.code == 200) {
      plMessage("确认收款成功", "success");
      getDetailList();
    }
  } catch (error) {
    console.log(error);
  }
};

// 导出明细
const btnLoading = ref(false);
const exportDetail = () => {
  if (!drawerTableData.value.length) {
    plMessage("暂无可导出的数据", "warning");
    return;
  }

  let settlementItemIdList = [];
  if (selectedRows.value.length === 0) {
    settlementItemIdList = [];
  } else {
    settlementItemIdList = selectedRows.value.map(
      (item) => item.settlementItemId
    );
  }

  btnLoading.value = true;
  const params = {
    ...drawerQueryForm.value,
    settlementCycleDate: curRow.value?.settlementCycleDate,
    settlementId: curRow.value?.settlementId,
    settlementItemIdList,
  };
  exportSettlementItem(params)
    .then((res) => {
      if (res.code == 200) {
        window.open(res.data);
        plMessage(res.message, "success");
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

// ^ 明细弹窗
const detailDialogTitle = ref("");
const detailDialogVisible = ref(false);
const dialogData = ref([]);
const dialogLoading = ref(false);
const detailTable = async (scope, type) => {
  dialogData.value = [];
  dialogColumns.value = [];
  detailDialogTitle.value = type === "receipt" ? "实收明细" : "退款明细";

  //   实收
  if (type === "receipt") {
    console.log("curRow", scope);

    const res = await actualList({
      current: 1,
      limit: 100,
      tenantId: 10035,
      isVerificationPage: false,
      settlementItemId: scope.row.settlementItemId,
    });
    dialogData.value = res.data.records;
    dialogColumns.value = [
      {
        label: "实收单号",
        prop: "receiptNo",
        sortable: "custom",
        minWidth: 160,
      },
      { label: "订单号", prop: "orderNo", sortable: "custom", minWidth: 160 },
      {
        label: "订单类型",
        prop: "orderTypeName",
        sortProp: "orderType",
        sortable: "custom",
        width: 180,
      },
      { label: "付款方名称", prop: "payerName", width: 180 },
      {
        label: "实收金额（元）",
        prop: "receiptAmount",
        sortable: "custom",
        price: true,
        width: 180,
      },
      {
        label: "未核销金额（元）",
        prop: "unverifiedAmount",
        sortable: "custom",
        price: true,
        width: 180,
      },
      {
        label: "支付账户",
        prop: "payerBankNo",
        sortable: "custom",
        width: 180,
      },
      {
        label: "收款状态",
        prop: "receiptStatusName",
        sortProp: "receiptStatus",
        sortable: "custom",
        width: 180,
      },
      {
        label: "收款方式",
        prop: "paymentWayName",
        sortProp: "paymentWay",
        sortable: "custom",
        width: 180,
      },
      {
        label: "交易流水号",
        prop: "transactionNo",
        sortable: "custom",
        width: 180,
      },
      {
        label: "核销状态",
        prop: "verificationStatusName",
        sortProp: "verificationStatus",
        sortable: "custom",
        width: 180,
      },
      {
        label: "核销时间",
        prop: "verificationTime",
        sortable: "custom",
        width: 180,
      },
      {
        label: "业务类型",
        prop: "businessTypeName",
        sortProp: "businessType",
        sortable: "custom",
        width: 180,
      },
      //   { label: "关联合同", prop: "name", sortable: "custom", width: 180 },
      { label: "操作人", prop: "updateName", width: 180 },
      { label: "账单备注", prop: "remark", width: 180 },
      {
        label: "核销方式",
        prop: "verificationWay",
        sortable: "custom",
        template: "verificationWay",
        width: 180,
      },
      {
        label: "创建方式",
        prop: "createWay",
        sortable: "custom",
        template: "createWay",
        width: 180,
      },
      { label: "创建时间", prop: "createTime", sortable: "custom", width: 180 },
      { label: "更新时间", prop: "updateTime", sortable: "custom", width: 180 },
      {
        label: "交易凭证",
        prop: "voucherPath",
        sortable: "custom",
        template: "voucherPath",
        width: 180,
      },
    ];
  } else {
    // 退款
    const res = await refundList({
      current: 1,
      limit: 100,
      tenantId: 10035,
      settlementItemId: scope.row.settlementItemId,
    });
    dialogData.value = res.data.records;
    dialogColumns.value = [
      {
        label: "序号",
        type: "index",
        minWidth: 80,
      },
      {
        label: "退款单号",
        prop: "refundRecordNo",
        sortable: "custom",
        minWidth: 140,
      },
      {
        label: "订单号",
        prop: "refundNo",
        sortable: "custom",
        minWidth: 150,
      },
      {
        label: "退款金额(元)",
        prop: "refundAmount",
        sortable: "custom",
        price: true,
        minWidth: 130,
        align: "center",
      },
      {
        label: "退款方式",
        prop: "refundWay",
        sortable: "custom",
        minWidth: 120,
      },
      {
        label: "退款流水号",
        prop: "transactionNo",
        sortable: "custom",
        minWidth: 140,
      },
      {
        label: "退款时间",
        prop: "refundTime",
        sortable: "custom",
        minWidth: 180,
      },
      {
        label: "退款原因",
        prop: "refundReason",
        sortable: "custom",
        minWidth: 160,
      },
      {
        label: "退款状态",
        prop: "refundStatusName",
        sortable: "custom",
        minWidth: 110,
      },
      {
        label: "审核人",
        prop: "reviewUserName",
        minWidth: 130,
      },
      {
        label: "退款通知时间",
        prop: "notifyTime",
        sortable: "custom",
        minWidth: 150,
      },
      {
        label: "退款操作人",
        prop: "refundUserName",
        minWidth: 120,
        align: "center",
      },
      {
        label: "业务类型",
        prop: "businessTypeName",
        sortable: "custom",
        minWidth: 120,
        align: "center",
      },
      {
        label: "创建时间",
        prop: "createTime",
        sortable: "custom",
        minWidth: 150,
        align: "center",
      },
      {
        label: "更新时间",
        prop: "updateTime",
        sortable: "custom",
        minWidth: 180,
        align: "center",
      },
      {
        label: "交易凭证",
        prop: "voucherPath",
        template: "voucherPath",
        minWidth: 280,
        align: "center",
      },
    ];
  }
  detailDialogVisible.value = true;
};
// 明细列表
const dialogColumns = ref([]);

const handleDrawerSubmit = () => {
  drawerVisible.value = false;
};

// 金额格式化
const priceFormat = (value, decimal) => {
  if (!decimal) {
    decimal = 2;
  }
  // 如果值为空或undefined，直接返回
  if (value === null || value === undefined || value === "") {
    return value;
  }

  let numValue = value;

  // 如果是字符串，转换为数字并校验
  if (typeof value === "string") {
    // 移除可能的空格和特殊字符
    const cleanValue = value.trim().replace(/[^\d.-]/g, "");
    numValue = parseFloat(cleanValue);

    // 如果转换失败，返回原值
    if (isNaN(numValue)) {
      return value;
    }
  }

  // 如果是数字类型，直接使用
  if (typeof numValue === "number") {
    // 检查数字的绝对值是否超过1000
    if (Math.abs(numValue) >= 1000) {
      // 超过1000的数字，添加千分位分隔符
      return numValue.toLocaleString("en-US", {
        minimumFractionDigits: decimal,
        maximumFractionDigits: decimal,
      });
    } else {
      // 不超过1000的数字，正常展示
      return numValue.toFixed(decimal);
    }
  }

  // 其他情况返回原值
  return value;
};
</script>

<style lang="scss" scoped>
.draw-btns {
  display: flex;
  justify-content: space-between;
  padding: 0 10px 0 0;
}

.table-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  .table-detail-tag {
    color: #409eff;
    cursor: pointer;
    background-color: #f0f2f5;
    padding: 0 8px;
    border-radius: 4px;
    margin-left: 6px;
    font-size: 12px;
    margin-left: 10px;
  }
}

.price {
  display: flex;
  align-items: center;
  margin: 10px 0;
  .price-item {
    display: flex;
    align-items: center;
    .price-item-title {
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px;
    }
    .price-item-value {
      font-size: 14px;
    }
  }
  .price-line {
    width: 1px;
    height: 10px;
    background-color: #ccc;
    margin: 0 10px;
  }
}
</style>
