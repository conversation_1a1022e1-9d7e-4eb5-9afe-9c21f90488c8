<!-- 机构管理 -->
<template>
  <div class="organize-box">
    <div class="org-search">
      <pl-input placeholder="请输入关键字查询" v-model="searchKey"></pl-input>
      <pl-button class="search-btn" type="primary" plain @click="handleSearch"
        >搜索</pl-button
      >
    </div>
    <div class="org-list">
      <div class="list-item-box">
        <pl-scrollbar>
          <div
            v-for="(item, index) in tenantList"
            :key="index"
            class="list-item-parent"
          >
            <div
              class="org-item"
              :class="activeIndex == index ? 'active' : ''"
              @click="handleClick(index, item)"
            >
              <div class="tenant-parent">
                <div
                  class="pl-font pl-icon-a-qietu42 icon"
                  v-if="item.tenantType === 0"
                ></div>
                <div
                  class="pl-font pl-icon-a-qietu43 icon"
                  v-else-if="item.tenantType === 1"
                ></div>
                <div
                  class="pl-font pl-icon-a-qietu44 icon"
                  v-else-if="item.tenantType === 2"
                ></div>
                {{ item.tenantName }}
              </div>
            </div>
            <div
              class="tenant-children"
              :class="activeIndex == index ? 'active' : ''"
            >
              <div
                class="item"
                :class="roleId == itemChild.roleId ? 'active' : ''"
                v-for="itemChild in item.sysRoleVOList"
                :key="itemChild.roleId"
                @click="itemChildClick(itemChild.roleId)"
              >
                <span>{{ itemChild.roleName }}</span>
              </div>
            </div>
          </div>
        </pl-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getTenantList } from "@/api/index";
let roleId = ref(""); // 当前选中的角色id
// 搜索
const searchKey = ref("");
/**
 * 搜索租户
 */
const handleSearch = () => {
  getList();
};

const tenantList = ref([]);
/**
 * 获取租户列表
 */
const getList = (cb) => {
  getTenantList({
    //   isDeedRoleList: true,
    tenantName: searchKey.value || "",
    roleList: true,
  }).then((res) => {
    let list = [];
    for (let k in res.data) {
      list = list.concat(res.data[k]);
    }
    tenantList.value = list;
    if (cb) {
      return cb();
    }
  });
};
getList(() => {
  roleId.value = tenantList.value[0].sysRoleVOList[0].roleId;
  if (roleId.value) {
    emit("change", roleId.value);
  }
});

let activeIndex = ref(0);

const emit = defineEmits(["change"]);

const handleClick = (index) => {
  activeIndex.value = index;
};

/**
 * 租户子集点击事件
 */
const itemChildClick = (id) => {
  roleId.value = id;
  emit("change", id);
};
</script>

<style scoped lang="scss">
.organize-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  .org-list {
    flex: 1;
    position: relative;

    .list-item-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }

    .org-item {
      padding-left: 20px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 5px;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      .pl-icon-a-qietu42 {
        color: #ff4c51ff;
      }
      .pl-icon-a-qietu43 {
        color: #ffb400ff;
      }
      .pl-icon-a-qietu44 {
        color: #666666ff;
      }
      .icon {
        margin-right: 10px;
      }
      &:hover {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary) !important;
        .icon {
          color: var(--el-color-primary) !important;
        }
      }

      &.active {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary) !important;
        .icon {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }
}

.org-list {
  margin-top: 15px;

  .org-item {
    padding: 10px 0;
    color: #999;
    font-size: 14px;
  }
}

.org-search {
  display: flex;

  .search-btn {
    margin-left: 10px;
  }
}
.tenant-parent {
  display: flex;
  align-items: center;
}
.tenant-children {
  max-height: 0;
  overflow: hidden;
  font-size: 14px;
  color: #999;
  transition: all 0.5s;
  .item {
    padding-bottom: 10px;
    padding-top: 10px;
    padding-left: 50px;
    &.active {
      color: var(--el-color-primary);
      font-weight: bold;
      // background-color: var(--el-color-primary-light-8);
    }
    &:hover {
      cursor: pointer;
      color: var(--el-color-primary);
    }
  }
  &.active {
    max-height: 1000px;
  }
}

.list-item-parent {
  &.active {
    // border: 1px solid var(--el-color-primary);
  }
}
</style>
