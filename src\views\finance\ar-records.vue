<!-- 财务管理-应收记录 -->
<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="reset()"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <pl-button type="primary" @click="handleExport" :loading="btnLoading"
          >导出</pl-button
        >
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="arConfColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort($event, arConfColumns)"
          @selection-change="selectionChange"
        >
          <!-- 订单类型 -->
          <template #orderType="{ scope }">
            <div class="order-type" :class="scope.row.orderType">
              {{ scope.row.orderTypeName }}
            </div>
          </template>
          <!-- 操作 -->
          <template #operation="{ scope }">
            <pl-button type="primary" link @click="handleEdit(scope.row)"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope.row, '设置成需要删除的id名字')"
              >删除</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit(event)"
      >
      </drawerFrom>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { arConfColumns } from "./config/config";
import { useTable } from "@/hooks/usetTable";
import { getBusinessType, arList } from "@/api/index";
import { getDictionaryData } from "@/api/dict";
import { plMessage } from "pls-common";
import PAY from "@/api/pay";

onMounted(() => {
  getDictionaryData({
    dictTypeCode: "order_type",
  }).then((res) => {
    orderType.value = res;
  });
  getDictionaryData({
    dictTypeCode: "verification_status",
  }).then((res) => {
    verificationStatus.value = res;
  });

  getBusType();
});

// 获取业务类型
const businessType = ref([]); //业务类型
const getBusType = async () => {
  const res = await getBusinessType();
  businessType.value = res.data;
};

// 重置
const reset = async () => {
  tabLoading.value = true;
  queryForm.value = { isVerificationPage: false };
  const params = {
    ...queryForm.value,
    current: 1,
    limit: 10,
  };
  try {
    const { data: res } = await arList(params);
    tableData.value = res.records;
    dataTotal.value = res.total;
    current.value = res.current;
  } catch (error) {
    console.log(error);
  } finally {
    tabLoading.value = false;
  }
};

// 导出
const selectedRows = ref([]); // 选中的行数据
const selectionChange = (selection) => {
  selectedRows.value = selection;
};
const btnLoading = ref(false);
const handleExport = () => {
  if (!tableData.value.length) {
    plMessage("暂无可导出的数据", "warning");
    return;
  }

  btnLoading.value = true;
  let receivableRecordIdList = [];
  if (selectedRows.value.length === 0) {
    receivableRecordIdList = [];
  } else {
    receivableRecordIdList = selectedRows.value.map(
      (item) => item.receivableRecordId
    );
  }
  const params = {
    ...queryForm.value,
    receivableRecordIdList,
  };
  PAY.exportReceivableRecord(params)
    .then((res) => {
      if (res.code == 200) {
        window.open(res.data);
        plMessage(res.message, "success");
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const orderType = ref([]); //订单类型
const verificationStatus = ref([]); //核销状态

// 查询表单的数据
const queryForm = ref({ isVerificationPage: false });

// 使用 useTable 钩子管理表格相关逻辑
const {
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  handleEdit,
  handleDelete,
  handleSort,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/pay/receivableRecord/pageReceivableRecord",
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "应收单号",
    prop: "receivableNo",
    type: "input",
    placeholder: "请输入应收单号",
  },
  {
    label: "订单类型",
    prop: "orderTypeList",
    type: "select",
    options: orderType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
    placeholder: "请输入订单号",
  },
  {
    label: "核销状态",
    prop: "verificationStatusList",
    type: "select",
    options: verificationStatus,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "更新时间",
    prop: "updateTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      queryForm.value.updateTimeStart = e[0] + " 00:00:00";
      queryForm.value.updateTimeEnd = e[1] + " 23:59:59";
    },
  },
  {
    label: "创建时间",
    prop: "createTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      queryForm.value.createTimeStart = e[0] + " 00:00:00";
      queryForm.value.createTimeEnd = e[1] + " 23:59:59";
    },
  },
  {
    label: "到期日期",
    prop: "expireTime",
    type: "daterange",
    format: "YYYY-MM-DD",
    onChange: (e) => {
      queryForm.value.expirationTimeStart = e[0] + " 00:00:00";
      queryForm.value.expirationTimeEnd = e[1] + " 23:59:59";
    },
  },
  {
    label: "业务类型",
    type: "select",
    prop: "businessTypeList",
    labelKey: "businessTypeName",
    valueKey: "businessType",
    options: businessType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
]);

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "订单类型",
    prop: "orderType",
    type: "select",
    options: orderType,
  },
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "付款方名称",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "收款方式",
  },
  {
    label: "支付账户",
  },
  {
    label: "实收金额（元）",
  },
  {
    label: "交易流水号",
  },
  {
    label: "业务类型",
  },
  {
    label: "关联合同",
  },
  {
    label: "账单备注",
  },
  {
    label: "交易凭证",
  },
]);

/**
 * 处理抽屉提交事件的方法
 * @param {Event} e - 提交事件对象
 */
const handleDrawerSubmit = (e) => {
  // 输出提交事件对象到控制台，用于调试目的
  console.log(e);
};
</script>

<style lang="scss" scoped>
.operation-btns {
  display: flex;
  justify-content: flex-end;
}

.order-type {
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  width: 48px;
  display: flex;
  align-content: center;
  justify-content: center;
}
.TK {
  background: rgba(245, 63, 27, 0.1);
  color: rgb(245, 63, 27);
}
.PT {
  background: rgba(27, 118, 245, 0.1);
  color: rgb(27, 118, 245);
}
.BJ {
  background: rgba(3, 192, 120, 0.1);
  color: rgb(3, 192, 120);
}
</style>
