<template>
  <div class="detail-container">
    <div>
      <div class="drawer-w">
        <div class="title">订单信息</div>
        <pl-form
          :fields="baseFields"
          :form="formData"
          inline
          :disabled="true"
          :isButtonShow="false"
        ></pl-form>
        <div class="title">乘车人信息</div>
        <div v-for="(user, index) in formData.orderUserVOList" :key="index">
          <pl-form
            :fields="passengerFields"
            :form="user"
            inline
            :disabled="true"
            :isButtonShow="false"
          ></pl-form>
        </div>
        <div class="title">状态扭转</div>
        <div>
          <OrderStatusFlow
            :current-status="formData.orderState"
            :data="formData"
            @clickBtn="handleClickBtn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getOrderDetais } from "@/api/travel";
import OrderStatusFlow from "./OrderStatusFlow.vue";
const emit = defineEmits(["clickBtn"]);
const handleClickBtn = (type, data) => {
  emit("clickBtn", type, data);
};
const props = defineProps({
  detailData: {
    type: Object,
    default: () => {},
  },
});

const formData = ref(props.detailData);

const getNewDetailData = async () => {
  getOrderDetais({
    orderId: formData.value.orderId,
  }).then((res) => {
    formData.value = res.data;
  });
};

defineExpose({
  getNewDetailData,
});

const baseFields = ref([
  {
    label: "订单号",
    prop: "orderNo",
    type: "input",
  },
  {
    label: "订单类型",
    prop: "orderType",
    type: "input",
  },
  {
    label: "线路名称",
    prop: "travelLineName",
    type: "input",
  },
  {
    label: "乘车日期",
    prop: "orderDate",
    type: "date",
    format: "YYYY-MM-DD",
  },
  {
    label: "乘车时间",
    prop: "orderTime",
    type: "date",
    format: "HH:mm",
  },
  {
    label: "起点",
    prop: "startStopName",
    type: "input",
  },
  {
    label: "终点",
    prop: "endStopName",
    type: "input",
  },
  {
    label: "创建日期",
    prop: "createTime",
    type: "date",
    format: "YYYY-MM-DD HH:mm:ss",
  },
  {
    label: "联系电话",
    prop: "phone",
    type: "input",
  },
  {
    label: "备注",
    prop: "remark",
    type: "textarea",
  },
  {
    label: "派车备注",
    prop: "orderRemark",
    type: "textarea",
  },
]);

const passengerFields = ref([
  {
    label: "乘车人",
    prop: "userName",
    type: "input",
  },
  {
    label: "身份证号",
    prop: "idCard",
    type: "input",
  },
]);
</script>

<style lang="scss" scoped>
.drawer-w {
  .divider {
    margin: 16px 0;
    border-bottom: 1px dashed #e8e8e8;
  }
}
</style>
