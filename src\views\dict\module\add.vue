<template>
  <drawer :title="title" :modelValue="modelValue" @close="handleClose">
    <pl-form
      :form="form"
      ref="formRef"
      :fields="fields"
      :isButtonShow="false"
      inline
      clear
    ></pl-form>

    <!-- 按钮 -->
    <div class="button-box">
      <pl-button @click="handleClose" size="large">取消</pl-button>
      <pl-button
        plain
        type="primary"
        @click="handleSubmit(false)"
        v-if="type == 'add'"
        size="large"
        >确定并新增</pl-button
      >
      <pl-button type="primary" size="large" @click="handleSubmit(true)"
        >确定</pl-button
      >
    </div>
  </drawer>
</template>

<script setup>
import { ref, watch } from "vue";
import drawer from "@/components/module/drawer.vue";
import { updateDictType, addDictType } from "@/api/dict";
import { plMessage } from "pls-common";
let formRef = ref(null);

const prop = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  form: {
    type: Object,
    default: () => ({}),
  },
  tenantId: {
    default: null,
  },
});

let propType = ref(prop.type);
watch(
  () => prop.type,
  (val) => {
    propType.value = val;
    fields.value[1].disabled = val == "edit" ? true : false;
  }
);

const emit = defineEmits(["update:modelValue", "change"]);
/**
 * 关闭抽屉
 */
const handleClose = () => {
  emit("update:modelValue", false);
};

const fields = ref([
  {
    label: "类型名称",
    prop: "dictTypeName",
    type: "text",
    placeholder: "请输入类型名称",
    rules: [{ required: true, message: "请输入类型名称", trigger: "change" }],
  },
  {
    label: "编码类型",
    prop: "dictTypeCode",
    type: "text",
    disabled: false,
    placeholder: "请输入编码类型",
    rules: [{ required: true, message: "请输入编码类型", trigger: "change" }],
  },
]);
/**
 * 提交表单
 */
const handleSubmit = (isClosePop) => {
  formRef.value.confirm((data) => {
    if (propType.value == "edit") {
      updateDictType({
        tenantId: prop.tenantId,
        ...data,
      }).then((res) => {
        if (res.code == 200) {
          plMessage("修改成功", "success");
          handleClose();
          emit("change");
        } else {
          plMessage(res.message, "error");
        }
      });
    } else {
      addDictType({
        tenantId: prop.tenantId,
        ...data,
      }).then((res) => {
        if (res.code == 200) {
          plMessage(res.message, "success");
          formRef.value.handClear();
          if (isClosePop) {
            handleClose();
          }
          emit("change");
        } else {
          plMessage(res.message, "error");
        }
      });
    }
  });
};
</script>

<style scoped lang="scss"></style>
