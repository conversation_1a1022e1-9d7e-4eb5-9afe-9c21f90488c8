import request from "./request";
// 工程师助手相关api

//指令模板下载
export const getDownloadCommandTemplate = () => {
  return request.get(`/ea/commandTemplate/getDownloadCommandTemplate`);
};

/**
 * 指令模板导入
 */
export const postImportCommandTemplate = (params: any) => {
  return request.post(`/ea/commandTemplate/importCommandTemplate`, params);
};

/**
 * 领取网卡
 */
export const postReceiveNetCard = (params: any) => {
  return request.post(`/ea/netCard/receiveNetCard`, params);
};

/**
 * 移交网卡
 */
export const postTransferNetCard = (params: any) => {
  return request.post(`/ea/netCard/transferNetCard`, params);
};

/**
 * 注销网卡
 */
export const postCancelNetCard = (params: any) => {
  return request.post(`/ea/netCard/cancelNetCard`, params);
};

/**
 * 复用网卡
 */
export const getReuseNetCard = (params: any) => {
  return request.get(`/ea/netCard/reuseNetCard/${params.netCardId}`);
};

/**
 * 查询网卡详情
 */
export const getNetCardDetail = (params: any) => {
  return request.get(`/ea/netCard/netCardDetail/${params.netCardId}`);
};

/**
 * 查询网卡短信
 */
export const getNetCardSms = (params: any) => {
  return request.get(`/ea/netCardSms/getNetCardSms/${params.accessNumber}`);
};

/**
 * 网卡状态
 */
export const getNetCardStatus = (params: any) => {
  return request.get(`/ea/netCard/getNetCardStatus/${params.accessNumber}`);
};

/**
 * 网卡模板下载
 */
export const getDownloadNetCardTemplate = () => {
  return request.get(`/ea/netCard/getDownloadNetCardTemplate`);
};

/**
 * 网卡导入
 */
export const postImportNetCard = (params: any) => {
  return request.post(`/ea/netCard/importNetCard`, params);
};

/**
 * 导出网卡
 */
export const postExportNetCard = (params: any) => {
  return request.post(`/ea/netCard/exportNetCard`, params);
};

/**
 * 查询供应商列表(启用)
 */
export const getSupplierList = (params: any) => {
  return request.get(`/ea/supplier/getSupplierList`, params);
};

/**
 * 批量修改供应商
 */
export const updateNetCardSupplier = (params: any) => {
  return request.post(`/ea/netCard/updateNetCardSupplier`, params);
};

/**
 * 网卡退回
 */
export const postReturnNetCard = (params: any) => {
  return request.post(`/ea/netCard/returnNetCard`, params);
};

/**
 *手动拉取
 */
export const getPullNetCard = (params: any) => {
  return request.get(`/ea/netCard/pullNetCard/${params.orgId}`);
};

/**
 * 指令发送功能
 */
export const helper = {
  // 卡片列表
  cardList: (params: any) => {
    return request.get(`/ea/netCard/getNetCardList` + params);
  },
  // 历史消息列表
  historyList: (params: any) => {
    return request.get(`/ea/netCardSms/getNetCardSmsList/${params}`);
  },
  // 发送消息
  sendMsg: (params: any) => {
    return request.post(`/ea/netCardSms/netCardSendSms`, params);
  },
  // 获取厂商列表
  supplierList: () => {
    return request.get(`/ea/supplier/getSupplierList`);
  },
  // 获取模板列表(树形)
  getTempListTree: (params: any) => {
    return request.post(
      `/ea/commandTemplate/getCommandTemplateTreeList`,
      params
    );
  },
  // 获取模板列表
  getTempList: (params: any) => {
    return request.post(`/ea/commandTemplate/getCommandTemplateList`, params);
  },
  // 获取卡状态
  getCardStatus: (params: any) => {
    return request.get(`/ea/netCard/getNetCardStatus/${params}`);
  },
};
