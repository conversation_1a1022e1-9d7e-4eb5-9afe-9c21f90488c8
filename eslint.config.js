// 导入全局变量，以支持在代码中识别特定的全局变量
import globals from "globals";
// 导入JavaScript的eslint插件配置
import pluginJs from "@eslint/js";
// 导入TypeScript的eslint插件配置
import tseslint from "typescript-eslint";
// 导入Vue的eslint插件配置
import pluginVue from "eslint-plugin-vue";

// 定义并导出适用于项目中JavaScript、TypeScript和Vue文件的eslint配置数组
export default [
  // 配置适用于指定文件类型的基本设置
  { files: ["**/*.{js,mjs,cjs,ts,vue}"] },
  // 设置浏览器环境下的全局变量
  { languageOptions: { globals: globals.browser } },
  // 引入并应用eslint的JavaScript推荐配置
  pluginJs.configs.recommended,
  // 引入并应用eslint的TypeScript推荐配置
  ...tseslint.configs.recommended,
  // 引入并应用eslint的Vue插件的基础规则配置
  ...pluginVue.configs["flat/essential"],
  // 针对Vue文件，设置使用TypeScript解析器的解析选项
  {
    files: ["**/*.vue"],
    languageOptions: { parserOptions: { parser: tseslint.parser } },
  },
  {
    rules: {
      "vue/multi-word-component-names": "off",
      "no-var": "error", // 要求使用 let 或 const 而不是 var
      // "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
      // "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
      "no-multiple-empty-lines": ["warn", { max: 1 }], // 不允许多个空行
      "no-unexpected-multiline": "error", // 禁止空余的多行
      "@typescript-eslint/no-explicit-any": "off", // 禁止使用 any 类型
      "vue/no-mutating-props": "off", // 不允许组件 prop的改变
      "vue/attribute-hyphenation": "off", //
    },
  },
];
