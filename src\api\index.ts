import request from "./request";
import { storage } from "pls-common";
/**
 * 租户管理-查询租户列表 （请求头传管理员id：10000）
 */

export const getTenantList = (params: any) => {
  return request.post("/tenantList", params);
};

/**
 * 岗位管理-岗位列表 （启用状态）
 */
export const getPostList = (params: any) => {
  return request.get("/tenant/post/postList", params);
};

/**
 * 机构管理-机构树列表
 */
export const getOrgList = (params: any) => {
  return request.post("/tenant/org/orgTreeList", params);
};

/**
 * 用户管理-删除用户
 */
export const deleteUser = (params: any) => {
  return request.get(
    `/tenant/user/deleteUser/${params.userId}/${params.tenantId}`
  );
};

/**
 * 生成自定义编码规则
 */
export const getCodeRuleData = (params: any) => {
  return request.post(`/tenant/codeRule/generateCode`, params);
};

/**
 * 用户管理-查询用户详情
 */
export const getUserDetail = (userId: any) => {
  return request.get(`/teaant/user/userDetail/${userId}`);
};

/**
 * 租户管理-添加租户
 */
export const postTenantAdd = (params: any) => {
  return request.post("/saveTenant", params);
};

/**
 * 租户管理-修改租户
 * @param params
 * @returns
 */
export const updateTenant = (params: any) => {
  return request.post("/updateTenant", params);
};

/**
 * 方案管理-方案列表
 */
export const getPlanList = (params: any) => {
  return request.get("/plan/planList", params);
};

/**
 * 租户管理-查询租户详情
 */
export const getTenantDetail = (id: any) => {
  return request.get(`/tenantDetail/${id}`);
};

/**
 * 查询角色列表
 */
export const getRoleList = (params: any) => {
  return request.post("/tenant/role/roleList", params);
};

/**
 * 员工角色配置
 */
export const postRoleConfig = (params: any) => {
  return request.post(`/tenant/user/userRoleConfig`, params);
};

/**
 * 获取角色授权信息
 */
export const postRoleMenuTreeList = (params: any) => {
  return request.post(`/tenant/roleMenu/treeList`, params);
};
/**
 * 保存角色授权信息
 */
export const postRoleMenuSave = (params: any) => {
  return request.post(`/tenant/roleMenu/save`, params);
};

/**
 * 获取方案菜单树
 */
export const postPlanMenuTreeList = (params: any) => {
  return request.post(`/planMenu/treeList`, params);
};

/**
 * 保存方案菜单树
 */
export const postPlanMenuSave = (params: any) => {
  return request.post(`/planMenu/save`, params);
};

/**
 * 父级机构树列表
 */
export const getParentOrgTreeList = (params: any) => {
  return request.post(`/tenant/org/parentOrgTreeList`, params);
};

/**
 * 修改菜单状态
 */
export const updateMenuStatus = (params: any) => {
  return request.post(`/menu/updateMenuStatus`, params);
};

/**
 * 线路查询列表
 */
export const getLineList = (params: any) => {
  return request.post(`/line/getLineList`, params);
};

/**
 * 排班线路查询列表
 */
export const schedulingList = (params: any) => {
  return request.post(`/scheduling/getSchedulingLine`, params);
};

/**
 * 排班线路预览
 */
export const schedulingPreview = (params: any) => {
  return request.post(`/scheduling/getSchedulingItem`, params);
};

// 获取发车区域及站点信息
export const getSchedulingReginBarge = (params: any) => {
  return request.get(`/scheduling/template/getSchedulingRegion/` + params);
};

// 排班定制线路预览
export const customSchedulingPreview = (params: any) => {
  return request.post(`/scheduling/getSchedulingDZItem`, params);
};

// 排班修改发车时间
export const schedulingCarStart = (params: any) => {
  return request.post(`/scheduling/template/updateSchedulingItem`, params);
};

// 排班追加班次
export const schedulingCarAdd = (params: any) => {
  return request.post(`/scheduling/addScheduleItem`, params);
};

// 排班更换线路
export const schedulingChangeLine = (params: any) => {
  return request.post(`/scheduling/template/updateSchedulingItemLine`, params);
};

// 排班手动发车
export const schedulingStatus = (params: any) => {
  return request.post(`/scheduling/manualSchedulingStatus`, params);
};

// 已发车车辆手动到站
export const schedulingManual = (params: any) => {
  return request.post(`/scheduling/manualStopTime`, params);
};

/**
 * 省市区
 */
export const getAreaList = async (params: any) => {
  const allAreaList = storage.getItem("allAreaList", "session");
  if (allAreaList) {
    return allAreaList;
  }
  const res = await request.get(`/area/allAreaList`, params);
  if (res.code == 200) {
    storage.setItem("allAreaList", res.data, "session");
    return res.data;
  }
  return [];
};

/**
 * 带条件查询场站列表
 */
export const getStationList = async (params: any) => {
  const data = await request.get(`/depot/depotList`, params).then((res) => {
    return res.data.map((item: any) => {
      return {
        label: item.depotName,
        value: item.depotId,
      };
    });
  });
  return data;
};

/**
 * 查询线路详情
 */
export const getLineDetail = (id: any) => {
  return request.get(`/line/lineDetail/${id}`);
};

/**
 * 查询车辆列表
 */
export const getCarList = (params: any) => {
  return request.post(`/vehicle/getVehicleList`, params);
};

/**
 * 站点列表
 */
export const getDepotList = (params: any) => {
  return request.post(`/stop/getStopList`, params);
};

/**
 * 站点列表条件筛选
 */
export const siteFilterList = (params: any) => {
  return request.post(`/stop/pageStop`, params);
};

/**
 * 初始化线路地图
 */
export const postLineMap = (params: any) => {
  return request.post("/lineMap/getLineOwnLineMapData", params);
};

/**
 * 获取所有地图点位信息
 */
export const postAllMapPoint = (params: any) => {
  return request.get("/lineMap/getLineOwnLineMapData", params);
};

/**
 * 保存线路规划
 */
export const postSaveLineMapData = (params: any) => {
  return request.post("/lineMap/saveLineMapData", params);
};

/**
 * 查询所有车辆
 */
export const postAllVehicle = (params: any) => {
  return request.post("/vehicle/getVehicleList", params);
};
/**
 * 查询所有司机
 */
export const getDriverList = (params: any) => {
  // if (import.meta.env.MODE === "development") {
  //   return request.post("/tenant/user/getUserList", {
  //     ...params,
  //     employeeType: "SJ",
  //   });
  // } else {
  //   return request.get("/user/getDriverListByMenuCode", params);
  // }
  return request.post("/tenant/user/getUserList", {
    ...params,
    employeeType: "SJ",
  });
};

/**
 * 保存车辆和司机关联
 */
export const postSaveVehicleDriver = (PArams: any) => {
  return request.post("/vehicleDriver/saveVehicleDriver", PArams);
};

/**
 * 分页带条件查询人车绑定
 */
export const postLineVehicleDriver = (PArams: any) => {
  return request.post("/vehicleDriver/pageLineVehicleDriver", PArams);
};

/**
 * 删除所属线路
 */
export const deleteVehicleDriver = (params: any) => {
  return request.get(
    `/vehicleDriver/deleteVehicleDriver/${params.lineVehicleId}`
  );
};

/**
 * 解绑人车关系
 */
export const unbindVehicleDriver = (params: any) => {
  return request.post(`/vehicleDriver/unbindVehicleDriver`, params);
};

/**
 * 查询人车关系
 */
export const getVehicleDriverDetail = (params: any) => {
  return request.get(
    `/vehicleDriver/vehicleDriverDetail/${params.lineVehicleId}`
  );
};

/**
 * 修改人车关系
 */
export const updateVehicleDriver = (params: any) => {
  return request.post(`/vehicleDriver/updateVehicleDriver`, params);
};

/**
 * 查询C端用户详情
 */
export const getClientUserDetail = (params: any) => {
  return request.get(`/ClientUser/clientUserDetail/${params.clientUserId}`);
};

/**
 * 修改用户状态
 */
export const upDateClientUserStatus = (params: any) => {
  return request.post(`/ClientUser/updateClientUserStatus`, params);
};

/**
 * 排班保存
 */
export const postSchedulingAdd = (params: any) => {
  return request.post(`/scheduling/template/insert`, params);
};

/**
 * 公交-获取模板详情
 */
export const getGJLXTemplateItem = (params: any) => {
  return request.get(
    `/scheduling/template/getGJLXTemplateItem/${params.schedulingTemplateId}`
  );
};

/**
 * 排版生效
 */
export const postschedulingEffect = (params: any) => {
  return request.get(`/scheduling/effect/${params.schedulingId}`);
};

/**
 * 删除排版
 */
export const deleteScheduling = (params: any) => {
  return request.get(`/scheduling/deleteScheduling`, params);
};

/**
 * 修改排版
 */
export const updateSchedulingTemplateItem = (params: any) => {
  return request.post(`scheduling/template/updateSchedulingTemplate`, params);
};
/**
 * 查询站点列表
 */
export const getNearStopList = (params: any) => {
  return request.post(`/stop/getNearStopList`, params);
};

/**
 * 查询排班人车关系GET
 */
export const getSchedulingVehicleDriver = (params: any) => {
  return request.get(
    `/vehicleDriver/getSchedulingVehicleDriver/${params.lineId}`
  );
};
/**
 * 查询排班人车关系POST
 */
export const postSchedulingVehicleDriver = (params: any) => {
  return request.post(`/vehicleDriver/getSchedulingVehicleDriver`, params);
};
/**
 * 批量添加站点
 */
export const postBatchAddStop = (params: any) => {
  return request.post(`/stop/saveStopList`, params);
};
/**
 * 获取所有线路的线路规划信息
 */
export const getUnLineMapId = () => {
  return request.get(`/line/getUnLineMapId`);
};

/**
 * 采集同步线路站点
 */
export const postGatherLineStop = (params: any) => {
  return request.post(`/lineStop/gatherLineStop`, params);
};

/**
 * 获取线路下的站点以及车辆区间
 */
export const getLineStopVehicleInterval = (params: any) => {
  return request.post(`/traffic/stop/getLineStopVehicleInterval`, params);
};

/**
 * &获取统计数据
 */
export const getStat = {
  // 新增用户数
  getStatUserNum() {
    return request.get(`/uc/clientUser/getTodayUserCount`);
  },
  // 用户趋势图
  getStatUserTrend() {
    return request.get(`/uc/clientUser/getUserTrend`);
  },
  // 今日订单数
  getStatOrderNum() {
    return request.get(`/sc/order/getTodayOrder`);
  },
  // 订单趋势(周)
  getStatOrderTrend() {
    return request.get(`/sc/order/getOrderTrend`);
  },
  // 活跃车辆数
  getStatCarNum() {
    return request.get(`/vehicle/getTodayVehicleCount`);
  },
  // 车辆类型分布趋势
  getStatCarTypeTrend() {
    return request.get(`/line/getTodayLineCount`);
  },
};

// 获取oss配置信息
export const getOssConfig = async () => {
  const ossConfig = storage.getItem("ossConfig");
  if (ossConfig) {
    return ossConfig;
  }
  const res = await request.get(`/traffic/oss/getOss`);
  if (res) {
    storage.setItem("ossConfig", res);
    return res;
  }
};

//获取oss临时授权
export const getStsTemporaryToken = () => {
  return request.get(`/traffic/login/getStsTemporaryToken`);
};

/**
 * 重新执行业务（新）
 */
export const getPayReExecuteBusiness = (params: any) => {
  return request.get(`/pay/paymentNotify/reExecuteNotifyBusiness/${params}`);
};

/**
 * 支付通知详情（新）
 */
export const getPayNoticeDetail = (params: any) => {
  return request.get(`/pay/paymentNotify/paymentNotifyDetail/${params}`);
};
// 支付通知记录导出（新）
export const exportPayNotice = (params: any) => {
  return request.post(`/pay/paymentNotify/exportPaymentNotify`, params);
};

/**
 * 查看微信平台订单
 */
export const getPayWxOrder = (params: any) => {
  return request.get(`/pay/receivePay/getWxOrder/${params.outTradeNo}`);
};

// 查询数据权限配置接口
export const postQueryDataAuth = (params: any) => {
  return request.post(`/dataScope/queryDataScope`, params);
};

// 保存数据权限配置接口
export const postSaveDataAuth = (params: any) => {
  return request.post(`/dataScope/setDataScope`, params);
};

// 查询用户列表
export const getUserList = (params: any) => {
  return request.post(`/tenant/user/pageUser`, params);
};

// 查询司机列表
export const getDriverUserList = (params: any) => {
  return request.post(`/tenant/user/getUserList`, params);
};

// 批量解绑人车关系
export const postBatchUnBind = (params: any) => {
  return request.post(`/vehicleDriver/batchUnbindVehicleDriver/`, params);
};

// 标签列表
export const postLabelList = (params: any) => {
  return request.post(`/label/pageLabel`, params);
};
// 新增标签
export const addLabel = (params: any) => {
  return request.post(`/label/saveLabel`, params);
};
// 修改标签
export const editLabel = (params: any) => {
  return request.post(`/label/updateLabel`, params);
};
// 标签类目类别列表树形
export const labelTypeTree = (params: any) => {
  return request.post(`/labelCategory/getLabelCategoryTreeList`, params);
};
// 条件查询标签类目
export const labelTypeFilter = (params: any) => {
  return request.post(`/labelCategory/pageLabelCategory`, params);
};
// 标签类目类别列表
export const getLabelTypeList = (params: any) => {
  return request.post(`/labelCategory/labelCategoryList`, params);
};
// 标签类目新增
export const addLabelType = (params: any) => {
  return request.post(`/labelCategory/saveLabelCategory`, params);
};
// 标签类目修改
export const editLabelType = (params: any) => {
  return request.post(`/labelCategory/updateLabelCategory`, params);
};
// 标签类目删除
export const deleteLabelType = (params: any) => {
  return request.get(`/labelCategory/deleteLabelCategory/` + params);
};

/**
 * 获取应用列表
 */
export const getAppList = () => {
  return request.get(`/app/getAppListByAppType`);
};
/**
 * 查询问答详情
 */
export const getQaDetail = (params: any) => {
  return request.get(`/qa/qaDetail/${params.guideId}`);
};

/**
 * 获取标签列表
 */
export const getLabelList = (params: any) => {
  return request.post(`/label/getLabelList`, params);
};

/**
 * 设置排班模板状态
 */
export const setSchedulingTemplateStatus = (params: any) => {
  return request.post(
    `/scheduling/template/setTemplateStatus
`,
    params
  );
};

/**
 * 公交-获取班次详情
 */
export const getGJLXSchedulingItems = (params: any) => {
  return request.get(
    `/scheduling/template/getGJLXSchedulingItems/${params.schedulingId}`
  );
};

/**
 * 定制-获取班次详情
 */
export const getDZLXSchedulingItems = (params: any) => {
  return request.get(
    `/scheduling/template/getDZLXSchedulingItems/${params.schedulingId}`
  );
};
/**
 * 公交-定制-修改排班详情
 */
export const updateSchedulingItems = (params: any) => {
  return request.post(`/scheduling/template/updateSchedulingItems`, params);
};

/**
 * 定制-获取模板详情
 */ export const getDZLXTemplateItem = (params: any) => {
  return request.get(
    `/scheduling/template/getDZLXTemplateItem/${params.schedulingTemplateId}`
  );
};

/**
 * 查询问题反馈详情
 */
export const getFeedBackDetail = (params: any) => {
  return request.get(`/feedBack/feedBackDetail/${params.feedBackId}`);
};

/**
 * 问题反馈处理列表
 */
export const getFeedBackProcessList = (params: any) => {
  return request.get(
    `/feedBack/feedBackProcessList/${params.feedBackId}/${params.sortOrder}`
  );
};

/**
 * 提交处理问题反馈
 */
export const saveFeedBackProcess = (params: any) => {
  return request.post(`/feedBack/saveFeedBackProcess`, params);
};

/**
 * 完结处理
 */
export const getFinishProcess = (params: any) => {
  return request.get(`/feedBack/finishProcess/${params.feedBackId}`);
};

/**
 * 根据驳载线路id获取关联记录
 */
export const getSchedulingBarge = (params: any) => {
  return request.get(
    `/scheduling/template/getSchedulingBarge/${params.bargeId}`
  );
};

/**
 * 获取线路站点信息
 */
export const getLineStopInfo = (params: any) => {
  return request.post(`/traffic/line/getLineStop`, params);
};

/**
 * 查询线路下的所有站点
 */
export const getLineAllStop = (params: any) => {
  return request.post(`/line/getLineAllStop`, params);
};

/**
 * 获取员工信息列表（不包含系统和平台管理员）
 */
export const getEmployeeList = (params: any) => {
  return request.get(`/user/getUserList`, params);
};

/**
 * 区域详情
 */
export const getRegionDetail = (params: any) => {
  return request.get(`/sc/region/regionDetail/` + params);
};
/**
 * 新增区域
 */
export const addRegion = (params: any) => {
  return request.post(`/sc/region/saveRegion`, params);
};
/**
 * 修改区域
 */
export const updateRegion = (params: any) => {
  return request.post(`/sc/region/updateRegion`, params);
};
/**
 * 获取区域下站点
 */
export const getRegionSiteList = (params: any) => {
  return request.post(`/sc/region/getRegionElecFenceOwnStopList`, params);
};

/**
 * 绑定定制线路司机
 */
export const postBindCustomDriver = (params: any) => {
  return request.post(`/tenant/user/bindingUserOwnLine`, params);
};

/**
 * 解绑用户关联的线路
 */
export const unbindUserOwnLine = (params: any) => {
  return request.post(`/tenant/user/unbindUserOwnLine`, params);
};

/**
 * 绑定车辆关联的线路
 */
export const bindingVehicleOwnLine = (params: any) => {
  return request.post(`/vehicle/bindingVehicleOwnLine`, params);
};

/**
 *获取计价方案列表
 */
export const getPricingPlanList = () => {
  return request.get(`/ticket/pricingPlan/getPricingPlanList`);
};

/**
 * 附加计价类型-删除附加计价
 */
export const postRemovePricing = (params: any) => {
  return request.post(`/sc/travelLine/removePricing`, params);
};

/**
 * 附加计价类型-添加-修改附加计价
 */
export const insertUpdateAdditionalPricing = (params: any) => {
  return request.post(`/sc/travelLine/insertUpdateAdditionalPricing`, params);
};

/**
 * 绑定定制线路和计价方案
 */
export const bindingTravelLinePricingPlan = (params: any) => {
  return request.post(
    `/sc/travelLinePricingPlan/bindingTravelLinePricingPlan`,
    params
  );
};

/**
 * 查询定制线路的计价方案
 */
export const getTravelLinePricingPlan = (travelLineId: any) => {
  return request.get(
    `/sc/travelLinePricingPlan/getTravelLinePricingPlan/${travelLineId}`
  );
};

/**
 * 解绑定制线路的计价方案
 */
export const unbindingTravelLinePricingPlan = (params: any) => {
  return request.get(
    `/sc/travelLinePricingPlan/unbindingTravelLinePricingPlan/${params.travelLineId}`
  );
};

/**
 * 解绑车辆关联的线路
 */
export const unbindVehicleOwnLine = (params: any) => {
  return request.post(`/vehicle/unbindVehicleOwnLine`, params);
};

/**
 * 查询指定司机-车辆是否有排班项
 */
export const getDriverVehicleOwnSchedulingItem = (params: any) => {
  return request.post(`/scheduling/getDriverVehicleOwnSchedulingItem`, params);
};
/**
 * 查询计价
 */
export const getPricingDetail = (params: any) => {
  return request.get(`/ticket/pricing/pricingDetail/${params.pricingId}`);
};
/**
 * 车型列表
 */
export const getVehicleModelList = (params: any) => {
  return request.get(`/vehicleModel/getVehicleModelList`, params);
};

// ^ 财务
export const getBusinessType = (params: any) => {
  return request.get(`/businessType/getBusinessTypeList`, params);
};

// ^ 票务
/**
 * 查询线路列表
 */
export const getSiteList = (params: any) => {
  return request.post(`/stop/getStopList`, params);
};
// 车辆检票
export const checkTicketInfo = (params: any) => {
  return request.post(`/ticket/getCheckTicketInfo`, params);
};
// 确认车辆检票
export const checkTicket = (params: any) => {
  return request.post(`/ticket/checkTicket`, params);
};
// 后台发起退票
export const refundTicket = (params: any) => {
  return request.post(`/order/refund/refundTicket`, params);
};
// 车票票据详情
export const getTicketInfo = (params: any) => {
  return request.get(`/ticket/ticketDetail/${params}`);
};
/**
 * 查询车票座位信息
 */
export const getTicketSeat = (params: any) => {
  return request.post(`/ticket/price/getSeatTicketInfo`, params);
};
// 下单计价规则计算
export const tickPriceCal = (parmas: any) => {
  return request.post(`/sc/travelLinePricingPlan/getPricingRule`, parmas);
};
// 车票下单
export const payTicketOrder = (params: any) => {
  return request.post(`/order/logicOrder`, params);
};
// 确认支付订单
export const confirmPayTicketOrder = (params: any) => {
  return request.post(`/order/confirmPayOrder`, params);
};
// 根据业务编码获取业务详情
export const getBusinessDetail = (params: any) => {
  return request.get(
    `/businessType/getBusinessTypeDetail/${params.businessCode}`
  );
};
// 改签查询票
export const getTicketByNo = (params: any) => {
  return request.get(`/ticket/getTicketPriceInfo/${params}`);
};
// 改签新票据列表
export const getNewTicketList = (params: any) => {
  return request.post(`/ticket/price/pageTicketPrice`, params);
};
// 确认改签票
export const rebookTicket = (params: any) => {
  return request.post(`/order/rebookTicket`, params);
};

// ^ 实收
// 实收列表
export const actualList = (params: any) => {
  return request.post(`/pay/receiptRecord/pageReceiptRecord`, params);
};
// 新增实收
export const addActualRecord = (params: any) => {
  return request.post(`/pay/receiptRecord/saveReceiptRecord`, params);
};
// 确认收款
export const confirmReceipt = (params: any) => {
  return request.post(`/pay/receiptRecord/confirmReceipt`, params);
};
// 核销实收
export const verifyReceipt = (params: any) => {
  return request.post(`/pay/receiptRecord/batchVerificationReceipt`, params);
};
// 查单
export const checkOrder = (params: any) => {
  return request.post(
    `/pay/receiptRecord/getReceiptRecordPlatformOrder`,
    params
  );
};

// ^ 应收
export const arList = (params: any) => {
  return request.post(`/pay/receivableRecord/pageReceivableRecord`, params);
};

// ^ 退款
// 退款列表
export const refundList = (params: any) => {
  return request.post(`/pay/refundRecord/pageRefundRecord`, params);
};
// 记录导出
export const exportRefundRecord = (params: any) => {
  return request.post(`/pay/refundRecord/exportRefundRecord`, params);
};
// 退款方式
export const refundWayList = () => {
  return request.get(`/pay/refundRecord/getRefundWayList`);
};
// 确认/补发退款
export const confirmRefund = (params: any) => {
  return request.post(`/pay/refundRecord/confirmRefund`, params);
};

// ^ 结算清单
// 获取结算周期
export const getSettlementCycle = () => {
  return request.get(`/pay/settlement/getSettlementCycleList`);
};
// 清单明细列表
export const balanceDetailList = (params: any) => {
  return request.post(`/pay/settlementItem/pageSettlementItem`, params);
};
// 确认结算
export const confirmSettlement = (params: any) => {
  return request.post(`/pay/settlementItem/batchConfirmSettlementItem`, params);
};
// 导出结算明细
export const exportSettlementItem = (params: any) => {
  return request.post(`/pay/settlementItem/exportSettlementItem`, params);
};

// ^ 发票
// 发票详情
export const invoiceDetail = (params: any) => {
  return request.get(`/pay/invoice/invoiceDetail/${params}`);
};
// 开票
export const openInvoice = (params: any) => {
  return request.post(`/pay/invoice/openInvoice`, params);
};
export const getClientUserList = (params: any) => {
  return request.post("/uc/clientUser/getClientUserList", params);
};
//查询公告详情
export const noticeDetail = (noticeId: any) => {
  return request.get(`/traffic/notice/noticeDetail/${noticeId}`);
};

// 消息推送
// 新增消息推送模板
export const addMessageTemplate = (params: any) => {
  return request.post(`/messageTemplate/saveMessageTemplate`, params);
};
// 编辑消息推送模板
export const editMessageTemplate = (params: any) => {
  return request.post(`/messageTemplate/updateMessageTemplate`, params);
};
// 启用/停用消息模板
export const updateTemplateStatus = (params: any) => {
  return request.get(`/messageTemplate/updateMessageTemplateStatus/${params}`);
};
