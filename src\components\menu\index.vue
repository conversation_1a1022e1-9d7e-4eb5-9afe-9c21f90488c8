<template>
  <plScrollbar class="menu-scrollbar">
    <plMenu
      :isCollapse="isCollapse"
      :menuData="menuData"
      :defaultActive="defaultActive"
      v-if="menuData.length > 0"
      @select="handleMenuSelect"
    ></plMenu>

    <!-- 折叠 -->
  </plScrollbar>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { plMenu } from "pls-common";
defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
});
const router = useRouter();
const route = useRoute();
// 获取当前url地址
let defaultActive = ref(``);
// 监听路由路径的变化
watch(
  // 获取当前路由的路径
  () => route.path,
  // 当路由路径变化时执行的函数
  (newVal) => {
    setCurrentPath(newVal);
  }
);

const setCurrentPath = (path) => {
  defaultActive.value = path;
};
setCurrentPath(route.path);

let getRoutes = router.getRoutes();
const topRoutes = computed(() =>
  getRoutes.filter((route) => route.meta.level == 1)
);
/**
 * 构建菜单数据结构
 */
let menuData = ref([]);

/**
 * 遍历顶级路由数组，根据每个路由项构建菜单数据结构
 */
for (let i = 0; i < topRoutes.value.length; i++) {
  let item = topRoutes.value[i];
  let children = {};
  /**
   * 对首页进行特殊处理
   */
  if (item.path == "/") {
    children = {
      path: "/dashboard",
      name: item.meta.title || "",
      icon: item.meta.icon || "",
      children: [],
    };
  } else if (item.children.length == 1) {
    children = {
      path: item.path + "/" + item.children[0].path,
      name: item.meta.title || "",
      icon: item.meta.icon || "",
      children: [],
    };
  } else {
    /**
     * 处理非首页的路由项，包括其子路由的获取
     */
    children = {
      path: item.path,
      name: item.meta.title || "",
      icon: item.meta.icon || "",
      children: item.children.length > 0 ? getChildRoutes(item.children) : [],
    };
  }

  /**
   * 将构建好的children对象推入menuData数组中
   */
  menuData.value.push(children);
}
/**
 * 获取子路由
 * @param children 子路由
 */
function getChildRoutes(children) {
  return children.map((item) => {
    return {
      path: `/${item.path}`,
      name: item.meta.title,
      icon: item.meta.icon,
    };
  });
}

// 页面跳转
const handleMenuSelect = (key) => {
  router.push(key);
};
</script>

<style scoped lang="scss">
.menu-scrollbar {
  position: absolute;
  width: 100%;
}
</style>
