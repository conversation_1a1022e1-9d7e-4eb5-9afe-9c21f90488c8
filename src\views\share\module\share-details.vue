<template>
  <div class="share-container">
    <div class="share-form">
      <pl-form
        :fields="columns"
        inline
        :isButtonShow="false"
        :form="formData"
      ></pl-form>
    </div>

    <div class="share-table">
      <div class="left content">
        <leftContent
          :data="data"
          @rowClick="handleRowClick"
          @shareData="handleShareData"
        />
      </div>
      <div class="right content">
        <rightContent :data="detailsData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import leftContent from "./leftContent.vue";
import rightContent from "./rightContent.vue";
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const detailsData = ref({});
const formData = ref(props.data);
const handleShareData = (e) => {
  formData.value = {
    ...formData.value,
    ...e,
  };
};

const handleRowClick = (row) => {
  detailsData.value = row;
};

const columns = ref([
  {
    label: "用户名",
    prop: "userName",
    type: "input",
    disabled: true,
  },
  {
    label: "应用名称",
    prop: "appName",
    type: "input",
    disabled: true,
  },
  {
    label: "分享总数",
    prop: "shareCount",
    type: "input",
    disabled: true,
    unit: "人",
  },
  {
    label: "分享下单总数",
    prop: "shareOrderCount",
    type: "input",
    disabled: true,
    unit: "单",
  },
  {
    label: "分享订单总金额",
    prop: "shareAmount",
    type: "input",
    disabled: true,
    unit: "元",
  },
  {
    label: "抽佣金额",
    prop: "commissionAmount",
    type: "input",
    disabled: true,
    unit: "元",
  },
]);
</script>

<style lang="scss" scoped>
.share-table {
  display: flex;
  .left {
    border-right: 1px solid #e6e6e6;
    margin-right: 20px;
  }
  .content {
    flex: 1;
    padding: 20px;
    box-sizing: border-box;
    // box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.1);
    // border-radius: 10px;
  }
}
.share-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 20px;
  .share-table {
    flex: 1;
  }
}
.share-form {
  :deep(.pls-form-box) {
    border-bottom: 1px solid #e6e6e6;
  }
}
</style>
