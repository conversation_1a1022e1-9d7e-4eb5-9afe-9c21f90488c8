<template>
  <div class="seat-canvas h100">
    <!-- 座次选择业务组件 -->

    <div class="seat-canvas-box">
      <pl-dropdown
        :dropdown="dropdown"
        ref="dropdownRef"
        trigger="contextmenu"
        :teleported="false"
        placement="top"
        @menuClick="setCellStatus"
        v-if="isFullScreen"
      >
        <div class="dropdown-box" :style="dropdownStyle"></div>
      </pl-dropdown>
      <div class="zoom-controls">
        <el-popover :width="240" placement="left" :teleported="false">
          <template #reference>
            <pl-button>统计</pl-button>
          </template>

          <div class="seat-box-container">
            <div class="driver-box">
              <div class="driver-item">
                <img class="driver-img" :src="MainDriver" alt="" />
                <span>主驾驶位</span>
              </div>
              <div class="driver-item">
                <img class="driver-img fu" :src="copilotDriver" alt="" />
                <span>副驾驶位</span>
              </div>
            </div>
            <div class="seat-box">
              <div class="seat-item">
                <span class="seat-type ys">
                  <div class="pl-font pl-icon-selected"></div>
                </span>
                <span>已售 {{ getSeatCount("YS") }}</span>
              </div>
              <div class="seat-item">
                <span class="seat-type">
                  <div class="pl-font pl-icon-selected"></div>
                </span>
                <span>待售 {{ getSeatCount("DS") }}</span>
              </div>

              <div class="seat-item">
                <span class="seat-type yl">
                  <div class="pl-font pl-icon-selected"></div>
                </span>
                <span>预留 {{ getSeatCount("YL") }}</span>
              </div>
              <div class="seat-item">
                <span class="seat-type sd">
                  <div class="pl-font pl-icon-selected"></div>
                </span>
                <span>锁定 {{ getSeatCount("SD") }}</span>
              </div>
              <div class="seat-item">
                <span class="seat-type gz">
                  <div class="pl-font pl-icon-selected"></div>
                </span>
                <span>故障 {{ getSeatCount("GZ") }}</span>
              </div>
              <div class="seat-item">
                <span class="seat-type gd">
                  <div class="pl-font pl-icon-guodao"></div>
                </span>
                <span>过道</span>
              </div>
            </div>
          </div>
        </el-popover>
        <!-- <pl-button @click="handleZoom('out')">
          <pl-icon name="ZoomOut"></pl-icon>
        </pl-button>
        <pl-button @click="handleZoom('in')">
          <pl-icon name="ZoomIn"></pl-icon>
        </pl-button>
        <pl-button @click="handleRotate">
          <pl-icon name="RefreshRight"></pl-icon>
        </pl-button> -->
        <pl-button @click="handleFullScreen" style="margin-left: 10px">
          <pl-icon name="FullScreen"></pl-icon>
        </pl-button>
      </div>
      <div
        class="table-container"
        ref="tableContainerRef"
        @wheel.prevent="handleWheel"
        @mousedown="startDrag"
        @mousemove="onDrag"
        @mouseup="stopDrag"
        @mouseleave="stopDrag"
      >
        <div class="table-wrapper" :style="dragStyle">
          <table>
            <tbody>
              <tr v-for="(item, iIndex) in seatStatus" :key="iIndex">
                <td
                  v-for="(childItem, jIndex) in item"
                  :key="jIndex"
                  @mousedown.stop="handleCellMouseDown($event, iIndex, jIndex)"
                >
                  <!-- 操作按钮 start -->
                  <div
                    class="setting-left setting-box"
                    v-if="jIndex == 0 && isEdit"
                    @click.stop="handleSetting('left', iIndex, jIndex)"
                  ></div>
                  <div
                    class="setting-top setting-box"
                    v-if="iIndex == 0 && isEdit"
                    @click.stop="handleSetting('top', iIndex, jIndex)"
                  ></div>
                  <!-- 操作按钮 end -->

                  <!-- 座位状态 start -->
                  <div
                    class="seat-status-box"
                    :class="{
                      selected: isCellSelected(iIndex, jIndex),
                      selecting: isSelecting,
                    }"
                  >
                    <div class="gd" v-if="childItem.status == 'GD'">
                      <div class="pl-font pl-icon-guodao"></div>
                    </div>
                    <div class="yl" v-if="childItem.status == 'YL'">
                      <div class="pl-font pl-icon-selected"></div>
                      <span class="seat-num">{{ childItem.num }}</span>
                    </div>
                    <div class="sd" v-if="childItem.status == 'SD'">
                      <div class="pl-font pl-icon-selected"></div>
                      <span class="seat-num">{{ childItem.num }}</span>
                    </div>
                    <div class="ys" v-if="childItem.status == 'YS'">
                      <div class="pl-font pl-icon-selected"></div>
                      <span class="seat-num">{{ childItem.num }}</span>
                    </div>
                    <div class="gz" v-if="childItem.status == 'GZ'">
                      <div class="pl-font pl-icon-selected"></div>
                      <span class="seat-num">{{ childItem.num }}</span>
                    </div>
                    <div class="ds" v-if="childItem.status == 'DS'">
                      <div class="pl-font pl-icon-selected"></div>
                      <span class="seat-num">{{ childItem.num }}</span>
                    </div>
                    <div
                      class="zjsw"
                      v-if="childItem.status == 'ZJSW'"
                      :style="`background-image: url('${MainDriver}')`"
                    ></div>
                    <div
                      class="fjsw"
                      v-if="childItem.status == 'FJSW'"
                      :style="`background-image: url('${copilotDriver}')`"
                    ></div>
                  </div>
                  <!-- 座位状态 end -->
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 按钮 -->
      <div class="button-box" v-if="isEdit && showBtn">
        <pl-button class="btn" @click="handleClose">取消</pl-button>
        <!-- <pl-button class="btn">确定并新增</pl-button> -->
        <pl-button class="btn" type="primary" @click="handleSubmit"
          >确定</pl-button
        >
      </div>
    </div>

    <Teleport to="body">
      <pl-dropdown
        :dropdown="dropdown"
        ref="dropdownRef"
        trigger="contextmenu"
        :teleported="false"
        placement="top"
        @menuClick="setCellStatus"
        v-if="!isFullScreen"
      >
        <div
          class="dropdown-box"
          :style="dropdownStyle"
          v-if="!isFullScreen"
        ></div>
      </pl-dropdown>
    </Teleport>

    <!-- 菜单 end -->
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, onMounted, watch } from "vue";
import MainDriver from "@/assets/img/zhujiashi.png";
import copilotDriver from "@/assets/img/fujiashi.png";
import { plMessage } from "pls-common";
// import { getSeatDetail } from "@/api/car";
import { nextTick } from "process";
const props = defineProps({
  seatId: {
    // 座次id
    type: [String, Number],
    default: "",
  },
  // 是否是编辑模式
  isEdit: {
    type: Boolean,
    default: false,
  },
  // 是否是售票模式
  isSale: {
    type: Boolean,
    default: false,
  },
  // 下拉框显示操作
  dropValueData: {
    type: Array,
    default: () => ["ZJSW", "FJSW", "DS", "GD", "YL", "GZ", ""],
  },
  // 是否能修改主驾驶
  canModifyMainDriver: {
    type: Boolean,
    default: true,
  },
  // 是否能修改副驾驶
  canModifyCopilotDriver: {
    type: Boolean,
    default: true,
  },
  // 是否显示按钮
  showBtn: {
    type: Boolean,
    default: true,
  },
  // 座次信息
  seatInfoSetList: {
    type: Array,
    default: () => [],
  },
});

const isFullScreen = ref(false);

// 添加全屏变化事件监听
const handleFullscreenChange = () => {
  isFullScreen.value = !!document.fullscreenElement;
};
const handleFullScreen = () => {
  const mapElement = document.querySelector(".seat-canvas-box");
  if (!document.fullscreenElement) {
    isFullScreen.value = true;
    mapElement.requestFullscreen().catch((err) => {
      isFullScreen.value = false;
      console.error(
        `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
      );
    });
  } else {
    isFullScreen.value = false;
    document.exitFullscreen();
  }
};

onMounted(() => {
  // 添加全屏变化事件监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
});
onUnmounted(() => {
  // 移除全屏变化事件监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
});

/**
 * clickSeat 售票模式下点击座位
 * close 编辑模式下关闭
 * submit 编辑模式下确定
 */
const emit = defineEmits(["clickSeat", "close", "submit", "change"]);

// 取消
const handleClose = () => {
  emit("close");
};

const queryForm = ref({});
const seatStatus = ref([]);
onMounted(() => {
  if (
    props.seatId &&
    (!props.seatInfoSetList || props.seatInfoSetList.length === 0)
  ) {
    // getSeatDetail({
    //   seatId: props.seatId,
    // }).then((res) => {
    //   seatStatus.value = res.data.seatInfoSetList;
    //   checkZJSWAndFJSW();
    //   emit("change", seatStatus.value);
    // });
  } else if (props.seatInfoSetList && props.seatInfoSetList.length > 0) {
    seatStatus.value = props.seatInfoSetList;
    checkZJSWAndFJSW();
    emit("change", seatStatus.value);
  }
});

watch(
  () => props.seatId,
  (newVal) => {
    if (
      newVal &&
      (!props.seatInfoSetList || props.seatInfoSetList.length === 0)
    ) {
      // getSeatDetail({
      //   seatId: newVal,
      // }).then((res) => {
      //   seatStatus.value = res.data.seatInfoSetList;
      //   checkZJSWAndFJSW();
      //   emit("change", seatStatus.value);
      // });
    } else {
      seatStatus.value = props.seatInfoSetList;
      checkZJSWAndFJSW();
    }
  }
);

watch(
  () => props.seatInfoSetList,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      seatStatus.value = newVal;
      checkZJSWAndFJSW();
      emit("change", seatStatus.value);
    }
  }
);

const dropdownRef = ref(null);
const dpV = [
  {
    name: "设为主驾驶位",
    value: "ZJSW",
    show: true,
  },
  {
    name: "设为副驾驶位",
    value: "FJSW",
    show: true,
  },
  {
    name: "设为待售",
    value: "DS",
    show: true,
  },
  {
    name: "设为过道",
    value: "GD",
    show: true,
  },
  {
    name: "设为预留",
    value: "YL",
    show: true,
  },
  {
    name: "设为故障",
    value: "GZ",
    show: true,
  },
  {
    name: "去掉座位",
    value: "",
    show: true,
  },
];

const dropdown = ref([]);

props.dropValueData.forEach((dpD) => {
  const option = dpV.find((item) => item.value == dpD);
  dropdown.value.push(option);
});

const dropdownStyle = ref({
  left: "120px",
  top: "120px",
});

// 获取座位数量
const getSeatCount = (status) => {
  let count = 0;
  seatStatus.value.forEach((item) => {
    item.forEach((seat) => {
      if (seat.status === status) {
        count++;
      }
    });
  });
  return count;
};

// 确定
const handleSubmit = () => {
  // 检查主驾驶位是否设置在第一排
  const isZJSWSet = seatStatus.value[0].some((seat) => seat.status === "ZJSW");
  if (!isZJSWSet) {
    plMessage("主驾驶位必须设置且必须在第一排", "warning");
    return;
  }
  queryForm.value.seatInfoSetList = seatStatus.value;

  // 统计待售(DS)座位的数量
  let dsCount = 0;

  // 统计除了ZJSW、FJSW、GD以外的座位数量（排除空的）
  let otherSeatsCount = 0;

  // 行
  let rowCount = 0;
  // 列
  let columnCount = 0;

  seatStatus.value.forEach((row) => {
    rowCount++;
    columnCount = row.length;
    row.forEach((seat) => {
      // 统计待售座位
      if (seat.status === "DS") {
        dsCount++;
      }
      // 统计除了ZJSW、FJSW、GD以外的有效座位
      if (seat.status && seat.status !== "GD") {
        otherSeatsCount++;
      }
    });
  });
  // 可以将待售座位数量保存到表单中
  queryForm.value.forSale = dsCount;
  queryForm.value.total = otherSeatsCount;
  queryForm.value.seatRow = Number(rowCount);
  queryForm.value.seatColumn = Number(columnCount);
  console.log("确定", queryForm.value);
  emit("submit", queryForm.value);
};

// 选择整行或整列
const handleSetting = (type, i, j) => {
  if (type == "left") {
    seatStatus.value[i].forEach((item, index) => {
      selectedCells.value.push([i, index]);
    });
  } else if (type == "top") {
    seatStatus.value.forEach((item, index) => {
      selectedCells.value.push([index, j]);
    });
  }
};

// 打开下拉框
const handleDropdownOpen = (event) => {
  console.log("打开下拉框");
  if (!props.isEdit) return; // 如果不是编辑模式，操作无效
  dropdownStyle.value = {
    left: event.clientX + "px",
    top: event.clientY + "px",
  };
  console.log(dropdownStyle.value);
  dropdownRef.value.handleOpen();
};

const scale = ref(1);
const MIN_SCALE = 0.2;
const MAX_SCALE = 3;
const SCALE_STEP = 0.1;

// 拖动相关状态
const isDragging = ref(false);
const startX = ref(0);
const startY = ref(0);
const translateX = ref(0);
const translateY = ref(0);
const lastTranslateX = ref(0);
const lastTranslateY = ref(0);

// 添加选择相关的状态
const isSelecting = ref(false);
const selectionStart = ref(null);
const selectionEnd = ref(null);
const selectedCells = ref([]);
const longPressTimeout = ref(null);

// 长按开始时间
const LONG_PRESS_DURATION = 300; // 300ms 长按判定

// 添加点击时间记录
const mouseDownTime = ref(0);

// 合并拖动和缩放样式
const dragStyle = computed(() => ({
  transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value}) rotate(${rotationAngle.value}deg)`,
  transformOrigin: "center center",
  cursor: isDragging.value ? "grabbing" : "grab",
}));

// const handleZoom = (type) => {
//   if (type === "in" && scale.value < MAX_SCALE) {
//     scale.value += SCALE_STEP;
//   } else if (type === "out" && scale.value > MIN_SCALE) {
//     scale.value -= SCALE_STEP;
//   }
// };

const rotationAngle = ref(0); // 用于存储当前旋转角度
// //选择
// const handleRotate = () => {
//   rotationAngle.value = rotationAngle.value + 90; // 每次旋转90度
// };

// 处理鼠标滚轮事件
const handleWheel = (event) => {
  // 判断滚动方向，向上滚动为放大，向下滚动为缩小
  const isZoomIn = event.deltaY < 0;

  if (isZoomIn && scale.value < MAX_SCALE) {
    scale.value = Math.min(scale.value + SCALE_STEP, MAX_SCALE);
  } else if (!isZoomIn && scale.value > MIN_SCALE) {
    scale.value = Math.max(scale.value - SCALE_STEP, MIN_SCALE);
  }
};

// 清除选择状态的函数
const clearSelection = () => {
  isSelecting.value = false;
  selectionStart.value = null;
  selectionEnd.value = null;
  selectedCells.value = [];
  clearTimeout(longPressTimeout.value);
};
// 鼠标按下状态
const mouseDownStatus = ref(0);
// 处理单元格鼠标按下事件
const handleCellMouseDown = (event, i, j) => {
  dropdownRef.value.handleClose();

  mouseDownStatus.value = event.buttons;
  // 阻止事件冒泡
  event.stopPropagation();

  // 记录鼠标按下时间
  mouseDownTime.value = Date.now();

  // 清除之前的选择状态
  clearSelection();
  document.addEventListener("mouseup", handleGlobalMouseUp);
  // 启动长按计时器
  longPressTimeout.value = setTimeout(() => {
    // 只有在鼠标还在按下状态时才激活选择模式
    if (mouseDownStatus.value === 1) {
      // 1 表示鼠标左键
      isSelecting.value = true;
      selectionStart.value = { i, j };
      selectionEnd.value = { i, j };
      selectedCells.value = new Set([[i, j]]);
      // 添加全局鼠标移动事件监听
      if (props.isEdit) {
        // 编辑模式下，鼠标移动时，更新选中的单元格
        document.addEventListener("mousemove", handleGlobalMouseMove);
      }
    }
  }, LONG_PRESS_DURATION);
};

// 全局鼠标移动处理
const handleGlobalMouseMove = (event) => {
  if (!isSelecting.value) return;

  // 获取鼠标所在的单元格
  const element = document.elementFromPoint(event.clientX, event.clientY);

  let $element = getElementTd(element);

  if ($element && $element.tagName.toLowerCase() === "td") {
    // 从元素中获取索引
    const row = $element.parentElement.rowIndex;
    const col = $element.cellIndex;

    if (row !== undefined && col !== undefined) {
      selectionEnd.value = { i: row, j: col };
      updateSelectedCells();
    }
  }
};
// 检查主驾驶和副驾驶是否被设置
const checkZJSWAndFJSW = () => {
  // 检查整个 seatStatus 数组中是否还有 ZJSW 或 FJSW
  if (!seatStatus.value || seatStatus.value.length === 0) return;
  const hasZJSW = seatStatus.value.some((row) =>
    row.some((cell) => cell.status === "ZJSW")
  );

  const hasFJSW = seatStatus.value.some((row) =>
    row.some((cell) => cell.status === "FJSW")
  );

  // 如果没有 ZJSW，则启用对应的下拉选项
  if (!hasZJSW) {
    const zjswOption = dropdown.value.find((item) => item.value === "ZJSW");
    if (zjswOption) zjswOption.disabled = false;
  } else {
    const zjswOption = dropdown.value.find((item) => item.value === "ZJSW");
    if (zjswOption) zjswOption.disabled = true;
  }

  // 如果没有 FJSW，则启用对应的下拉选项
  if (!hasFJSW) {
    const fjswOption = dropdown.value.find((item) => item.value === "FJSW");
    if (fjswOption) fjswOption.disabled = false;
  } else {
    const fjswOption = dropdown.value.find((item) => item.value === "FJSW");
    if (fjswOption) fjswOption.disabled = true;
  }
};
// 设置每个单元格的状态
const setCellStatus = (setSeatStatus) => {
  if (
    (setSeatStatus.value == "ZJSW" || setSeatStatus.value == "FJSW") &&
    selectedCells.value.length > 1
  ) {
    plMessage("你觉得设置多个主驾驶位或副驾驶位合理吗？", "warning");
    return;
  }

  selectedCells.value.forEach(([row, col]) => {
    if (
      seatStatus.value[row][col].status == "ZJSW" &&
      setSeatStatus.value === ""
    ) {
      dropdown.value.find((item) => item.value == "ZJSW").disabled = false;
    } else if (
      seatStatus.value[row][col].status == "FJSW" &&
      setSeatStatus.value === ""
    ) {
      dropdown.value.find((item) => item.value == "FJSW").disabled = false;
    }

    seatStatus.value[row][col].status = setSeatStatus.value;
  });

  let seatNum = 0;
  seatStatus.value.forEach((row) => {
    row.forEach((col) => {
      if (
        col.status == "DS" ||
        col.status == "GZ" ||
        col.status == "YL" ||
        col.status == "YS" ||
        col.status == "SD"
      ) {
        seatNum++;
        col.num = String(seatNum).padStart(3, "0");
      } else {
        col.num = "";
      }
    });
  });

  checkZJSWAndFJSW();

  clearSelection();
  // 关闭下拉菜单
  dropdownRef.value?.handleClose();

  if (setSeatStatus.value == "ZJSW") {
    dropdown.value.find((item) => item.value == "ZJSW").disabled = true;
  } else if (setSeatStatus.value == "FJSW") {
    dropdown.value.find((item) => item.value == "FJSW").disabled = true;
  }
  emit("change", seatStatus.value);
};

// 获取td元素
const getElementTd = (element) => {
  function getTd(element) {
    if (element.tagName.toLowerCase() === "td") {
      return element;
    } else {
      return getTd(element.parentElement);
    }
  }
  return getTd(element);
};

// 全局鼠标抬起处理
const handleGlobalMouseUp = (event) => {
  mouseDownStatus.value = event.buttons;
  // 移除全局事件监听
  document.removeEventListener("mousemove", handleGlobalMouseMove);
  document.removeEventListener("mouseup", handleGlobalMouseUp);

  const pressDuration = Date.now() - mouseDownTime.value;
  console.log(selectedCells.value, "selectedCells.value");
  if (isSelecting.value) {
    console.log("选中");
    // 这里可以处理选中的单元格
    if (Array.from(selectedCells.value).length > 0) {
      isCanModifyDriverAndCopilot().then((res) => {
        if (res) {
          handleDropdownOpen(event);
        }
      });
    }

    // 处理完选中的单元格后清除选择状态
    // clearSelection();
  } else if (pressDuration < LONG_PRESS_DURATION) {
    console.log("单选");
    const $element = getElementTd(event.target);

    if ($element && $element.tagName.toLowerCase() === "td") {
      const row = $element.parentElement.rowIndex;
      const col = $element.cellIndex;
      if (row !== undefined && col !== undefined) {
        selectedCells.value = [[row, col]];
        isCanModifyDriverAndCopilot().then((res) => {
          if (res) {
            handleDropdownOpen(event);
          }
        });
      }
    }
  }

  if (props.isSale) {
    handleClickSeatChange();
  }

  clearTimeout(longPressTimeout.value);
};

// 判断是否能修改副驾驶和主驾驶
const isCanModifyDriverAndCopilot = () => {
  return new Promise((resolve) => {
    nextTick(() => {
      let num = 0;
      for (let i = 0; i < selectedCells.value.length; i++) {
        let item = selectedCells.value[i];
        if (
          !props.canModifyMainDriver &&
          seatStatus.value[item[0]][item[1]].status == "ZJSW"
        ) {
          plMessage("选中的位置包含主驾驶, 不能被更改", "warning");
          clearSelection();
          return;
        }
        if (
          !props.canModifyCopilotDriver &&
          seatStatus.value[item[0]][item[1]].status == "FJSW"
        ) {
          plMessage("选中的位置包含副驾驶, 不能被更改", "warning");
          clearSelection();
          return;
        }
        num++;
      }

      if (num == selectedCells.value.length) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  });
};

// 售票模式下会调用这个方法
const handleClickSeatChange = () => {
  console.log("售票模式下会调用这个方法");
  const seatValue = selectedCells.value[0];
  if (!seatValue) {
    clearSelection();
    return false;
  }

  // 判断位置是否是司机或者副驾驶或者故障
  if (seatStatus.value[seatValue[0]][seatValue[1]].status == "ZJSW") {
    plMessage("当前位置是主驾驶,请选择其他位置", "warning");
    clearSelection();
    return;
  } else if (seatStatus.value[seatValue[0]][seatValue[1]].status == "FJSW") {
    plMessage("当前位置是副驾驶,请选择其他位置", "warning");
    clearSelection();
    return;
  } else if (
    seatStatus.value[seatValue[0]][seatValue[1]].status == "GD" ||
    seatStatus.value[seatValue[0]][seatValue[1]].status == ""
  ) {
    // plMessage("当前,请选择其他位置", "warning");
    clearSelection();
    return;
  } else if (seatStatus.value[seatValue[0]][seatValue[1]].status == "YS") {
    plMessage("当前位置是已售状态,请选择其他位置", "warning");
    clearSelection();
    return;
  } else if (seatStatus.value[seatValue[0]][seatValue[1]].status == "SD") {
    plMessage("当前位置是锁定状态,请选择其他位置", "warning");
    clearSelection();
    return;
  } else if (seatStatus.value[seatValue[0]][seatValue[1]].status == "GZ") {
    plMessage("当前位置是故障状态,请选择其他位置", "warning");
    clearSelection();
    return;
  }

  console.log(
    `当前选中的位置是第${seatValue[0]}行,第${seatValue[1]}列, 座次号：${
      seatStatus.value[seatValue[0]][seatValue[1]].num
    }`
  );
  emit("clickSeat", seatStatus.value[seatValue[0]][seatValue[1]]);
};

// 更新选中的单元格
const updateSelectedCells = () => {
  if (!selectionStart.value || !selectionEnd.value) return;

  const startI = Math.min(selectionStart.value.i, selectionEnd.value.i);
  const endI = Math.max(selectionStart.value.i, selectionEnd.value.i);
  const startJ = Math.min(selectionStart.value.j, selectionEnd.value.j);
  const endJ = Math.max(selectionStart.value.j, selectionEnd.value.j);

  selectedCells.value = [];
  for (let i = startI; i <= endI; i++) {
    for (let j = startJ; j <= endJ; j++) {
      selectedCells.value.push([i, j]);
    }
  }
};

// 判断单元格是否被选中
const isCellSelected = (i, j) => {
  return Array.from(selectedCells.value).some(
    (cell) => cell[0] === i && cell[1] === j
  );
};

// 修改开始拖动函数
const startDrag = (event) => {
  isDragging.value = true;
  startX.value = event.clientX - translateX.value;
  startY.value = event.clientY - translateY.value;
  lastTranslateX.value = translateX.value;
  lastTranslateY.value = translateY.value;
};

// 拖动中
const onDrag = (event) => {
  if (!isDragging.value) return;

  event.preventDefault();
  translateX.value = event.clientX - startX.value;
  translateY.value = event.clientY - startY.value;
};

// 停止拖动
const stopDrag = () => {
  isDragging.value = false;
};

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener("mousemove", handleGlobalMouseMove);
  document.removeEventListener("mouseup", handleGlobalMouseUp);
  clearTimeout(longPressTimeout.value);
});
</script>

<style lang="scss" scoped>
.seat-box-container {
  padding: 10px;
  width: 225px;
}

.preview-box {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  .preview-item {
    margin-right: 20px;
    font-size: 16px;
    &:last-child {
      margin-right: 0;
    }
  }
}
.pl-icon-selected,
.pl-icon-guodao {
  font-size: 30px !important;
}
.dropdown-box {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  opacity: 0;
  width: 50;
  // background-color: red;
}
.table-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  overflow: auto;
  position: relative;
  cursor: grab;
  user-select: none; // 防止拖动时选中文本
  overflow: hidden;
  background-image: radial-gradient(#838383 1px, transparent 1px);
  background-size: 20px 20px;
  background-color: #fff;
  padding: 20px;
  z-index: 5;
  position: relative;
}

.table-wrapper {
  transition: transform 0.3s linear;
  &:active {
    transition: none;
  }
}

.zoom-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
  .long-press-tip {
    position: absolute;
    bottom: -30px;
    left: 0;
    text-align: center;
    width: 100%;
    color: #868686;
    font-size: 14px;
  }
}

table {
  border-collapse: collapse;
}
td {
  width: 80px;
  height: 80px;
  // border: 1px solid #000;
  background: #fff;
  position: relative;
  cursor: pointer;
  .fjsw {
    width: 100%;
    height: 100%;
    background-size: 70%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .zjsw {
    width: 100%;
    height: 100%;
    background-size: 80%;
    background-repeat: no-repeat;
    background-position: center;
  }
  .driver-img {
    width: 30px;
  }
  &.gdtd {
    border-bottom: none;
    border-top: none;
  }
  .seat-status-box {
    height: 100%;
    padding: 2px;
    &.selecting {
      user-select: none;
      cursor: crosshair;
    }
    div {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      border-radius: 10px;
    }
  }
  .seat-num {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: #555;
    font-weight: bold;
  }
  .gd {
    background: rgb(194, 204, 255);
    width: 100%;
    height: 100%;
    font-size: 12px;
  }
  .yl {
    background: rgb(205, 205, 205);
    width: 100%;
    height: 100%;
  }
  .sd {
    background: rgb(244, 207, 74);
    width: 100%;
    height: 100%;
  }
  .gz {
    background: rgb(255, 108, 108);
    width: 100%;
    height: 100%;
  }
  .ds {
    background: rgb(154, 210, 255);
    width: 100%;
    height: 100%;
    position: relative;
  }
  .ys {
    background: rgb(4, 223, 66);
    width: 100%;
    height: 100%;
  }
  .setting-box {
    position: absolute;
    background-color: var(--el-color-primary-light-3);

    &.setting-left {
      left: -32px;
      top: 0;
      height: 100%;
      width: 30px;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
    }
    &.setting-top {
      left: 0;
      top: -32px;
      height: 30px;
      width: 100%;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }

  &:hover {
    background: #f5f7fa;
  }

  .selected {
    background-color: var(--el-color-primary);
    border-radius: 10px;
    transform: scale(0.95);
    .gd {
      background: none;
    }
    .yl {
      background: none;
    }
    .gz {
      background: none;
    }
    .ds {
      background: none;
    }
  }

  &.selecting {
    user-select: none;
    cursor: crosshair;
  }
}

.seat-canvas-box {
  height: 100%;
  position: relative;
  background-color: transparent; /* 确保背景透明 */
}
.seat-box {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  user-select: none; // 防止拖动时选中文本
  flex-wrap: wrap;
  .seat-item {
    width: 50%;
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
    .seat-type {
      width: 30px;
      height: 30px;
      background-color: rgba(154, 210, 255, 1);
      border-radius: 5px;
      margin-right: 10px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .pl-icon-selected,
      .pl-icon-guodao {
        font-size: 18px !important;
      }
      &.sd {
        background-color: rgb(244, 207, 74);
      }
      &.ys {
        background-color: rgb(4, 223, 66);
      }
      &.yl {
        background-color: rgba(205, 205, 205, 1);
      }
      &.gz {
        background-color: rgba(255, 108, 108, 1);
      }
      &.gd {
        background-color: rgba(194, 204, 255, 1);
      }
    }
  }
}
.driver-box {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  .driver-item {
    display: flex;
    align-items: center;
    pointer-events: none; /* 这将阻止所有鼠标事件，包括拖动 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    span {
      margin-left: 10px;
    }
    .driver-img {
      width: 30px;
      height: 30px;
      &.fu {
        width: 25px;
        height: 25px;
      }
    }
  }
}
.seat-canvas {
  padding-bottom: 20px;
  display: flex;
  min-height: 400px;
  position: relative;
  background-color: green; /* 确保根元素背景透明 */
  z-index: 8;
  .seat-canvas-box {
    flex: 1;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: transparent; /* 确保背景透明 */
  }
}
.setup-btn {
  width: 150px;
  margin: auto;
  display: block;
}

.table-container {
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  &:active {
    cursor: grabbing;
  }
}

// 防止文本选择
.table-wrapper {
  user-select: none;
}
:deep(.preview) {
  .left-container,
  .drag-line {
    display: none !important;
  }
  .right-container {
    width: 100% !important;
  }
}
</style>
