<template>
  <pl-select
    :options="options"
    v-model="selectVal"
    @change="change"
    :multiple="multiple"
  ></pl-select>
</template>

<script setup>
import { ref, watch } from "vue";
import { getDictionaryData } from "@/api/dict";
const options = ref([]);
const props = defineProps({
  dictCode: {
    type: String,
    default: "",
  },
  modelValue: {
    type: [String, Number, Array],
    default: "",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
});

const selectVal = ref(props.modelValue);

watch(
  () => props.modelValue,
  (v) => {
    selectVal.value = v;
  }
);
const emit = defineEmits(["update:modelValue", "getOptions"]);

//请求字典数据
getDictionaryData({
  dictTypeCode: props.dictCode,
}).then((res) => {
  options.value = res;
});

const change = (v) => {
  console.log(v);
  emit("update:modelValue", v);
  emit("getOptions", options.value);
};
</script>

<style lang="scss" scoped></style>
