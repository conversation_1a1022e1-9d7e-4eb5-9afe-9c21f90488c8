import { reactive } from "vue";

let savePolygon = reactive<any[]>([]);
export function useFence(mapBase: any, props: any) {
  const drawMapConfig = {
    map: mapBase.map.value,
    strokeWeight: 1,
    strokeColor: "#0091ea",
    fillColor: "#80d8ff",
    fillOpacity: 0.2,
  };

  const areaFence = props.areaFence;
  if (savePolygon.length) {
    savePolygon.forEach((item: any) => {
      mapBase.map.value.remove(item);
    });
    savePolygon = [];
  }
  if (areaFence.length > 0) {
    for (let i = 0; i < areaFence.length; i++) {
      const polygon = new mapBase.AMap.Polygon({
        ...drawMapConfig,
        path: areaFence[i],
      });
      savePolygon.push(polygon);
      polygon.on("click", (e: any) => {
        mapBase.map.value.setFitView([e.target], true);
      });
    }

    if (props.setFitView) {
      console.log("setFitView", props.setFitView);
      setTimeout(() => {
        mapBase.map.value.setFitView(savePolygon, true);
      }, 300);
    }
  }

  return {
    savePolygon,
  };
}
