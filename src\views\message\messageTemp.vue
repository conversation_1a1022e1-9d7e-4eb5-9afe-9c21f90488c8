<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 操作按钮开始 -->
      <div class="operation-btns">
        <pl-button type="primary" @click="handleAdd()">新增</pl-button>
      </div>
      <!-- 操作按钮结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="tempTableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <template #templateContent="{ scope }">
            <div class="temp-text">
              {{ scope.row.msgTemplateContent }}
            </div>
          </template>
          <template #status="{ scope }">
            <div class="status-item on" v-if="scope.row.status === 1">启用</div>
            <div class="status-item off" v-else>停用</div>
          </template>
          <template #operation="{ scope }">
            <pl-button
              type="primary"
              link
              @click="handleStatus(scope.row, 1)"
              v-if="scope.row.status == 0"
            >
              启用
            </pl-button>
            <pl-button
              link
              style="color: #f31260"
              @click="handleStatus(scope.row, 0)"
              v-if="scope.row.status == 1"
            >
              停用
            </pl-button>
            <pl-button
              type="primary"
              link
              @click="handleEdit(scope.row)"
              v-if="scope.row.status == 0"
            >
              编辑
            </pl-button>
            <pl-button
              link
              type="danger"
              v-if="scope.row.status == 0"
              @click="handleDelete(scope, 'msgTemplateId')"
            >
              删除
            </pl-button>
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :loading="drawerLoading"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit(event)"
      >
        <template #templateContent>
          <div class="temp-content">
            <pl-input type="textarea" v-model="fromData.msgTemplateContent" />
            <div class="temp-content-notice">
              注:需要根据实际情况变化的字段请用
              <span style="font-weight: 800">${}</span>
              包含在内
            </div>
          </div>
        </template>
        <template #templateVoiceContent>
          <div class="temp-content" v-show="fromData.msgTemplateType === 'APP'">
            <pl-input
              type="textarea"
              v-model="fromData.msgTemplateVoiceContent"
            />
          </div>
        </template>
      </drawerFrom>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { getDictionaryData } from "@/api/dict";
import { tempTableColumns } from "./config";
import {
  addMessageTemplate,
  editMessageTemplate,
  updateTemplateStatus,
} from "@/api";
import { useTable } from "@/hooks/usetTable";
import { pinyin } from "pinyin-pro";
import { plMessage, plMessageBox } from "pls-common";

const templateType = ref([]);
onMounted(() => {
  //组件加载完成后执行事件
  getDictionaryData({
    dictTypeCode: "message_template_type",
  }).then((res) => {
    templateType.value = res;
  });
});

// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  loadData,
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  handleAdd,
  handleEdit,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/messageTemplate/pageMessageTemplate",
  queryForm,
  delete: "/messageTemplate/deleteMessageTemplate/",
  del: {
    message: "确定要删除该模版?",
    type: "message",
  },
});

// 启用/停用模板
const handleStatus = async (row, status) => {
  plMessageBox
    .confirm(`是否${status == 1 ? "启用" : "停用"}该模版?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
    .then(async () => {
      try {
        const res = await updateTemplateStatus(
          `${row.msgTemplateId}/${status}`
        );
        if (res.code == 200) {
          plMessage(`${status == 1 ? "启用" : "停用"}成功`, "success");
          loadData();
        }
      } catch (error) {
        console.log(error);
      }
    })
    .catch(() => {});
};

// 查询表单的列配置
const formColumns = ref([
  {
    label: "模板名称",
    prop: "msgTemplateName",
    type: "input",
    placeholder: "请输入模板名称",
  },
  {
    label: "模板类型",
    prop: "msgTemplateType",
    type: "select",
    placeholder: "请选择模板类型",
    options: templateType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "状态",
    prop: "status",
    type: "select",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: 1 },
      { label: "停用", value: 0 },
    ],
  },
]);

// 动态计算drawerFields，根据收款方式过滤字段
const drawerFields = computed(() => {
  const fields = [...baseDrawerFields.value];
  // 如果收款方式是现金，隐藏交易流水号和支付账户字段
  if (fromData.value.msgTemplateType !== "APP") {
    return fields.filter((field) => field.prop !== "msgTemplateVoiceContent");
  }
  return fields;
});
// 弹窗表单配置
const baseDrawerFields = ref([
  {
    label: "模板名称",
    prop: "msgTemplateName",
    type: "input",
    placeholder: "请输入名称",
    input: (val) => {
      if (val) {
        fromData.value.msgTemplateCode = autoCode(val);
      }
    },
    rules: {
      required: true,
      message: "请输入类型编码",
    },
  },
  {
    label: "模板编码",
    prop: "msgTemplateCode",
    type: "select",
    placeholder: "自动生成，不可编辑",
    disabled: true, // 添加禁用属性
    rules: {
      required: true,
      message: "请输入类型编码",
    },
  },
  {
    label: "模板类型",
    prop: "msgTemplateType",
    type: "select",
    placeholder: "请选择模板类型",
    options: templateType,
    filterable: true,
    rules: {
      required: true,
      message: "请输入类型编码",
    },
    onChange: (val) => {
      if (val !== "APP") {
        fromData.value.msgTemplateVoiceContent = "";
      }
    },
  },
  {
    label: "状态",
    prop: "status",
    type: "radio",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
    rules: [
      {
        required: true,
        message: "请选择状态",
        trigger: "change",
      },
    ],
  },
  {
    label: "模板内容",
    prop: "msgTemplateContent",
    template: "templateContent",
    rules: {
      required: true,
      message: "请输入模板内容",
    },
  },
  {
    label: "播报内容",
    prop: "msgTemplateVoiceContent",
    template: "templateVoiceContent",
  },
]);

/**
 * 处理抽屉提交事件的方法
 */
const drawerLoading = ref(false);
const handleDrawerSubmit = async () => {
  drawerLoading.value = true;
  try {
    const params = {
      ...fromData.value,
      orgId: JSON.parse(localStorage.getItem("userInfo"))?.orgId || 56,
    };
    if (drawerTitle.value == "新增") {
      const res = await addMessageTemplate(params);
      if (res.code === 200) {
        drawerVisible.value = false;
        plMessage("新增成功", "success");
        loadData();
      }
    } else {
      const editParams = {
        ...params,
        msgTemplateId: fromData.value.msgTemplateId,
      };
      const res = await editMessageTemplate(editParams);
      if (res.code === 200) {
        drawerVisible.value = false;
        plMessage("编辑成功", "success");
        loadData();
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    drawerLoading.value = false;
  }
};

// 自动获取编码
function autoCode(str) {
  if (!str) return "";
  // 使用 pinyin-pro 获取拼音首字母
  const result = pinyin(str, {
    pattern: "first", // 只获取首字母
    toneType: "none", // 不带声调
    type: "array", // 输出数组形式
  });
  return result.join("").toUpperCase();
}
</script>

<style lang="scss" scoped>
.temp-content {
  display: flex;
  width: 100%;
  flex-direction: column;
  .temp-content-notice {
    font-size: 12px;
    margin-top: -5px;
    color: #ff1245;
  }
}

.temp-text {
  font-size: 12px;
  color: #333;
}

.operation-btns {
  justify-content: flex-start;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  width: 48px;
  font-size: 12px;
  font-weight: 500;
}
.on {
  background: rgba(23, 201, 100, 0.1);
  color: #17c964;
}
.off {
  background: rgba(243, 18, 96, 0.1);
  color: #f31260;
}
</style>
