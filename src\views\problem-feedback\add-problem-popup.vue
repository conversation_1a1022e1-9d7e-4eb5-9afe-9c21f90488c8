<!-- 新建问题 -->
<template>
  <div class="add-problem-popup">
    <pl-scrollbar>
      <pl-form
        :fields="formColumns"
        inline
        :span="6"
        :isButtonShow="false"
        :form="formData"
        ref="formRef"
      >
        <template #questionType>
          <pl-dict-select
            dictCode="question_type"
            v-model="formData.questionType"
          ></pl-dict-select>
        </template>
        <template #questionSource>
          <pl-dict-select
            dictCode="question_source"
            v-model="formData.questionSource"
          ></pl-dict-select>
        </template>
      </pl-form>
      <div class="problem-desc">
        <div class="form-title">问题描述</div>
        <textarea
          class="textarea"
          placeholder="请输入问题描述"
          v-model="formData.content"
        ></textarea>
      </div>
      <div class="upload-box">
        <div class="form-title">上传图片</div>
        <pl-upload
          uploadType="image"
          v-model="formData.picturePathss"
        ></pl-upload>
      </div>
    </pl-scrollbar>
    <div class="button-box">
      <pl-button class="btn" @click="handleClose">取消</pl-button>
      <!-- <pl-button class="btn">确定并新增</pl-button> -->
      <pl-button class="btn" type="primary" @click="handleSubmit"
        >确定</pl-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getAppList, getLineList, getLineAllStop } from "@/api";
import { plMessage } from "pls-common";
const formRef = ref(null);
const emit = defineEmits(["close", "submit"]);
const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  formRef.value.confirm((data) => {
    if (!data.content) {
      plMessage("请输入问题描述", "warning");
      return;
    }
    data.picturePath = data.picturePaths ? data.picturePaths.split(",") : [];
    emit("submit", data);
  });
};

const appList = ref([]);
getAppList().then((res) => {
  appList.value = res.data;
});
const formData = ref({});
const lineList = ref([]);
getLineList({
  lineType: "GJLX",
}).then((res) => {
  lineList.value = res.data;
});

const stopList = ref([]);
const formColumns = ref([
  {
    label: "问题来源",
    type: "select",
    prop: "questionSource",
    rules: [{ required: true, message: "请选择问题来源" }],
    template: "questionSource",
  },
  {
    label: "所属应用",
    type: "select",
    options: appList,
    prop: "appCode",
    valueKey: "appCode",
    labelKey: "appName",
  },
  {
    label: "问题类别",
    prop: "questionType",
    template: "questionType",
    rules: [{ required: true, message: "请选择问题类别" }],
  },
  {
    label: "反馈人",
    type: "input",
    prop: "userName",
  },
  {
    label: "反馈电话",
    type: "input",
    prop: "phone",
  },
  {
    label: "线路名称",
    type: "select",
    prop: "lineId",
    options: lineList,
    valueKey: "lineId",
    labelKey: "lineName",
    onChange: (e, s) => {
      formData.value.lineName = s.lineName;
    },
  },
  {
    label: "方向",
    type: "select",
    prop: "direct",
    options: [
      {
        label: "上行",
        value: 0,
      },
      {
        label: "下行",
        value: 1,
      },
    ],
    onChange: () => {
      if (formData.value.lineId) {
        getLineAllStop({
          direct: formData.value.direct,
          lineId: formData.value.lineId,
        }).then((res) => {
          stopList.value = res;
        });
      }
    },
  },
  {
    label: "站点名称",
    type: "select",
    prop: "stopId",
    options: stopList,
    valueKey: "stopId",
    labelKey: "stopName",
    onChange: (e, s) => {
      formData.value.stopName = s.stopName;
    },
  },
]);
</script>

<style lang="scss" scoped>
.add-problem-popup {
  padding-bottom: 50px;
  height: 100%;
}
.form-title {
  font-size: 14px;
  color: var(--el-text-color-regular);
  height: 32px;
  line-height: 32px;
}
.problem-desc {
  margin-top: -20px;

  .textarea {
    width: 100%;
    height: 200px;
    padding: 10px;
    border: 1px solid var(--el-border-color-light);
    outline: none;
  }
}
</style>
