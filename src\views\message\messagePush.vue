<template>
  <pl-card>
    <div class="card-flex">
      <!-- 查询表单开始 -->
      <pl-form
        confirmButtonText="搜索"
        cancelButtonText="重置"
        :fields="formColumns"
        :form="queryForm"
        inline
        :span="6"
        clear
        @confirm="handleSearch"
        @cancel="handleCancel"
      >
      </pl-form>
      <!-- 查询表单结束 -->

      <!-- 表格展示区域开始 -->
      <div class="card-table mt20">
        <pl-table
          :columns="pushTableColumns"
          :data="tableData"
          class="table"
          v-loading="tabLoading"
          @sort-change="handleSort"
        >
          <template #status="{ scope }">
            <div class="status-item on" v-if="scope.row.status === 1">启用</div>
            <div class="status-item off" v-else>停用</div>
          </template>
          <template #operation="{ scope }">
            <pl-button type="primary" link @click="handleEdit(scope.row)"
              >编辑</pl-button
            >
            <pl-button
              link
              type="danger"
              @click="handleDelete(scope.row, '设置成需要删除的id名字')"
              >删除</pl-button
            >
          </template>
        </pl-table>
      </div>
      <!-- 表格展示区域结束 -->

      <!-- 分页组件开始 -->
      <pl-pagination
        :currentPage="current"
        :total="dataTotal"
        @size-change="sizeChange"
        @current-change="currentChange"
      ></pl-pagination>
      <!-- 分页组件结束 -->

      <!-- 新增/编辑弹窗 -->
      <drawerFrom
        :fields="drawerFields"
        v-model="drawerVisible"
        :title="drawerTitle"
        :form="fromData"
        :disabled="formDisabled"
        @submit="handleDrawerSubmit(event)"
      >
      </drawerFrom>
    </div>
  </pl-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import drawerFrom from "@/components/module/drawer-from.vue";
import { getDictionaryData } from "@/api/dict";
import { useTable } from "@/hooks/usetTable";
import { pushTableColumns } from "./config";

const templateType = ref([]);
onMounted(() => {
  //组件加载完成后执行事件
  getDictionaryData({
    dictTypeCode: "message_template_type",
  }).then((res) => {
    templateType.value = res;
  });
});

// 查询表单的数据
const queryForm = ref({});

// 使用 useTable 钩子管理表格相关逻辑
const {
  tabLoading,
  dataTotal,
  drawerVisible,
  drawerTitle,
  formDisabled,
  tableData,
  fromData,
  current,
  handleEdit,
  handleDelete,
  handleSort,
  handleCancel,
  handleSearch,
  sizeChange,
  currentChange,
} = useTable({
  list: "/messagePush/pageMessagePush",
  queryForm,
});

// 查询表单的列配置
const formColumns = ref([
  {
    label: "模板名称",
    prop: "msgTemplateName",
    type: "input",
    placeholder: "请输入模板名称",
  },
  {
    label: "模板类型",
    prop: "msgTemplateType",
    type: "select",
    placeholder: "请选择模板类型",
    options: templateType,
    multiple: true,
    filterable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    maxCollapseTags: 1,
  },
  {
    label: "状态",
    prop: "status",
    type: "select",
    placeholder: "请选择状态",
    options: [
      { label: "启用", value: 1 },
      { label: "停用", value: 0 },
    ],
  },
]);

// 弹窗表单配置
const drawerFields = ref([
  {
    label: "名称",
    prop: "name",
    type: "input",
    placeholder: "请输入名称",
  },
]);

/**
 * 处理抽屉提交事件的方法
 * @param {Event} e - 提交事件对象
 */
const handleDrawerSubmit = (e) => {
  // 输出提交事件对象到控制台，用于调试目的
  console.log(e);
};
</script>

<style lang="scss" scoped>
.status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  width: 48px;
  font-size: 12px;
  font-weight: 500;
}
.on {
  background: rgba(23, 201, 100, 0.1);
  color: #17c964;
}
.off {
  background: rgba(243, 18, 96, 0.1);
  color: #f31260;
}
</style>
