<template>
  <pl-label-select
    :options="options"
    v-model="selectVal"
    :cascaderProps="cascaderProps"
  ></pl-label-select>
</template>

<script setup>
import { ref } from "vue";
import { getLabelList } from "@/api/index";

const props = defineProps({
  code: {
    type: String,
    default: "",
  },
});

const cascaderProps = {
  label: "labelName",
  value: "labelId",
};

const options = ref([]);
getLabelList({
  categoryCode: props.code,
}).then((res) => {
  options.value = res.data;
  console.log(options.value);
});

const selectVal = ref([]);
</script>

<style lang="scss" scoped></style>
