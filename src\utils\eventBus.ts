// src/eventBus.ts
import { ref } from "vue";

type EventHandler = (...args: any[]) => void;
const eventBus = ref(new Map<string, EventHandler[]>());

export function emit(event: string, ...args: any[]) {
  const handlers = eventBus.value.get(event);
  if (handlers) {
    handlers.forEach((handler) => handler(...args));
  }
}

export function on(event: string, handler: EventHandler) {
  if (!eventBus.value.has(event)) {
    eventBus.value.set(event, []);
  }
  eventBus.value.get(event)!.push(handler);
}

export function off(event: string, handler: EventHandler) {
  const handlers = eventBus.value.get(event);
  if (handlers) {
    eventBus.value.set(
      event,
      handlers.filter((h) => h !== handler)
    );
  }
}
