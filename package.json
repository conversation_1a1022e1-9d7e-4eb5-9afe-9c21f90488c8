{"name": "pls-mocs-web", "private": false, "version": "0.0.0", "type": "module", "scripts": {"commit": "git-cz", "dev": "vite", "build:prod": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "prepare": "husky install", "add": "node updateVersion.js && git add .", "commitlint": "commitlint --config commitlint.config.cjs -e -V"}, "lint-staged": {"**/*.{js,vue,ts}": "eslint --fix"}, "dependencies": {"@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.7", "echarts": "^5.6.0", "element-plus": "^2.8.4", "fs": "^0.0.1-security", "path": "^0.12.7", "pinia": "^2.2.2", "pinia-plugin-persist": "^1.0.0", "pinyin-pro": "^3.26.0", "pls-common": "^2.0.1", "screenfull": "^6.0.2", "terser": "^5.37.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.4.37", "vue-router": "^4.4.3", "vue-virtual-scroller": "^2.0.0-beta.8", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.10.0", "@types/conventional-commits-parser": "^5.0.1", "@types/node": "^22.5.0", "@vitejs/plugin-vue": "^5.1.2", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "cz-conventional-changelog-zh": "^0.0.2", "cz-customizable": "^7.2.1", "eslint": "^9.10.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vue": "^9.28.0", "git-cz": "^4.9.0", "globals": "^15.11.0", "husky": "^8.0.3", "lint-staged": "^15.2.10", "pre-commit": "^1.2.2", "typescript": "^5.5.3", "typescript-eslint": "^8.5.0", "vite": "^5.4.1", "vue-tsc": "^2.0.29"}, "config": {"commitizen": {"path": "cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}}