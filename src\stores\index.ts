import { defineStore } from "pinia";
import { User } from "@/types/user";
/**
 * 使用pinia定义一个名为`navtags`的store。
 *
 * 该store用于管理导航标签的状态，包括添加和删除导航标签。
 */
export const useNavtagsStore = defineStore("navtags", {
  /**
   * 状态初始化函数，用于定义store的初始状态。
   *
   * @returns {Object} 返回一个包含`navtags`属性的对象，`navtags`是一个空数组，用于存储导航标签。
   */
  state: () => ({
    navtags: [] as any[],
  }),
  /**
   * 定义store的actions，用于处理变化。
   *
   * 包含`addTag`和`removeTag`两个操作，分别用于添加和删除导航标签。
   */
  actions: {
    /**
     * 添加导航标签。
     *
     * 根据`tag`对象中的`name`属性检查导航标签数组中是否存在相同的标签，
     * 若不存在则将标签添加到数组中。
     *
     * @param {Object} tag 待添加的导航标签，应为一个包含`name`属性的对象。
     */
    addTag(tag: any) {
      if (tag.path == "/dashboard") return;
      const exists = this.navtags.some((t) => t.meta.title === tag.meta.title);
      if (!exists) {
        this.navtags.push(tag);
      }
    },
    /**
     * 删除导航标签。
     *
     * 根据`tag`对象中的`name`属性从导航标签数组中移除对应的标签。
     *
     * @param {Object} tag 待删除的导航标签，应为一个包含`name`属性的对象。
     */
    removeTag(tag: any) {
      this.navtags = this.navtags.filter(
        (t) => t.meta.title !== tag.meta.title
      );
    },
  },
});

export const useUserInfo = defineStore("userInfo", {
  state: () => {
    return {
      user: {} as User,
    };
  },
  persist: {
    enabled: true,
  },
  actions: {
    setUser(user: any) {
      this.user = user;
    },
  },
});
