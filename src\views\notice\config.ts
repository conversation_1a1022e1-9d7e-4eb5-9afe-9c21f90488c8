import { ref } from "vue";
//弹窗表单字段
export const drawerFields = ref([
  {
    label: "标题",
    type: "input",
    rules: { required: true, message: "请输入标题", trigger: "change" },
  },
  {
    label: "所属应用",
    type: "select",
    options: [],
    rules: { required: true, message: "请选择所属应用", trigger: "change" },
  },
  {
    label: "接收点",
    type: "select",
    options: [],
    rules: {
      required: true,
      message: "请选择接收点",
      trigger: "change",
    },
  },
  {
    label: "接收人员",
    message: "请选择接收人员(多选)",
    type: "select",
    options: [],
    rules: {
      required: true,
      message: "请选择接接收人员",
      trigger: "change",
    },
  },
  {
    label: "附件",
    message: "附件",
    type: "input",
  },
]);

// 表格字段
export const tableColumns = ref([
  {
    label: "序号",
  },
  {
    label: "标题",
  },
  {
    label: "发布网点",
  },
  {
    label: "发布人",
  },
  {
    label: "所属应用",
  },
  {
    label: "接收点",
  },
  {
    label: "接收人员",
  },
  {
    label: "附件",
  },
  {
    label: "发布时间",
  },
  {
    label: "操作",
    template: "operate",
  },
]);

// 表单字段
export const columns = ref([
  {
    label: "发布人",
    type: "input",
    placeholder: "请输入发布人",
  },
  {
    label: "所属应用",
    type: "select",
    placeholder: "请选择所属应用",
    options: [],
  },
  {
    label: "接收点",
    type: "select",
    placeholder: "请选择接收点",
    options: [],
  },
  {
    label: "接收人员",
    type: "select",
    placeholder: "请选择接收人员",
    options: [
      {
        label: "启用",
        value: 1,
      },
      {
        label: "停用",
        value: 0,
      },
    ],
  },
]);
