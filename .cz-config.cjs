/* global module */
module.exports = {
  types: [
    { value: "feat", name: "feat:🆕      新增功能" },
    { value: "fix", name: "fix:🚑      修复bug" },
    { value: "docs", name: "docs:🍎     文档更新" },
    {
      value: "style",
      name: "style:🔧    代码格式（不影响功能，比如空格、分号等格式修正）",
    },
    {
      value: "refactor",
      name: "refactor:⚡️ 代码重构（不包括 bug 修复和新增功能）",
    },
    { value: "test", name: "test:🎬     增加测试" },
    { value: "chore", name: "chore:🔨    构建过程或辅助工具的变动" },
    { value: "revert", name: "revert:🚧   回滚到上一个版本" },
  ],
  messages: {
    type: "选择提交类型:",
    scope: "更改的范围 (可选):",
    subject: "简要说明 (必填):\n",
    body: '详细描述，使用 "|" 换行 (可选):\n',
    footer: "关闭的 issue (例如: #31, #34) (可选):\n",
    confirmCommit: "确定提交吗?",
    breaking: "列出任何重大变更（可选）",
  },
  allowCustomScopes: true,
  allowBreakingChanges: ["feat", "fix"],
};
